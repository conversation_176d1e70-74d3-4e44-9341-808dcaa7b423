# JSON File Safety

## The Problem

We've identified an issue with how JSON files are being written in various parts of the codebase. 

When a file is opened with `open(file_path, 'w')`, Python immediately truncates the file (deletes all content), 
and then the writing operation begins. If any error occurs between truncating and completing the write, 
such as a crash or exception, the file will be left empty or in a corrupted state.

## The Solution

We've implemented a safer approach in `src/lib/json_safety.py` that uses atomic operations to prevent data loss:

1. First write to a temporary file
2. If the write is successful, create a backup of the original file if it exists
3. Replace the original file with the temporary file
4. If there's an error reading a file, check if there's a backup and recover from it

## Fixed Files

So far, we've updated the following files to use the safer approach:

- `src/scripts/json_utilities.py`

## How to Use the Safe JSON Functions

Replace code like this:

```python
with open(file_path, 'w') as file:
    json.dump(data, file, indent=4)
```

With the safer approach:

```python
from src.lib.json_safety import safe_json_write, safe_json_read

if safe_json_write(file_path, data, indent=4):
    print("File updated successfully")
else:
    print(f"Error updating file {file_path}")
```

And for reading:

```python
data = safe_json_read(file_path)
if data is None:
    print(f"Error reading file {file_path}")
    # Handle the error
```

## Files to Update

The following files still use potentially unsafe JSON operations and should be updated:

```
/Users/<USER>/PycharmProjects/lexgenius/src/lib/post_processing3.py
/Users/<USER>/PycharmProjects/lexgenius/src/lib/pacer_client.py
/Users/<USER>/PycharmProjects/lexgenius/src/lib/post_processing2.py
/Users/<USER>/PycharmProjects/lexgenius/src/lib/fb_ads.py
/Users/<USER>/PycharmProjects/lexgenius/src/lib/mistral_ocr.py
/Users/<USER>/PycharmProjects/lexgenius/src/lib/pacer_client4.py
/Users/<USER>/PycharmProjects/lexgenius/src/lib/fb_archive_manager.py
/Users/<USER>/PycharmProjects/lexgenius/src/lib/pacer_client3.py
...
```

## Why This Fix Works

The approach ensures that:

1. The original file is never directly modified in-place, preventing truncation risks
2. A backup is always created before replacing the original file
3. The replacement operation is atomic (it either completely succeeds or completely fails)
4. If a read fails, we automatically check for and try to recover from backups