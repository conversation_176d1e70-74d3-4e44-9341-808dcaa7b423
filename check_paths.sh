#!/bin/bash

# Prevent glob pattern from causing errors when no matches are found
setopt +o nomatch 2>/dev/null || true  # For zsh
shopt -s nullglob 2>/dev/null || true  # For bash

echo "Directory exists:"
test -d data/20250327 && echo "Yes" || echo "No"

echo "\nDirectory contents (including hidden files):"
ls -la data/20250327

echo "\nDirectory permissions:"
ls -ld data/20250327

echo "\nParent directory structure:"
ls -la data
