#!/usr/bin/env python3
"""
Compares records between local DynamoDB 'Pacer' and 'PacerDockets' tables.
Identifies records unique to each table based on (CourtId, DocketNum)
and saves differences to 'comparison_results.json'.
"""

import argparse
import decimal
import json
import logging
import sys
import time
from collections import defaultdict
from typing import Dict, Any, Set, Tuple, List, Optional

import boto3
from botocore.exceptions import ClientError, NoCredentialsError, PartialCredentialsError

try:
    from rich.console import Console
    from rich.logging import RichHandler
    from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn
    from rich.table import Table
    from rich.syntax import Syntax
    from rich.panel import Panel
except ImportError:
    print("Rich library is not installed. Please install it with 'pip install rich'.")
    sys.exit(1)

# --- Configuration ---
DYNAMODB_ENDPOINT_URL = "http://localhost:8000"  # Default local DynamoDB endpoint
PACER_TABLE_NAME = "Pacer"
PACER_DOCKETS_TABLE_NAME = "PacerDockets"
JSON_OUTPUT_FILE = "pacer_comparison_results.json"

# --- Setup ---
console = Console()
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(console=console, rich_tracebacks=True, show_path=False)]
)
logger = logging.getLogger("dynamodb_comparator")


# --- Helper Functions ---
class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, decimal.Decimal):
            if o % 1 == 0:
                return int(o)
            else:
                return float(o)
        return super(DecimalEncoder, self).default(o)


def replace_decimals(obj: Any) -> Any:
    """Recursively convert Decimal instances in dicts/lists to int/float."""
    if isinstance(obj, list):
        return [replace_decimals(i) for i in obj]
    elif isinstance(obj, dict):
        return {k: replace_decimals(v) for k, v in obj.items()}
    elif isinstance(obj, decimal.Decimal):
        if obj % 1 == 0:
            return int(obj)
        else:
            return float(obj)
    return obj


def scan_table_and_extract_data(
        dynamodb_resource: Any,
        table_name: str,
        progress: Progress
) -> Tuple[Set[Tuple[str, str]], Dict[Tuple[str, str], List[Dict[str, Any]]], List[Dict[str, Any]], int]:
    """
    Scans a DynamoDB table, extracts comparison keys and item data.
    Comparison key is (CourtId, DocketNum).
    """
    comparison_keys: Set[Tuple[str, str]] = set()
    # Maps comparison_key -> list of items (because Pacer might have multiple FilingDates for same CourtId/DocketNum)
    items_by_comparison_key: Dict[Tuple[str, str], List[Dict[str, Any]]] = defaultdict(list)
    items_with_missing_keys: List[Dict[str, Any]] = []
    total_records_scanned = 0

    logger.info(f"Scanning table: [cyan]{table_name}[/cyan]...")
    task_id = progress.add_task(f"Scanning [bold]{table_name}[/bold]", total=None)  # Total unknown initially

    try:
        table = dynamodb_resource.Table(table_name)
        # Get approximate item count for progress bar
        try:
            table.load()  # Check if table exists
            item_count = table.item_count
            progress.update(task_id, total=item_count,
                            description=f"Scanning [bold]{table_name}[/bold] (~{item_count} items)")
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                logger.error(f"Table [bold red]{table_name}[/bold red] not found.")
                progress.remove_task(task_id)
                return comparison_keys, items_by_comparison_key, items_with_missing_keys, total_records_scanned
            logger.warning(f"Could not get item count for {table_name}. Progress total will be approximate. Error: {e}")
            progress.update(task_id, description=f"Scanning [bold]{table_name}[/bold]")

        response = table.scan()
        items = response.get('Items', [])
        total_records_scanned += len(items)
        if item_count:  # Update progress only if we have a total
            progress.update(task_id, advance=len(items))

        for item_raw in items:
            item = replace_decimals(item_raw)
            court_id = str(item.get('CourtId', '')).strip()
            docket_num = str(item.get('DocketNum', '')).strip()

            if court_id and docket_num:
                comp_key = (court_id, docket_num)
                comparison_keys.add(comp_key)
                items_by_comparison_key[comp_key].append(item)
            else:
                # Store minimal info about items with missing keys for reporting
                pk_info = {}
                if table_name == PACER_TABLE_NAME:
                    pk_info = {"FilingDate": item.get("FilingDate"),
                               "DocketNum": item.get("DocketNum_orig", item.get("DocketNum"))}
                elif table_name == PACER_DOCKETS_TABLE_NAME:
                    pk_info = {"CourtId": item.get("CourtId"), "DocketNum": item.get("DocketNum")}
                items_with_missing_keys.append({
                    "table_pk": pk_info,
                    "reason": f"Missing CourtId ('{court_id}') or DocketNum ('{docket_num}')"
                })

        while 'LastEvaluatedKey' in response:
            response = table.scan(ExclusiveStartKey=response['LastEvaluatedKey'])
            new_items = response.get('Items', [])
            total_records_scanned += len(new_items)
            if item_count:  # Update progress only if we have a total
                progress.update(task_id, advance=len(new_items))

            for item_raw in new_items:
                item = replace_decimals(item_raw)
                court_id = str(item.get('CourtId', '')).strip()
                docket_num = str(item.get('DocketNum', '')).strip()
                if court_id and docket_num:
                    comp_key = (court_id, docket_num)
                    comparison_keys.add(comp_key)
                    items_by_comparison_key[comp_key].append(item)
                else:
                    pk_info = {}
                    if table_name == PACER_TABLE_NAME:
                        pk_info = {"FilingDate": item.get("FilingDate"),
                                   "DocketNum": item.get("DocketNum_orig", item.get("DocketNum"))}
                    elif table_name == PACER_DOCKETS_TABLE_NAME:
                        pk_info = {"CourtId": item.get("CourtId"), "DocketNum": item.get("DocketNum")}
                    items_with_missing_keys.append({
                        "table_pk": pk_info,
                        "reason": f"Missing CourtId ('{court_id}') or DocketNum ('{docket_num}')"
                    })

        progress.update(task_id, completed=total_records_scanned if item_count else None,
                        description=f"Finished [bold]{table_name}[/bold]")

    except ClientError as e:
        logger.error(f"DynamoDB client error scanning {table_name}: {e}")
        progress.update(task_id, description=f"Error scanning [bold]{table_name}[/bold]")
    except Exception as e:
        logger.error(f"Unexpected error scanning {table_name}: {e}", exc_info=True)
        progress.update(task_id, description=f"Error scanning [bold]{table_name}[/bold]")

    logger.info(
        f"Finished scanning [cyan]{table_name}[/cyan]. Found {len(comparison_keys)} unique comparison keys from {total_records_scanned} records.")
    if items_with_missing_keys:
        logger.warning(
            f"Found {len(items_with_missing_keys)} records in [cyan]{table_name}[/cyan] with missing CourtId or DocketNum.")

    return comparison_keys, items_by_comparison_key, items_with_missing_keys, total_records_scanned


def format_results_for_json(
        comparison_key_tuple: Tuple[str, str],
        records_list: List[Dict[str, Any]],
        source_table_name: str
) -> Dict[str, Any]:
    """Formats a list of records for a given comparison key into the JSON structure."""
    formatted_records = []
    for record in records_list:
        if source_table_name == PACER_TABLE_NAME:
            # For Pacer, primary key is FilingDate, DocketNum
            # We want to report these, plus the CourtId for clarity
            formatted_records.append({
                "Pacer_PK_FilingDate": record.get("FilingDate"),
                "Pacer_PK_DocketNum": record.get("DocketNum"),  # This is the main DocketNum
                "CourtId_in_Pacer": record.get("CourtId"),  # This is the CourtId used in comparison
                # Optionally include other fields for context. e.g. Title
                "Title": record.get("Title"),
                "FullItemSample": {k: v for k, v in record.items() if
                                   k in ["FilingDate", "DocketNum", "CourtId", "Title", "MdlNum", "LawFirm", "AddedOn"]}
                # Sample subset
            })
        elif source_table_name == PACER_DOCKETS_TABLE_NAME:
            # For PacerDockets, primary key is CourtId, DocketNum
            formatted_records.append({
                "PacerDockets_PK_CourtId": record.get("CourtId"),
                "PacerDockets_PK_DocketNum": record.get("DocketNum"),
                "FilingDate_in_PacerDockets": record.get("FilingDate"),
                # Optionally include other fields
                "Title": record.get("Title"),
                "FullItemSample": {k: v for k, v in record.items() if
                                   k in ["FilingDate", "DocketNum", "CourtId", "Title", "MdlNum", "LawFirm", "AddedOn"]}
                # Sample subset
            })
    return {
        "comparison_key": {"CourtId": comparison_key_tuple[0], "DocketNum": comparison_key_tuple[1]},
        f"{source_table_name.lower()}_records": formatted_records
    }


def main(endpoint_url: str):
    console.print(Panel(
        f"DynamoDB Local Table Comparator\nTarget Tables: [cyan]{PACER_TABLE_NAME}[/cyan] and [cyan]{PACER_DOCKETS_TABLE_NAME}[/cyan]\nEndpoint: [yellow]{endpoint_url}[/yellow]",
        title="[bold green]Script Start[/bold green]", expand=False))

    try:
        dynamodb = boto3.resource('dynamodb', endpoint_url=endpoint_url, region_name='us-east-1',
                                  aws_access_key_id='dummy', aws_secret_access_key='dummy')  # Dummy creds for local
        # Test connection
        dynamodb.meta.client.list_tables()
        logger.info(f"Successfully connected to DynamoDB at [yellow]{endpoint_url}[/yellow]")
    except (NoCredentialsError, PartialCredentialsError):
        logger.error(
            "AWS credentials not found or incomplete. For local DynamoDB, dummy credentials are used by default if endpoint_url is specified.")
        console.print(
            "[bold red]Credential Error. Ensure your AWS CLI is configured or provide dummy credentials for local execution.[/bold red]")
        return
    except Exception as e:  # Catch other connection errors
        logger.error(f"Failed to connect to DynamoDB at [yellow]{endpoint_url}[/yellow]. Error: {e}", exc_info=True)
        console.print(f"[bold red]DynamoDB Connection Error. Is DynamoDB Local running at {endpoint_url}?[/bold red]")
        return

    results = {
        "summary": {},
        "in_pacer_only": [],
        "in_pacer_dockets_only": [],
        "records_with_missing_comparison_keys": {
            "pacer": [],
            "pacer_dockets": []
        }
    }

    with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn(
                "({task.completed} of {task.total} items)" if "[progress.percentage]" else "[magenta]{task.completed} items[/magenta]"),
            # Conditional percentage
            TimeElapsedColumn(),
            console=console,
            transient=False  # Keep progress visible after completion
    ) as progress:
        logger.info("--- Starting Data Extraction ---")
        pacer_keys, pacer_items_map, pacer_missing_comp_keys, pacer_total_scanned = \
            scan_table_and_extract_data(dynamodb, PACER_TABLE_NAME, progress)

        pacer_dockets_keys, pacer_dockets_items_map, pacer_dockets_missing_comp_keys, pacer_dockets_total_scanned = \
            scan_table_and_extract_data(dynamodb, PACER_DOCKETS_TABLE_NAME, progress)

    results["summary"]["pacer_total_records_scanned"] = pacer_total_scanned
    results["summary"]["pacer_dockets_total_records_scanned"] = pacer_dockets_total_scanned
    results["summary"]["pacer_unique_comparison_keys"] = len(pacer_keys)
    results["summary"]["pacer_dockets_unique_comparison_keys"] = len(pacer_dockets_keys)
    results["records_with_missing_comparison_keys"]["pacer"] = pacer_missing_comp_keys
    results["records_with_missing_comparison_keys"]["pacer_dockets"] = pacer_dockets_missing_comp_keys

    logger.info("--- Comparing Key Sets ---")
    in_pacer_only_keys = pacer_keys - pacer_dockets_keys
    in_pacer_dockets_only_keys = pacer_dockets_keys - pacer_keys
    common_keys = pacer_keys.intersection(pacer_dockets_keys)

    results["summary"]["in_pacer_only_count"] = len(in_pacer_only_keys)
    results["summary"]["in_pacer_dockets_only_count"] = len(in_pacer_dockets_only_keys)
    results["summary"]["common_comparison_keys_count"] = len(common_keys)

    # --- Populate JSON results ---
    for key_tuple in in_pacer_only_keys:
        records = pacer_items_map.get(key_tuple, [])
        results["in_pacer_only"].append(format_results_for_json(key_tuple, records, PACER_TABLE_NAME))

    for key_tuple in in_pacer_dockets_only_keys:
        records = pacer_dockets_items_map.get(key_tuple, [])  # Should be one record as PK is (CourtId, DocketNum)
        results["in_pacer_dockets_only"].append(format_results_for_json(key_tuple, records, PACER_DOCKETS_TABLE_NAME))

    # --- Console Output ---
    console.rule("[bold green]Comparison Summary[/bold green]")
    summary_table = Table(show_header=True, header_style="bold magenta", title="Scan and Comparison Statistics")
    summary_table.add_column("Metric", style="cyan")
    summary_table.add_column("Count", style="green", justify="right")
    summary_table.add_row(f"Total Records Scanned from [cyan]{PACER_TABLE_NAME}[/cyan]", f"{pacer_total_scanned:,}")
    summary_table.add_row(f"Total Records Scanned from [cyan]{PACER_DOCKETS_TABLE_NAME}[/cyan]",
                          f"{pacer_dockets_total_scanned:,}")
    summary_table.add_row(f"Unique Comparison Keys in [cyan]{PACER_TABLE_NAME}[/cyan]", f"{len(pacer_keys):,}")
    summary_table.add_row(f"Unique Comparison Keys in [cyan]{PACER_DOCKETS_TABLE_NAME}[/cyan]",
                          f"{len(pacer_dockets_keys):,}")
    summary_table.add_row(f"Records in [cyan]{PACER_TABLE_NAME}[/cyan] ONLY",
                          f"[bold yellow]{len(in_pacer_only_keys):,}[/bold yellow]")
    summary_table.add_row(f"Records in [cyan]{PACER_DOCKETS_TABLE_NAME}[/cyan] ONLY",
                          f"[bold yellow]{len(in_pacer_dockets_only_keys):,}[/bold yellow]")
    summary_table.add_row("Common Comparison Keys", f"{len(common_keys):,}")
    if pacer_missing_comp_keys:
        summary_table.add_row(f"Records in [cyan]{PACER_TABLE_NAME}[/cyan] missing comparison key fields",
                              f"[bold red]{len(pacer_missing_comp_keys):,}[/bold red]")
    if pacer_dockets_missing_comp_keys:
        summary_table.add_row(f"Records in [cyan]{PACER_DOCKETS_TABLE_NAME}[/cyan] missing comparison key fields",
                              f"[bold red]{len(pacer_dockets_missing_comp_keys):,}[/bold red]")
    console.print(summary_table)

    # Display details of differing items (limited for console brevity)
    max_display = 5
    if in_pacer_only_keys:
        console.rule(
            f"[bold yellow]Records in {PACER_TABLE_NAME} ONLY (Comparison Key: CourtId, DocketNum) - Showing first {max_display}[/bold yellow]")
        pacer_only_table = Table(title=f"Top {max_display} in {PACER_TABLE_NAME} Only")
        pacer_only_table.add_column("Comp. CourtId", style="magenta")
        pacer_only_table.add_column("Comp. DocketNum", style="magenta")
        pacer_only_table.add_column(f"{PACER_TABLE_NAME} PKs (FilingDate:DocketNum)", style="cyan")

        for i, key_tuple in enumerate(list(in_pacer_only_keys)[:max_display]):
            pacer_records_info = []
            for p_item in pacer_items_map.get(key_tuple, []):
                pacer_records_info.append(f"{p_item.get('FilingDate', 'N/A')}:{p_item.get('DocketNum', 'N/A')}")
            pacer_only_table.add_row(key_tuple[0], key_tuple[1], ", ".join(pacer_records_info))
        console.print(pacer_only_table)

    if in_pacer_dockets_only_keys:
        console.rule(
            f"[bold yellow]Records in {PACER_DOCKETS_TABLE_NAME} ONLY (Comparison Key: CourtId, DocketNum) - Showing first {max_display}[/bold yellow]")
        pd_only_table = Table(title=f"Top {max_display} in {PACER_DOCKETS_TABLE_NAME} Only")
        pd_only_table.add_column("Comp. CourtId", style="magenta")
        pd_only_table.add_column("Comp. DocketNum", style="magenta")
        pd_only_table.add_column(f"{PACER_DOCKETS_TABLE_NAME} PKs (CourtId:DocketNum)",
                                 style="cyan")  # PK is CourtId, DocketNum

        for i, key_tuple in enumerate(list(in_pacer_dockets_only_keys)[:max_display]):
            # For PacerDockets, (CourtId, DocketNum) is the PK, so list should contain one item.
            # We already have the key_tuple for PK.
            pd_records_info = [f"{key_tuple[0]}:{key_tuple[1]}"]
            pd_only_table.add_row(key_tuple[0], key_tuple[1], ", ".join(pd_records_info))
        console.print(pd_only_table)

    # --- Save to JSON ---
    try:
        with open(JSON_OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(results, f, cls=DecimalEncoder, indent=2)
        logger.info(f"Comparison results saved to [green]{JSON_OUTPUT_FILE}[/green]")

        # Optionally print a snippet of the JSON
        with open(JSON_OUTPUT_FILE, 'r', encoding='utf-8') as f_read:
            snippet = "".join(f_read.readlines()[:30])  # First 30 lines
        console.print(Panel(Syntax(snippet, "json", theme="native", line_numbers=True),
                            title=f"Snippet from [green]{JSON_OUTPUT_FILE}[/green]", expand=False))

    except IOError as e:
        logger.error(f"Failed to write results to {JSON_OUTPUT_FILE}: {e}", exc_info=True)
    except Exception as e:
        logger.error(f"An unexpected error occurred while saving JSON: {e}", exc_info=True)

    console.print(Panel("[bold green]Comparison Script Finished[/bold green]", expand=False))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description=f"Compare local DynamoDB tables '{PACER_TABLE_NAME}' and '{PACER_DOCKETS_TABLE_NAME}'.")
    parser.add_argument(
        "--endpoint-url",
        default=DYNAMODB_ENDPOINT_URL,
        help=f"Local DynamoDB endpoint URL (default: {DYNAMODB_ENDPOINT_URL})"
    )
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Enable verbose logging (DEBUG level)."
    )
    args = parser.parse_args()

    if args.verbose:
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:  # Also set handler level if necessary
            handler.setLevel(logging.DEBUG)
        logger.debug("Verbose logging enabled.")

    # Silence boto3/botocore unless verbose
    if not args.verbose:
        logging.getLogger("boto3").setLevel(logging.WARNING)
        logging.getLogger("botocore").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)

    start_time = time.perf_counter()
    main(args.endpoint_url)
    end_time = time.perf_counter()
    logger.info(f"Script execution time: {end_time - start_time:.2f} seconds.")