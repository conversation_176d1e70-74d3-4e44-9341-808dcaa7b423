#!/usr/bin/env python3
"""
Complete fix for fb_ads3.py to work with --add and mobile proxy
"""
import os
import re
import shutil

# Path to the fb_ads3.py file
FB_ADS_PATH = os.path.join(os.getcwd(), 'src', 'lib', 'fb_ads3.py')
BACKUP_PATH = FB_ADS_PATH + '.complete_fix.bak'

# Create backup
shutil.copy2(FB_ADS_PATH, BACKUP_PATH)
print(f"Created backup at {BACKUP_PATH}")

# Read the file
with open(FB_ADS_PATH, 'r') as f:
    content = f.read()

# Replace all problematic regex patterns with safe alternatives
content = content.replace('["\']', '[\'"]')
content = content.replace('[\"\']', '[\'"]')
content = content.replace('["\\\'"]', '[\'"]')
content = content.replace('["\\\']', '[\'"]')
content = content.replace('[\\"\']', '[\'"]')

# Write back the fixed content
with open(FB_ADS_PATH, 'w') as f:
    f.write(content)

print("\nFixed all remaining regex issues in fb_ads3.py")
print("Try running your script again with:")
print("python -m src.lib.fb_ads3 --add --mobile-proxy")