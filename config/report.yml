# YAML configuration for generate_report step
step_name: "generate_report"
input_source: "data/" # Example, adjust if needed
output_location: "data/" # Example, adjust if needed

# Date Configuration
date: '05/23/25' # MM/DD/YY format, matches main.py params
start_date: null # MM/DD/YY format or null
end_date: null   # MM/DD/YY format or null

# LLM Configuration
llm_provider: 'deepseek'

# Upload Configuration
upload: True
upload_types:
  - 's3'
  - 'dynamodb'
force_upload: True

# Ensure force_s3_upload is also set for data transformer compatibility
force_s3_upload: True

# Number of workers
num_workers: 16

# Report Generator Configuration (from main.py params)
report_generator: True
skip_ads: True
skip_invalidate: False
weekly: False

# https://ecf.flnd.uscourts.gov/cgi-bin/show_multidocs.pl?caseid=531455&arr_de_seq_nums=9&magic_num=&pdf_header=1&hdr=&psf_report=&pdf_toggle_possible=1&exclude_attachments=13008076&zipit=1