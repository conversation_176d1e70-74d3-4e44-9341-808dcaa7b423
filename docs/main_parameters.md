# Documentation for Post-Processing Settings in `main2.py`

These settings in the `params` dictionary of `main2.py` provide fine-grained control over the post-processing stage of the legal docket processing pipeline. They allow you to customize which files are processed, how they are processed, and where the results are uploaded. Understanding these settings is crucial for efficiently managing and debugging your data processing workflow.

Here's a detailed breakdown of each post-processing parameter:

### `post_process`

**Type:** `Boolean`

**Purpose:**  Enables or disables the core **post-processing workflow** which includes data enrichment, analysis, and preparation for upload.  Note that setting `upload = True` will still trigger the upload stage regardless of the `post_process` setting.

**Possible Values:**
*   `True`:  Post-processing is **enabled**. The `MainProcessor` will initialize and run the `PostProcessorV2`, triggering data enrichment and preparation steps before potentially uploading data (if `upload` is also `True`).
*   `False`: Post-processing is **disabled**. The `MainProcessor` will skip the data enrichment and preparation stages. However, if `upload` is set to `True`, it will still enter an "upload-only" mode.

**Effect:**
*   `True`:  The system will perform data processing steps like PDF text extraction, HTML data integration, attorney processing, MDL identification, and summary generation, *in addition to* potentially uploading data if `upload` is also `True`.
*   `False`:  The system will bypass data processing stages. If `upload` is `True`, it will enter "upload-only" mode (see `upload` parameter documentation for details). If `upload` is `False`, no post-processing or uploading will occur beyond the initial scraping if the `scraper` parameter is enabled.

**Example Usage in `main2.py`:**

```python
params = {
    # ... other parameters ...
    "post_process": True,  # Enable full post-processing workflow
    # ...
}
```

---

### `reprocess_files`

**Type:** `List[str]` or `Boolean True`

**Purpose:**  Specifies which JSON files should be re-processed during the **post-processing stage** (when `post_process = True`). This parameter is not relevant in "upload-only" mode.

**Possible Values:**
*   `List[str]`: A list of filenames (without paths) of JSON files to be re-processed. For example: `["file1.json", "file2.json", "file3.json"]`. These files are expected to be located in the `dockets` directory of the current date.
*   `True`:  Signals to re-process **all** JSON files within the current date's `dockets` directory during post-processing.
*   `False` or Not Set:  During post-processing, JSON files are processed according to the default logic (typically all JSON files in the date directory, unless further filtered by other conditions).

**Effect:**
*   `List[str]`: When `post_process = True`, only the JSON files listed in `reprocess_files` will undergo post-processing.
*   `True`: When `post_process = True`, all JSON files in the date-specific `dockets` directory will be post-processed.
*   `False` or Not Set: When `post_process = True`, the processing will follow the default behavior. In "upload-only" mode (`post_process = False`, `upload = True`), this parameter might be used (depending on `run_upload_only` implementation - needs further investigation).

**Example Usage in `main2.py`:**

```python
params = {
    # ... other parameters ...
    "post_process": True,
    "reprocess_files": ["cand_25_02347_E_3069_v_Uber_Technologies_Inc_et_al.json", "gand_25_01150_Thomas_v_Teva_Pharmaceuticals_USA_Inc_et_al.json"], # Re-process specific files during post-processing
    # ...
}

# Or to re-process all files in the date directory during post-processing:
params = {
    # ... other parameters ...
    "post_process": True,
    "reprocess_files": True, # Re-process all files during post-processing
    # ...
}
```

---

### `reprocess_md`

**Type:** `Boolean`

**Purpose:**  Determines whether to force re-extraction of text from PDF files and regeneration of `.md` (Markdown) files **during the post-processing stage** (when `post_process = True`).  This setting is not relevant in "upload-only" mode.

**Possible Values:**
*   `True`:  Force **re-extraction** of text from PDFs during post-processing. Even if an `.md` file already exists for a PDF, it will be overwritten with a fresh text extraction.
*   `False`:  **Avoid re-extraction** if possible during post-processing. The system will check for existing `.md` files. If an `.md` file exists for a PDF, the text will be loaded from the `.md` file, saving time.

**Effect:**
*   `True`: When `post_process = True`, ensures the most up-to-date text extraction from PDFs, but can increase processing time.
*   `False`: When `post_process = True`, optimizes processing speed by reusing existing text extractions when available.

**Example Usage in `main2.py`:**

```python
params = {
    # ... other parameters ...
    "post_process": True,
    "reprocess_md": True, # Force re-extraction of PDF text during post-processing
    # ...
}
```

---

### `start_from_incomplete`

**Type:** `Boolean`

**Purpose:**  Directs the post-processor to specifically re-process dockets that were previously identified as "incomplete" **during the post-processing stage** (when `post_process = True`). This setting is not relevant in "upload-only" mode.

**Possible Values:**
*   `True`:  **Re-process incomplete summaries**. The `PostProcessorV2` will load a list of previously identified incomplete dockets from `incomplete_summaries.csv` and attempt to re-process only these files to fill in missing information during the post-processing stage.
*   `False`:  **Do not specifically target incomplete summaries during post-processing**.

**Effect:**
*   `True`: When `post_process = True`, focuses post-processing efforts on dockets known to be missing data.
*   `False`: When `post_process = True`, processes dockets based on other criteria without prioritizing incomplete ones.

**Example Usage in `main2.py`:**

```python
params = {
    # ... other parameters ...
    "post_process": True,
    "start_from_incomplete": True, # Re-process incomplete dockets during post-processing
    # ...
}
```

---

### `skip_files`

**Type:** `List[str]`

**Purpose:**  Provides a mechanism to explicitly exclude certain JSON files from being processed during the **post-processing stage** (when `post_process = True`) or in "upload-only" mode (when `post_process = False` and `upload = True`).

**Possible Values:**
*   `List[str]`: A list of filenames (without paths) of JSON files to be skipped.
*   `[]` or Not Set:  No files are explicitly skipped.

**Effect:**
*   If a JSON filename is present in the `skip_files` list, it will be ignored during post-processing or upload-only operations.

**Example Usage in `main2.py`:**

```python
params = {
    # ... other parameters ...
    "post_process": True, # Or post_process: False and upload: True
    "skip_files": ["problematic_file.json", "another_bad_file.json"], # Skip these specific files during processing or upload
    # ...
}
```

---

### `upload_types`

**Type:** `List[str]`

**Purpose:**  Specifies the AWS services to which the processed docket data should be uploaded when `upload = True`. This setting is relevant both during the full post-processing workflow (when `post_process = True`) and in "upload-only" mode (when `post_process = False` and `upload = True`).

**Possible Values:**
*   `['s3']`:  Upload only to **Amazon S3**.
*   `['dynamodb']`: Upload only to **Amazon DynamoDB**.
*   `['s3', 'dynamodb']` or `['dynamodb', 's3']`: Upload to **both Amazon S3 and Amazon DynamoDB**.
*   `[]` or Not Set: Defaults to uploading to **both Amazon S3 and Amazon DynamoDB**.

**Effect:**
*   Determines the upload destination(s) when the upload stage is active (`upload = True`).

**Example Usage in `main2.py`:**

```python
params = {
    # ... other parameters ...
    "post_process": True, # Or post_process: False
    "upload": True,
    "upload_types": ['dynamodb'], # Upload only to DynamoDB
    # ...
}

params = {
    # ... other parameters ...
    "post_process": True, # Or post_process: False
    "upload": True,
    "upload_types": ['s3', 'dynamodb'], # Upload to both S3 and DynamoDB
    # ...
}
```

---

### `upload`

**Type:** `Boolean`

**Purpose:**  A simple on/off switch to enable or disable the **upload stage to AWS**.  **Crucially, setting `upload = True` will trigger the upload process *regardless* of the `post_process` setting.** This allows for an "upload-only" mode, where previously processed files can be uploaded without re-running the entire post-processing workflow.

**Possible Values:**
*   `True`:  **Enable upload**. The system will attempt to upload processed data to AWS. If `post_process` is also `True`, this will occur *after* post-processing. If `post_process` is `False`, it will operate in "upload-only" mode, attempting to upload files based on configuration (likely using `reprocess_files` to specify which files to upload if needed, although `reprocess_files` might be more relevant for post-processing context).
*   `False`: **Disable upload**. The upload stage will be skipped. Processed data will be saved locally only.

**Effect:**
*   `True`: Data is uploaded to AWS. In "upload-only" mode (when `post_process = False`), the system utilizes the `run_upload_only()` function in `MainProcessor`, which uses the *older* `PostProcessor` class for uploading.  The behavior of `reprocess_files` in upload-only mode needs further clarification by examining the `run_upload_only` and `PostProcessor.upload_dockets_to_aws` methods.
*   `False`: Data remains only locally.

**Example Usage in `main2.py`:**

```python
params = {
    # ... other parameters ...
    "post_process": True,
    "upload": True, # Enable upload to AWS after post-processing
    # ...
}

params = {
    # ... other parameters ...
    "post_process": False,
    "upload": True, # Enable upload to AWS in "upload-only" mode (skips post-processing)
    # ...
}
```

---

### `upload_pending`

**Type:** `Boolean`

**Purpose:**  Specifically triggers the upload of "pending" dockets to DynamoDB. Pending dockets are identified as those that were initially scraped but not fully processed and uploaded to DynamoDB (often marked with `added_on == '00000000'` or missing `added_on`). This is typically used as a separate, final step to ensure all scraped data is in DynamoDB. This setting is relevant both during the full post-processing workflow (when `post_process = True`) and potentially independently if only upload of pending items is desired.

**Possible Values:**
*   `True`:  **Upload pending dockets**. The system will specifically look for JSON files in the current date's `dockets` directory that are marked as pending (e.g., `added_on == '00000000'`) and upload their metadata to DynamoDB. This can be triggered after post-processing or independently.
*   `False`:  **Do not upload pending dockets**. This specific pending docket upload step will be skipped.

**Effect:**
*   `True`: Ensures that data from initially scraped dockets that might have been missed in previous uploads is finalized and available in DynamoDB.
*   `False`:  The pending docket upload step is skipped.

**Example Usage in `main2.py`:**

```python
params = {
    # ... other parameters ...
    "post_process": True, # Or post_process: False
    "upload_pending": True, # Upload pending dockets to DynamoDB
    # ...
}
```

---

**Interrelationship Summary:**

These post-processing settings work in concert to provide flexible control over the data pipeline. `post_process` generally governs the data enrichment and preparation stages, while `upload` is the primary control for initiating the AWS upload.  `reprocess_files`, `start_from_incomplete`, and `skip_files` refine which dockets are processed *during post-processing* (when `post_process = True`). `reprocess_md` is an optimization for PDF text handling within post-processing. Finally, `upload_types` and `upload_pending` govern *where* and *how* the processed data is uploaded to AWS when `upload = True`.  The "upload-only" mode (when `post_process = False` and `upload = True`) provides a way to upload data without re-running the full post-processing workflow, but the precise behavior of parameters like `reprocess_files` in this mode needs further investigation of the `run_upload_only` and `PostProcessor` classes.