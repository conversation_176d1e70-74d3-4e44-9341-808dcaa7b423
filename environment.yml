name: lexgenius
channels:
  - conda-forge
  - defaults
  - microsoft
dependencies:
  - pip
  - python=3.11
  - mypy
  - pandas
  - playwright
  - requests
  - tqdm
  - boto3
  - backoff
  - rich
  - undetected-chromedriver
  - aiohttp
  - python-dotenv
  - tiktoken
  - beautifulsoup4
  - pytesseract
  - pdf2image
  - aiofiles
  - tenacity
  - openai
  - portalocker
  - retrying
  - jinja2
  - webdriver-manager
  - fasteners
  - colorlog
  - chardet
  - matplotlib
  - pypdf2
  - tabulate
  - nltk
  - levenshtein
  - scikit-learn
  - imagehash
  - pillow
  - networkx
  - holidays
  - retry
  - seaborn
  - cairosvg
  - pyyaml
  - lxml
  - boto3
  - rich
  - aioboto3
  - mistralai
  - aiobotocore
  - sentencepiece
  - transformers
  - pytorch
  - sentence-transformers
  - faiss-cpu
  - hdbscan
  - psutil
  - spacy
  - fuzzywuzzy
  - pytest
  - pytest-cov
  - pytest-timeout
  - pytest-asyncio
  - pydantic
  - pydantic-settings
prefix: /Users/<USER>/miniconda3/envs/lexgenius
