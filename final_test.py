#!/usr/bin/env python3
"""
Final direct test of our zip extractor functionality
"""

import os
import logging
import shutil
import tempfile
import zipfile
import json
from pathlib import Path
from typing import List, Tuple, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('zip_extractor_test')

# ==================== ZipExtractor implementation ====================
class ZipExtractor:
    """Class to handle ZIP file extraction with proper error handling and logging."""
    
    def __init__(self, logger=None):
        """Initialize the ZipExtractor with optional custom logger.
        
        Args:
            logger: Optional logger instance. If not provided, creates a new one.
        """
        self.logger = logger or logging.getLogger(__name__)
    
    def extract_all_zips(self, target_path: str, delete_after: bool = True) -> Tuple[int, int]:
        """Extract all ZIP files in the target directory.
        
        Args:
            target_path: Directory path containing ZIP files
            delete_after: Whether to delete ZIP files after extraction
            
        Returns:
            Tuple of (successful_count, failed_count)
        """
        if not os.path.exists(target_path):
            self.logger.error(f"Target path does not exist: {target_path}")
            return 0, 0
            
        zip_files = self._find_zip_files(target_path)
        
        if not zip_files:
            self.logger.info(f"No ZIP files found in {target_path}")
            return 0, 0
            
        success_count = 0
        failed_count = 0
        
        for zip_file in zip_files:
            try:
                self.logger.info(f"Processing ZIP file: {zip_file}")
                result = self.extract_single_zip(zip_file, target_path, delete_after)
                if result:
                    success_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                self.logger.error(f"Failed to process ZIP file {zip_file}: {str(e)}")
                failed_count += 1
                
        self.logger.info(f"ZIP extraction complete. Successful: {success_count}, Failed: {failed_count}")
        return success_count, failed_count
    
    def extract_single_zip(self, zip_path: str, destination_dir: str = None, delete_after: bool = True) -> bool:
        """Extract a single ZIP file with proper naming of extracted contents.
        
        Args:
            zip_path: Path to the ZIP file
            destination_dir: Directory to extract files to (defaults to ZIP file's directory)
            delete_after: Whether to delete the ZIP file after extraction
            
        Returns:
            bool: True if extraction was successful, False otherwise
        """
        if not os.path.exists(zip_path):
            self.logger.error(f"ZIP file does not exist: {zip_path}")
            return False
            
        if not destination_dir:
            destination_dir = os.path.dirname(zip_path)
            
        # Create temp extraction directory
        temp_dir = os.path.join(destination_dir, 'temp_extract')
        try:
            # Ensure clean temp directory exists
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            os.makedirs(temp_dir, exist_ok=True)
            
            # Extract to temp directory
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
                
            # Get base filename for renaming
            zip_base_name = os.path.splitext(os.path.basename(zip_path))[0]
            
            # Rename and move extracted files
            extracted_files = self._rename_extracted_files(temp_dir, destination_dir, zip_base_name)
            
            if delete_after and extracted_files:
                try:
                    os.remove(zip_path)
                    self.logger.info(f"Deleted ZIP file: {zip_path}")
                except Exception as e:
                    self.logger.warning(f"Could not delete ZIP file {zip_path}: {str(e)}")
            
            return True
            
        except zipfile.BadZipFile:
            self.logger.error(f"Bad ZIP file: {zip_path}")
            return False
        except PermissionError:
            self.logger.error(f"Permission denied while extracting: {zip_path}")
            return False
        except Exception as e:
            self.logger.error(f"Error extracting ZIP file {zip_path}: {str(e)}")
            return False
        finally:
            # Clean up temp directory
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except Exception as e:
                    self.logger.warning(f"Could not remove temp directory {temp_dir}: {str(e)}")
    
    def _find_zip_files(self, directory: str) -> List[str]:
        """Find all ZIP files in the given directory.
        
        Args:
            directory: Directory to search for ZIP files
            
        Returns:
            List of full paths to ZIP files
        """
        result = []
        for root, _, files in os.walk(directory):
            for file in files:
                if file.lower().endswith('.zip'):
                    result.append(os.path.join(root, file))
        return result
    
    def _rename_extracted_files(self, temp_dir: str, destination_dir: str, base_name: str) -> List[str]:
        """Rename extracted files based on the ZIP file's base name.
        
        Args:
            temp_dir: Directory containing extracted files
            destination_dir: Directory to move files to
            base_name: Base name for renaming
            
        Returns:
            List of paths to renamed files
        """
        renamed_files = []
        files = list(Path(temp_dir).rglob('*'))
        
        for file_path in files:
            if file_path.is_file():
                # Determine new filename based on base_name and extension
                extension = file_path.suffix.lower()
                new_name = f"{base_name}{extension}"
                
                # Special handling for PDF and JSON pairs
                if extension in ['.pdf', '.json']:
                    # Use original base_name for both PDF and JSON
                    new_name = f"{base_name}{extension}"
                else:
                    # For other files, keep original name with prefix
                    new_name = f"{base_name}_{file_path.name}"
                
                new_path = os.path.join(destination_dir, new_name)
                
                # Handle filename conflicts
                counter = 1
                while os.path.exists(new_path):
                    name_part = os.path.splitext(new_name)[0]
                    new_name = f"{name_part}_{counter}{extension}"
                    new_path = os.path.join(destination_dir, new_name)
                    counter += 1
                
                try:
                    shutil.move(str(file_path), new_path)
                    self.logger.debug(f"Renamed {file_path.name} to {new_name}")
                    renamed_files.append(new_path)
                except Exception as e:
                    self.logger.error(f"Failed to rename {file_path.name} to {new_name}: {str(e)}")
        
        return renamed_files

# ==================== Test functions ====================
def create_test_zip_files(test_dir: str):
    """Create test zip files for testing."""
    os.makedirs(test_dir, exist_ok=True)
    
    # Create test cases
    test_cases = [
        {
            "name": "case1_2023_01_01",
            "contents": {
                "document.pdf": "PDF content for case 1",
                "metadata.json": json.dumps({
                    "case_number": "123-cv-456",
                    "court": "Test Court",
                    "date_filed": "2023-01-01"
                })
            }
        },
        {
            "name": "case2 with spaces 2023-02-02",
            "contents": {
                "file with spaces.pdf": "PDF content for case 2",
                "metadata.json": json.dumps({
                    "case_number": "789-cv-012",
                    "court": "Another Court",
                    "date_filed": "2023-02-02"
                })
            }
        }
    ]
    
    # Create zip files
    zip_files_created = []
    for case in test_cases:
        zip_path = os.path.join(test_dir, f"{case['name']}.zip")
        with zipfile.ZipFile(zip_path, 'w') as zipf:
            for filename, content in case["contents"].items():
                temp_file = os.path.join(test_dir, filename)
                with open(temp_file, 'w') as f:
                    f.write(content)
                zipf.write(temp_file, filename)
                os.remove(temp_file)
        zip_files_created.append(zip_path)
    
    return test_dir, zip_files_created

def test_zip_extractor():
    """Test the ZipExtractor class."""
    # Create a temporary directory
    test_dir = tempfile.mkdtemp()
    logger.info(f"Created test directory: {test_dir}")
    
    try:
        # Create test zip files
        test_dir, zip_files = create_test_zip_files(test_dir)
        logger.info(f"Created test zip files: {zip_files}")
        
        # Initialize the ZipExtractor
        extractor = ZipExtractor(logger)
        
        # Extract all zip files
        success_count, failed_count = extractor.extract_all_zips(test_dir)
        logger.info(f"Extracted {success_count} zip files successfully, {failed_count} failed")
        
        # List the files in the directory after extraction
        files = os.listdir(test_dir)
        logger.info(f"Files after extraction: {files}")
        
        # Check if all expected files are present
        expected_files = [
            'case1_2023_01_01.pdf',
            'case1_2023_01_01.json',
            'case2 with spaces 2023-02-02.pdf',
            'case2 with spaces 2023-02-02.json'
        ]
        
        missing_files = []
        for expected in expected_files:
            if expected not in files:
                missing_files.append(expected)
        
        if missing_files:
            logger.error(f"Missing expected files: {missing_files}")
            return False
        
        # Check if the content of the files is correct
        for expected in expected_files:
            if expected.endswith('.json'):
                with open(os.path.join(test_dir, expected), 'r') as f:
                    data = json.load(f)
                    logger.info(f"JSON file {expected} contains data: {data}")
        
        logger.info("All files extracted successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error during test: {str(e)}")
        return False
    finally:
        # Clean up
        shutil.rmtree(test_dir)
        logger.info(f"Cleaned up test directory: {test_dir}")

if __name__ == "__main__":
    if test_zip_extractor():
        print("TEST PASSED: ZipExtractor works correctly!")
        exit(0)
    else:
        print("TEST FAILED: Issues with ZipExtractor")
        exit(1)