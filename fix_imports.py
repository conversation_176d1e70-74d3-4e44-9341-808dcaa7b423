#!/usr/bin/env python3
"""
Fix import statements in pacer module files to use relative imports
instead of absolute 'pacer' imports.
"""
import os
import re
from pathlib import Path
import sys

# Get the absolute path to the project root
PROJECT_ROOT = Path(os.path.expanduser('~/PycharmProjects/lexgenius'))
PACER_DIR = PROJECT_ROOT / 'src' / 'lib' / 'pacer'

def fix_imports(file_path):
    """Fix imports in the specified file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Replace 'import pacer' with 'import src.lib.pacer'
    content = re.sub(r'import\s+pacer\b', 'import src.lib.pacer', content)
    
    # Replace 'from pacer' with 'from src.lib.pacer'
    content = re.sub(r'from\s+pacer\b', 'from src.lib.pacer', content)
    
    # Write the fixed content back to the file
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Fixed imports in {file_path}")

def main():
    """Main function to scan and fix imports in pacer module files."""
    # List of files to check
    files_to_check = [
        PACER_DIR / '__init__.py',
        PACER_DIR / 'docket_processor.py',
        PACER_DIR / 'pacer_document_downloader.py',
        PACER_DIR / 'orchestrator.py',
        PACER_DIR / 'case_transfer_handler.py',
        PACER_DIR / 'report_handler.py',
        PACER_DIR / 'authenticator.py',
    ]
    
    # Add prior_to_refactoring files
    prior_dir = PACER_DIR / 'prior_to_refactoring'
    if prior_dir.exists():
        for file in prior_dir.glob('*.py'):
            files_to_check.append(file)
    
    fixed_count = 0
    for file_path in files_to_check:
        if file_path.exists():
            try:
                fix_imports(file_path)
                fixed_count += 1
            except Exception as e:
                print(f"Error fixing imports in {file_path}: {e}")
    
    print(f"Fixed imports in {fixed_count} files")

if __name__ == "__main__":
    main()