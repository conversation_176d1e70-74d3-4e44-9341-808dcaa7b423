# Facebook Ad Categorizer Flow

```mermaid
graph TD
    %% Main Flow
    START[Start] --> PARSE[Parse Arguments]
    PARSE --> CHECK_GROUP{Group Names Mode?}
    
    %% Group Names Branch
    CHECK_GROUP -->|Yes| LOAD_CSV[Load Standardized CSV]
    LOAD_CSV --> GET_COMPANIES[Extract Unique Companies]
    GET_COMPANIES --> CALC_DISTANCE[Calculate Levenshtein Distances]
    CALC_DISTANCE --> CLUSTER[DBSCAN Clustering]
    CLUSTER --> SAVE_GROUPED[Save Grouped Results]
    SAVE_GROUPED --> END[End]

    %% Main Processing Branch
    CHECK_GROUP -->|No| LOAD_CONFIG[Load Base Config]
    LOAD_CONFIG --> CHECK_MODE{Check Processing Mode}

    %% Company Standardization Branch
    CHECK_MODE -->|Standardize Companies| INIT_MINIMAL[Initialize Minimal Processor]
    INIT_MINIMAL --> SETUP_OLLAMA[Setup Ollama Session]
    SETUP_OLLAMA --> STANDARDIZE[Standardize Company Names]
    STANDARDIZE --> CLEANUP_OLLAMA[Cleanup Ollama]
    CLEANUP_OLLAMA --> END

    %% Litigation Analysis Branch
    CHECK_MODE -->|Group Litigations| INIT_LITIGATION[Initialize for Litigation]
    INIT_LITIGATION --> ANALYZE[Analyze Litigation Similarity]
    ANALYZE --> CLEANUP_LITIGATION[Cleanup]
    CLEANUP_LITIGATION --> END

    %% Normal Processing Branch
    CHECK_MODE -->|Normal Processing| SELECT_ENGINE{Select Engine}
    SELECT_ENGINE -->|Ollama| INIT_OLLAMA[Initialize Ollama]
    SELECT_ENGINE -->|DeepSeek| INIT_DEEPSEEK[Initialize DeepSeek]
    
    INIT_OLLAMA --> PROCESS_ADS[Process Ads Async]
    INIT_DEEPSEEK --> PROCESS_ADS
    PROCESS_ADS --> FINAL_CLEANUP[Cleanup]
    FINAL_CLEANUP --> END

    %% Styling
    classDef process fill:#f9f,stroke:#333,stroke-width:2px;
    classDef decision fill:#bbf,stroke:#333,stroke-width:2px;
    classDef start_end fill:#9f9,stroke:#333,stroke-width:2px;

    class START,END start_end;
    class CHECK_GROUP,CHECK_MODE,SELECT_ENGINE decision;
    class PROCESS_ADS,STANDARDIZE,ANALYZE process;
```