# pacer_cleaning_items.py

import ast
import concurrent.futures
import os
import sys
import time
import json
from collections import defaultdict
from pathlib import Path
from typing import List, Dict, Any, Set, Union
import decimal  # For decimal.Decimal type check
from decimal import Decimal  # For Decimal class
import pandas as pd
import boto3
from rich.console import Console  # Correct import for Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.prompt import Prompt  # Make sure Prompt is imported for interactive_loop

# Add project root to path for imports
# This assumes the script is in a subdirectory like 'src/scripts/'
# and 'src/lib/' contains 'pacer_manager.py' and 'config.py'
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))


# The `console` object is instantiated inside the class __init__ method now,
# to ensure it's handled consistently after `PacerMdlCleaner` is ready.


class PacerMdlCleaner:
    def __init__(self, max_workers: int = 10):
        # Instantiate console within the class, after potential import path adjustments
        self.console = Console()
        self.console.print("[bold blue]PacerMdlCleaner: Initializing dependencies and DynamoDB clients...[/bold blue]")

        # --- CRITICAL: LOCAL IMPORTS TO BREAK CIRCULAR DEPENDENCIES ---
        # PacerManager and load_config are imported here, inside __init__,
        # to ensure the module loading order does not create circular imports.
        # This is the most aggressive way to manage dependencies when external files
        # cannot be modified.
        try:
            # Attempt to import PacerManager and load_config from their expected paths.
            # If `config` itself has a circular import, this might still fail.
            from src.lib.pacer_manager import PacerManager
            from src.lib.config import load_config
            self.console.print("[green]Dependencies (PacerManager, config) imported successfully.[/green]")
        except ImportError as e_imp:
            self.console.print(
                f"[bold red]CRITICAL ERROR: Initial import of PacerManager or config failed: {e_imp}[/bold red]")
            # Log the full traceback for debugging the circular import
            import logging
            logging.exception("Circular import detected or module not found during PacerManager/config import.")

            # This is a fallback to allow the script to *try* to run,
            # but usually, circular imports need to be fixed at their source.
            self.console.print("[yellow]Attempting fallback imports for PacerManager and config...[/yellow]")
            try:
                # This complex path logic is usually for scripts run from odd directories.
                # If running from project root, the sys.path.insert(0, ...) at the top should be enough.
                # However, re-attempting just in case.
                current_dir = os.path.dirname(os.path.abspath(__file__))
                parent_dir = os.path.dirname(current_dir)
                if os.path.basename(parent_dir) != "src":
                    sys.path.insert(0, os.path.abspath(os.path.join(current_dir, "..")))
                else:
                    sys.path.insert(0, os.path.abspath(os.path.join(parent_dir, "..")))
                from src.lib.pacer_manager import PacerManager
                from src.lib.config import load_config
                self.console.print("[green]Fallback imports successful.[/green]")
            except ImportError as inner_e:
                self.console.print(
                    f"[bold red]CRITICAL ERROR: PacerManager or config could not be imported even with fallback: {inner_e}[/bold red]")
                self.pacer_manager = None  # Ensure PacerManager is None if import fails
                self.logger = self.console  # Use console as fallback logger for critical errors
                self.logger.error(f"PacerManager/config import failed: {inner_e}", exc_info=True)
                # Re-raise to prevent further execution if core dependencies are missing
                raise RuntimeError(
                    "Core dependencies (PacerManager, config) could not be imported. Please check your project structure and dependencies.") from inner_e
        # --- END LOCAL IMPORTS ---

        self.pacer_manager: PacerManager | None = None  # Type hint, instance will be set below
        self.dockets_db_client: Any = None
        self.dockets_db_resource: Any = None
        self.logger = None  # Will be initialized from pacer_manager or as fallback

        try:
            # `load_config` is now guaranteed to be imported if we reached here
            config = load_config('01/01/70')

            # `PacerManager` is also guaranteed to be imported
            self.pacer_manager = PacerManager(config, use_local=True)
            self.logger = self.pacer_manager.logger
            self.logger.info("PacerMdlCleaner initialized with PacerManager for local DB.")

            dynamodb_endpoint_url = config.get("DYNAMODB_ENDPOINT_URL", "http://localhost:8000")
            aws_region = config.get("AWS_REGION", "us-east-1")

            self.console.print(
                f"[yellow]Attempting to initialize dedicated DynamoDB clients/resources for PacerDockets with endpoint_url: [cyan]{dynamodb_endpoint_url}[/cyan] and region: {aws_region}[/yellow]")
            self.logger.info(
                f"Attempting to initialize dedicated DynamoDB clients/resources for PacerDockets with endpoint_url: {dynamodb_endpoint_url} and region: {aws_region}")

            common_client_kwargs = {
                'endpoint_url': dynamodb_endpoint_url,
                'region_name': aws_region,
                # CRITICAL FIX: Removed dummy AWS credentials for local DynamoDB.
                # These are often rejected by dynamodb-local, causing UnrecognizedClientException.
                # 'aws_access_key_id': 'DUMMY_ACCESS_KEY',
                # 'aws_secret_access_key': 'DUMMY_SECRET_KEY',
            }

            self.dockets_db_client = boto3.client('dynamodb', **common_client_kwargs)
            self.dockets_db_resource = boto3.resource('dynamodb', **common_client_kwargs)

            # Test the client immediately with a simple call to catch connection issues early
            self.console.print("[cyan]Testing dedicated DynamoDB client connection...[/cyan]")
            self.dockets_db_client.list_tables()
            self.console.print(
                f"[green]Dedicated DynamoDB client and resource initialized and connected successfully for PacerDockets.[/green]")
            self.logger.info(
                f"Dedicated DynamoDB client and resource initialized for PacerDockets at {dynamodb_endpoint_url}.")

        except Exception as e:
            self.console.print(
                f"[bold red]CRITICAL: Error initializing PacerManager or dedicated DynamoDB clients/resources: {e}[/bold red]")
            # If logger not set by PacerManager, create a basic one
            if self.logger is None:
                import logging
                self.logger = logging.getLogger("PacerMdlCleaner_fallback")
                if not self.logger.hasHandlers():
                    handler = logging.StreamHandler(sys.stdout)
                    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                    handler.setFormatter(formatter)
                    self.logger.addHandler(handler)
                    self.logger.setLevel(logging.INFO)
            self.logger.error(f"PacerManager or dedicated DynamoDB client/resource initialization failed: {e}",
                              exc_info=True)
            self.pacer_manager = None  # Ensure it's None if initialization fails
            self.dockets_db_client = None
            self.dockets_db_resource = None
            self.console.print(
                "[bold red]ACTION REQUIRED: Please ensure local DynamoDB is running on port 8000 and is accessible. The error '[yellow]UnrecognizedClientException[/yellow]' specifically indicates credential issues or unexpected communication with DynamoDB local. Consider verifying your `dynamodb-local` setup.[/bold red]")

        self.pacer_items: List[Dict[str, Any]] = []
        self.max_workers = max_workers
        self.filtered_results: Dict[str, List[Dict[str, Any]]] = defaultdict(list)

    @staticmethod
    def convert_decimal(value: Any) -> Union[int, float, Any]:
        """ Converts Decimal objects to int or float. Passes other types through. """
        if isinstance(value, Decimal):
            if value % 1 == 0:
                return int(value)
            else:
                return float(value)
        return value

    def _deep_clean_value(self, value: Any) -> Any:
        """
        Recursively cleans a value, handling "nan" strings, float('nan'), literal evaluation,
        Decimal conversion, and nested structures. This is for general-purpose data cleaning,
        applied *before* DynamoDB specific sanitization.
        """
        # 1. Handle string "nan" immediately.
        if isinstance(value, str) and value.lower() == 'nan':
            return None  # Convert "nan" string to None

        # 2. Handle float NaN.
        if isinstance(value, float) and (value != value):  # Check for NaN
            return None  # Convert float('nan') to None

        # 3. Handle decimal.Decimal objects using the dedicated static method.
        if isinstance(value, Decimal):
            return self.convert_decimal(value)

        # 4. Handle string literals (lists, dicts, numbers, bools).
        if isinstance(value, str):
            val_str_stripped = value.strip()
            # Only attempt ast.literal_eval if it looks like a list or dict literal.
            # This avoids SyntaxWarning for malformed numbers that aren't true literals.
            if val_str_stripped.startswith(('[', '{')) and val_str_stripped.endswith((']', '}')):
                try:
                    evaluated_value = ast.literal_eval(val_str_stripped)
                    # If it's a list, dict, int, float, or bool literal, use it.
                    # Recurse on evaluated value to ensure nested elements are also cleaned.
                    if isinstance(evaluated_value, (list, dict, int, float, bool)):
                        return self._deep_clean_value(evaluated_value)
                except (ValueError, SyntaxError, TypeError):
                    # Not a valid literal, or malformed. Keep the original string.
                    pass
            return value  # Return original string if not a recognized literal

        # 5. Recursively clean lists.
        if isinstance(value, list):
            cleaned_list = []
            for item in value:
                processed_item = self._deep_clean_value(item)
                if processed_item is not None:  # Filter out None items from lists
                    cleaned_list.append(processed_item)
            return cleaned_list

        # 6. Recursively clean dictionaries.
        if isinstance(value, dict):
            cleaned_dict = {}
            for k, v in value.items():
                processed_v = self._deep_clean_value(v)
                if processed_v is not None:  # Filter out None values from dicts
                    cleaned_dict[k] = processed_v
            return cleaned_dict

        # 7. For all other types (int, bool, Path, etc.), return as is.
        return value

    def _create_pacer_dockets_table(self) -> None:
        """
        Creates a new local DynamoDB table 'PacerDockets' with specified PK, SK, and GSIs.
        This is a helper method called by other public methods.
        """
        self.console.print(f"[bold yellow]Step: Attempting to create or verify 'PacerDockets' table...[/bold yellow]")
        if not self.dockets_db_client:
            self.console.print(
                "[bold red]CRITICAL ERROR: Dedicated PacerDockets DB client not available. Cannot create table. Please review initialization errors at the start of execution.[/bold red]")
            return

        table_name = "PacerDockets"
        dynamodb_client = self.dockets_db_client

        try:
            self.console.print(f"[cyan]Checking if table '{table_name}' already exists...[/cyan]")
            # This is where a connection error would likely manifest if the client wasn't truly connected
            existing_tables = dynamodb_client.list_tables()['TableNames']
            if table_name in existing_tables:
                self.console.print(f"[yellow]Table '{table_name}' already exists. Skipping creation.[/yellow]")
                return

            self.console.print(f"[cyan]Table '{table_name}' does not exist. Proceeding with creation...[/cyan]")

            attribute_definitions = [
                {'AttributeName': 'CourtId', 'AttributeType': 'S'},
                {'AttributeName': 'DocketNum', 'AttributeType': 'S'},
                {'AttributeName': 'AddedOn', 'AttributeType': 'S'},
                {'AttributeName': 'FilingDate', 'AttributeType': 'S'},
                {'AttributeName': 'MdlNum', 'AttributeType': 'S'},
                {'AttributeName': 'TransferorCourtId', 'AttributeType': 'S'},
                {'AttributeName': 'TransferorDocketNum', 'AttributeType': 'S'},
                {'AttributeName': 'TransfereeCourtId', 'AttributeType': 'S'},
                {'AttributeName': 'TransfereeDocketNum', 'AttributeType': 'S'},
            ]

            key_schema = [
                {'AttributeName': 'CourtId', 'KeyType': 'HASH'},
                {'AttributeName': 'DocketNum', 'KeyType': 'RANGE'}
            ]

            global_secondary_indexes = [
                {
                    'IndexName': 'AddedOn-index',
                    'KeySchema': [{'AttributeName': 'AddedOn', 'KeyType': 'HASH'}],
                    'Projection': {'ProjectionType': 'ALL'},
                    'ProvisionedThroughput': {'ReadCapacityUnits': 1, 'WriteCapacityUnits': 1}
                },
                {
                    'IndexName': 'CourtId-FilingDate-index',
                    'KeySchema': [
                        {'AttributeName': 'CourtId', 'KeyType': 'HASH'},
                        {'AttributeName': 'FilingDate', 'KeyType': 'RANGE'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'},
                    'ProvisionedThroughput': {'ReadCapacityUnits': 1, 'WriteCapacityUnits': 1}
                },
                {
                    'IndexName': 'MdlNum-CourtId-index',
                    'KeySchema': [
                        {'AttributeName': 'MdlNum', 'KeyType': 'HASH'},
                        {'AttributeName': 'CourtId', 'KeyType': 'RANGE'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'},
                    'ProvisionedThroughput': {'ReadCapacityUnits': 1, 'WriteCapacityUnits': 1}
                },
                {
                    'IndexName': 'MdlNum-DocketNum-index',
                    'KeySchema': [
                        {'AttributeName': 'MdlNum', 'KeyType': 'HASH'},
                        {'AttributeName': 'DocketNum', 'KeyType': 'RANGE'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'},
                    'ProvisionedThroughput': {'ReadCapacityUnits': 1, 'WriteCapacityUnits': 1}
                },
                {
                    'IndexName': 'MdlNum-FilingDate-index',
                    'KeySchema': [
                        {'AttributeName': 'MdlNum', 'KeyType': 'HASH'},
                        {'AttributeName': 'FilingDate', 'KeyType': 'RANGE'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'},
                    'ProvisionedThroughput': {'ReadCapacityUnits': 1, 'WriteCapacityUnits': 1}
                },
                {
                    'IndexName': 'TransferorCourtId-TransferorDocketNum-index',
                    'KeySchema': [
                        {'AttributeName': 'TransferorCourtId', 'KeyType': 'HASH'},
                        {'AttributeName': 'TransferorDocketNum', 'KeyType': 'RANGE'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'},
                    'ProvisionedThroughput': {'ReadCapacityUnits': 1, 'WriteCapacityUnits': 1}
                },
                {
                    'IndexName': 'TransfereeCourtId-TransfereeDocketNum-index',
                    'KeySchema': [
                        {'AttributeName': 'TransfereeCourtId', 'KeyType': 'HASH'},
                        {'AttributeName': 'TransfereeDocketNum', 'KeyType': 'RANGE'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'},
                    'ProvisionedThroughput': {'ReadCapacityUnits': 1, 'WriteCapacityUnits': 1}
                }
            ]

            dynamodb_client.create_table(
                TableName=table_name,
                KeySchema=key_schema,
                AttributeDefinitions=attribute_definitions,
                ProvisionedThroughput={'ReadCapacityUnits': 1, 'WriteCapacityUnits': 1},
                GlobalSecondaryIndexes=global_secondary_indexes
            )

            self.console.print(
                f"[green]Table '{table_name}' creation initiated. Waiting for it to become active...[/green]")

            waiter = dynamodb_client.get_waiter('table_exists')
            waiter.wait(TableName=table_name,
                        WaiterConfig={'Delay': 5, 'MaxAttempts': 60})

            self.console.print(f"[bold green]Table '{table_name}' created and is now active.[/bold green]")

        except dynamodb_client.exceptions.ResourceInUseException as e:
            self.console.print(
                f"[yellow]Table '{table_name}' is already being created or exists. This is expected if a previous run was interrupted. Will wait for it to become active.[/yellow]")
            try:
                waiter = dynamodb_client.get_waiter('table_exists')
                waiter.wait(TableName=table_name, WaiterConfig={'Delay': 5, 'MaxAttempts': 60})
                self.console.print(f"[bold green]Table '{table_name}' is now active.[/bold green]")
            except Exception as wait_e:
                self.console.print(
                    f"[bold red]CRITICAL ERROR: Table '{table_name}' could not become active after creation attempt: {wait_e}[/bold red]")
                if self.logger:
                    self.logger.error(
                        f"CRITICAL ERROR: Table '{table_name}' could not become active after creation attempt: {wait_e}",
                        exc_info=True)
        except Exception as e:
            self.console.print(
                f"[bold red]CRITICAL ERROR: Unhandled error during table '{table_name}' creation or verification: {e}[/bold red]")
            if self.logger:
                self.logger.error(f"Error during table '{table_name}' creation or verification: {e}", exc_info=True)
            self.console.print(
                "[bold red]ACTION REQUIRED: Please ensure local DynamoDB is running on http://localhost:8000 and accessible. Check your network, firewall, or DynamoDB Local setup.[/bold red]")

    def _save_chunk_to_dynamodb(self, chunk: pd.DataFrame, table_name: str, progress_task_id: Any) -> int:
        """
        Worker function to save a chunk of a DataFrame to DynamoDB using batch_writer.
        The progress update is handled by the main thread receiving results from futures.
        """
        items_saved_in_chunk = 0
        if not self.dockets_db_resource:
            if self.logger:
                self.logger.error("Dedicated PacerDockets DB resource not available in worker. Cannot save data.")
            self.console.print("[bold red]CRITICAL: DynamoDB resource not available for saving data.[/bold red]")
            return 0

        table = self.dockets_db_resource.Table(table_name)

        try:
            with table.batch_writer() as batch:
                for idx, row in chunk.iterrows():
                    item = row.to_dict()
                    cleaned_item = self._clean_item_for_dynamodb(item)

                    if not cleaned_item.get('CourtId') or not cleaned_item.get('DocketNum'):
                        if self.logger:
                            self.logger.warning(
                                f"Skipping item (index {idx}) due to missing or empty PK/SK: CourtId='{cleaned_item.get('CourtId')}', DocketNum='{cleaned_item.get('DocketNum')}'")
                        continue

                    batch.put_item(Item=cleaned_item)
                    items_saved_in_chunk += 1
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error saving chunk to DynamoDB: {e}", exc_info=True)
            self.console.print(f"[red]Error saving chunk to DynamoDB: {e}[/red]")

        return items_saved_in_chunk

    def _parallel_save_to_dynamodb(self, df: pd.DataFrame, table_name: str) -> None:
        """
        Saves a DataFrame to DynamoDB in parallel using ThreadPoolExecutor.
        """
        self.console.print(f"[cyan]Starting parallel save of {len(df)} items to '{table_name}'...[/cyan]")
        total_items = len(df)
        if total_items == 0:
            self.console.print("[yellow]No items to save to DynamoDB.[/yellow]")
            return

        if not self.dockets_db_resource:
            self.console.print(
                "[bold red]ERROR: DynamoDB resource not available for parallel save. Skipping.[/bold red]")
            return

        # Determine chunk size. Max 25 items per batch_writer operation.
        # Larger chunks for the worker to process internally via batch_writer.
        chunk_size = 1000  # Each worker will process this many rows via its own batch_writer
        num_chunks = (total_items + chunk_size - 1) // chunk_size

        saved_items_count = 0

        with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                TextColumn("({task.completed} of {task.total} items saved)"),
                TimeElapsedColumn(),
                console=self.console,
                transient=False
        ) as progress:
            save_task = progress.add_task(f"Saving to '{table_name}'", total=total_items)

            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []
                for i in range(num_chunks):
                    start_idx = i * chunk_size
                    end_idx = min((i + 1) * chunk_size, total_items)
                    chunk = df.iloc[start_idx:end_idx]
                    if not chunk.empty:
                        futures.append(executor.submit(self._save_chunk_to_dynamodb, chunk, table_name, save_task))

                for future in concurrent.futures.as_completed(futures):
                    try:
                        items_in_chunk = future.result()
                        saved_items_count += items_in_chunk
                        progress.update(save_task, advance=items_in_chunk)  # Advance by actual items saved
                    except Exception as e:
                        if self.logger:
                            self.logger.error(f"A save chunk future completed with error: {e}", exc_info=True)
                        self.console.print(f"[red]Error processing a save chunk result: {e}[/red]")

        self.console.print(f"[bold green]Successfully saved {saved_items_count} items to '{table_name}'.[/bold green]")

    def prepare_pacer_dockets_environment(self) -> pd.DataFrame | None:
        """
        Loads Pacer data into a DataFrame, creates the PacerDockets table in local DynamoDB,
        and saves the DataFrame contents to the new table in parallel.
        Returns the DataFrame if successfully loaded.
        """
        self.console.print("[bold magenta]Preparing Pacer Dockets environment...[/bold magenta]")

        self.load_pacer_data()

        if not self.pacer_items:
            self.console.print("[yellow]No Pacer data loaded for DataFrame creation or saving.[/yellow]")
            return None

        if not self.dockets_db_client or not self.dockets_db_resource:
            self.console.print(
                "[bold red]CRITICAL: Dedicated PacerDockets DB client/resource is not initialized. Cannot create or save to table. Check previous errors in initialization.[/bold red]")
            return None

        self.console.print(f"[cyan]Converting {len(self.pacer_items)} Pacer items to DataFrame...[/cyan]")
        df = pd.DataFrame(self.pacer_items)
        self.console.print(f"[green]DataFrame created with {len(df)} rows and {len(df.columns)} columns.[/green]")

        self._create_pacer_dockets_table()

        table_name = "PacerDockets"
        try:
            self.console.print(f"[yellow]Waiting for table '{table_name}' to become active before saving...[/yellow]")
            waiter = self.dockets_db_client.get_waiter('table_exists')
            waiter.wait(TableName=table_name)
            self.console.print(f"[green]Table '{table_name}' is confirmed active for saving.[/green]")
            self._parallel_save_to_dynamodb(df, table_name)
        except Exception as e:
            self.console.print(
                f"[bold red]CRITICAL: Table '{table_name}' not active or error during save preparation: {e}[/bold red]")
            if self.logger:
                self.logger.error(f"Error ensuring table active or preparing save: {e}", exc_info=True)

        return df

    def _parallel_scan_worker(self, segment: int, total_segments: int) -> List[Dict[str, Any]]:
        """Worker to scan a single segment of the Pacer table."""
        if not self.pacer_manager or not self.pacer_manager.table:
            self.console.print("[red]PacerManager/table not available in scan worker. Skipping segment.[/red]")
            return []
        try:
            # scan_table from PacerManager (which uses DynamoDbBaseManager) processes decimals,
            # but *does not* handle string "nan" or comprehensive literal_eval like _deep_clean_value will.
            segment_items = list(self.pacer_manager.scan_table(
                Segment=segment,
                TotalSegments=total_segments
            ))
            return segment_items
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error scanning Pacer segment {segment}: {e}", exc_info=True)
            self.console.print(f"[red]Error in worker for segment {segment}: {e}[/red]")
            return []

    def load_pacer_data(self) -> None:
        """
        Loads data from the local Pacer table in parallel,
        and applies deep cleaning to remove "nan" strings and normalize types.
        """
        if not self.pacer_manager or not self.pacer_manager.table:
            self.console.print(
                "[bold red]PacerManager not available or table not initialized. Cannot load data. Ensure your local Pacer table exists and PacerManager can connect.[/bold red]")
            return

        table_name = "PacerDockets"
        if hasattr(self.pacer_manager, 'table_name') and self.pacer_manager.table_name:
            table_name = f"local Pacer table ('{self.pacer_manager.table_name}')"

        self.console.print(f"[cyan]Starting parallel scan of {table_name}...[/cyan]")
        start_time = time.time()

        total_segments = min(self.max_workers * 2, 50)
        raw_pacer_items: List[Dict[str, Any]] = []
        self.pacer_items = []  # This will store the *cleaned* items

        with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                TextColumn("({task.completed} of {task.total} segments)"),
                TimeElapsedColumn(),
                console=self.console,
                transient=False
        ) as progress:
            scan_task = progress.add_task("Scanning Pacer table", total=total_segments)

            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = [executor.submit(self._parallel_scan_worker, i, total_segments)
                           for i in range(total_segments)]

                for future in concurrent.futures.as_completed(futures):
                    try:
                        segment_results = future.result()
                        if segment_results:
                            raw_pacer_items.extend(segment_results)
                    except Exception as e:
                        if self.logger:
                            self.logger.error(f"A segment future completed with error: {e}", exc_info=True)
                        self.console.print(f"[red]Error processing a segment result: {e}[/red]")
                    progress.update(scan_task, advance=1)

        self.console.print(f"[cyan]Applying deep cleaning to {len(raw_pacer_items)} loaded items...[/cyan]")
        cleaned_count = 0
        with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                TextColumn("({task.completed} of {task.total} items cleaned)"),
                TimeElapsedColumn(),
                console=self.console,
                transient=False
        ) as progress:
            clean_task = progress.add_task("Deep Cleaning Items", total=len(raw_pacer_items))

            for item in raw_pacer_items:
                cleaned_item = self._deep_clean_value(item)
                if cleaned_item is not None:
                    self.pacer_items.append(cleaned_item)
                    cleaned_count += 1
                progress.update(clean_task, advance=1)

        elapsed_time = time.time() - start_time
        if self.pacer_items:
            self.console.print(
                f"[green]Successfully loaded and deep-cleaned {len(self.pacer_items)} items from Pacer table in {elapsed_time:.2f} seconds.[/green]")
        else:
            self.console.print(
                f"[yellow]No items loaded or all filtered out after deep cleaning. Elapsed: {elapsed_time:.2f}s.[/yellow]")

    def _parse_attribute_list(self, attribute_value: Any, item_context: str = "") -> List[str]:
        """
        Safely parses an attribute that might be a list or a string representation of a list.
        Returns a list of strings, normalized to lower case and stripped.
        This is crucial for robust comparison of 'Flags' and 'Defendants'.
        """
        if attribute_value is None:
            return []
        if isinstance(attribute_value, list):
            return [str(x).strip().lower() for x in attribute_value if x is not None and str(x).strip()]

        if isinstance(attribute_value, str):
            val_str = attribute_value.strip()
            if not val_str:
                return []
            try:
                if val_str.startswith('[') and val_str.endswith(']'):
                    parsed_list = ast.literal_eval(val_str)
                    if isinstance(parsed_list, list):
                        return [str(x).strip().lower() for x in parsed_list if x is not None and str(x).strip()]
                    else:
                        if self.logger:
                            self.logger.warning(
                                f"Parsed attribute string '{val_str}' for {item_context} was not a list (type: {type(parsed_list)}). Treating as single element.")
                        return [val_str.lower()]
                else:
                    return [s.strip().lower() for s in val_str.split(',') if s.strip()]
            except (ValueError, SyntaxError, TypeError) as e:
                if self.logger:
                    self.logger.warning(
                        f"Failed to parse attribute string '{val_str}' for {item_context}: {e}. Treating as single element.")
                return [val_str.lower()]

        str_val = str(attribute_value).strip().lower()
        return [str_val] if str_val else []

    def _convert_to_consistent_bool(self, value: Any) -> bool:
        """
        Converts various input types (bool, int, str) to a consistent boolean value.
        """
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            lower_val = value.lower().strip()
            if lower_val in ('true', '1', 't', 'y', 'yes'):
                return True
            if lower_val in ('false', '0', 'f', 'n', 'no', 'na'):
                return False
        if isinstance(value, (int, float)):
            return bool(value)

        return False

    def _clean_item_for_dynamodb(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recursively cleans an item for DynamoDB insertion.
        This method ensures:
        - `float('nan')` (including `numpy.nan` from pandas) and `None` values are removed.
        - `Decimal` objects are converted to `int` or `float`.
        - Specific boolean fields are consistently converted to `bool`.
        - Primary keys, sort keys, and GSI keys are explicitly `str`.
        - `Path` objects are converted to `str`.
        - Nested `dict`s and `list`s are cleaned recursively.
        - `set`s are converted to `list`s (as DynamoDB does not support general sets).
        """
        cleaned_item = {}

        STRING_KEY_ATTRIBUTES = {
            'CourtId', 'DocketNum', 'AddedOn', 'FilingDate', 'MdlNum',
            'TransferorCourtId', 'TransferorDocketNum', 'TransfereeCourtId', 'TransfereeDocketNum'
        }

        for k, v in item.items():
            # Apply initial cleaning common to all values
            # 1. Decimal conversion
            if isinstance(v, Decimal):
                v = self.convert_decimal(v)

            # 2. Convert float('nan') to None
            if isinstance(v, float) and (v != v):  # Check for NaN
                v = None

            # 3. If value is None, skip it entirely for DynamoDB
            if v is None:
                continue

            # Now, handle type-specific conversions for DynamoDB
            if k in ['IsTransferred', 'IsRemoval', 'PendingCto']:
                cleaned_item[k] = self._convert_to_consistent_bool(v)
            elif k in STRING_KEY_ATTRIBUTES:
                cleaned_item[k] = str(v)  # Ensure keys are strings
            elif isinstance(v, Path):
                cleaned_item[k] = str(v)
            elif isinstance(v, dict):
                # Recursively clean nested dictionaries
                cleaned_item[k] = self._clean_item_for_dynamodb(v)
            elif isinstance(v, list):
                # Recursively clean list elements
                cleaned_list = []
                for element in v:
                    # Apply cleaning rules to each element
                    if isinstance(element, Decimal):
                        element = self.convert_decimal(element)
                    if isinstance(element, float) and (element != element):  # Check for NaN
                        element = None

                    if element is None:
                        continue  # Skip None elements

                    if isinstance(element, dict):
                        cleaned_list.append(self._clean_item_for_dynamodb(element))
                    elif isinstance(element, (list, set)):
                        # If a nested list/set, stringify after cleaning its internal elements
                        # to avoid deeply nested complex structures if DynamoDB schema is flat
                        # (This is a design choice; if schema allows deeper lists of lists, adjust)
                        temp_inner_list = []
                        for inner_elem in element:
                            if isinstance(inner_elem, Decimal): inner_elem = self.convert_decimal(inner_elem)
                            if isinstance(inner_elem, float) and (inner_elem != inner_elem): continue  # Skip NaN
                            if inner_elem is not None: temp_inner_list.append(inner_elem)
                        cleaned_list.append(str(temp_inner_list))  # Stringify the inner list/set
                    else:
                        cleaned_list.append(element)

                if cleaned_list:  # Only add non-empty lists
                    cleaned_item[k] = cleaned_list
            elif isinstance(v, set):
                # DynamoDB does not directly support arbitrary sets, convert to a list
                cleaned_list_from_set = []
                for element in v:
                    # Apply cleaning rules to each element in the set (will become list element)
                    if isinstance(element, Decimal):
                        element = self.convert_decimal(element)
                    if isinstance(element, float) and (element != element):  # Check for NaN
                        element = None

                    if element is None:
                        continue  # Skip None elements

                    if isinstance(element, dict):
                        cleaned_list_from_set.append(self._clean_item_for_dynamodb(element))
                    elif isinstance(element, (list, set)):
                        temp_inner_list = []
                        for inner_elem in element:
                            if isinstance(inner_elem, Decimal): inner_elem = self.convert_decimal(inner_elem)
                            if isinstance(inner_elem, float) and (inner_elem != inner_elem): continue  # Skip NaN
                            if inner_elem is not None: temp_inner_list.append(inner_elem)
                        cleaned_list_from_set.append(str(temp_inner_list))
                    else:
                        cleaned_list_from_set.append(element)

                if cleaned_list_from_set:  # Only add non-empty lists from sets
                    cleaned_item[k] = cleaned_list_from_set
            else:
                # For all other types already cleaned by _deep_clean_value
                cleaned_item[k] = v

        return cleaned_item

    @staticmethod
    def _check_list_contains_any_substring(target_list: List[str], keywords: List[str]) -> bool:
        """
        Checks if any string in target_list contains any of the provided keywords as a substring.
        All comparisons are case-insensitive.
        """
        keywords_lower = [k.lower() for k in keywords]
        for item in target_list:
            for keyword in keywords_lower:
                if keyword in item:
                    return True
        return False

    @staticmethod
    def _check_list_contains_all_substrings(target_list: List[str], keywords: List[str]) -> bool:
        """
        Checks if for each keyword, there is at least one string in target_list that contains it as a substring.
        All comparisons are case-insensitive.
        """
        keywords_lower = [k.lower() for k in keywords]
        for keyword in keywords_lower:
            found_keyword = False
            for item in target_list:
                if keyword in item:
                    found_keyword = True
                    break
            if not found_keyword:
                return False
        return True

    def analyze_and_filter_mdl_data(self) -> None:
        """
        Filters loaded Pacer data based on predefined MDL cleaning rules.
        Stores matching items for each rule and reports which rules were not found.
        """
        if not self.pacer_items:
            self.console.print("[yellow]No Pacer data loaded. Skipping MDL filtering.[/yellow]")
            return

        self.console.print("[cyan]Applying MDL cleaning rules and filtering data...[/cyan]")
        self.filtered_results = defaultdict(list)

        rules = [
            ("Rule 1: MDL 3084 & Defendants ('Chemgaurd' OR '3M')",
             lambda item, mdl_num, defendants, flags:
             mdl_num == '3084' and self._check_list_contains_any_substring(defendants, ['Chemgaurd', '3M'])),

            ("Rule 2: MDL 2738 & Defendants (ALL Uber/Rasier entities)",
             lambda item, mdl_num, defendants, flags:
             mdl_num == '2738' and self._check_list_contains_all_substrings(defendants,
                                                                            ["Uber Technologies, Inc.", "Rasier, LLC",
                                                                             "Rasier-CA, LLC"])),

            ("Rule 3: MDL 2570 & Defendants (ALL Bard/Becton entities)",
             lambda item, mdl_num, defendants, flags:
             mdl_num == '2570' and self._check_list_contains_all_substrings(defendants,
                                                                            ["Bard Peripheral Vascular, Inc.",
                                                                             "Becton, Dickinson and Company",
                                                                             "C.R. Bard, Inc."])),

            ("Rule 4: MDL 2573 & Defendants ('Maquet Cardiovascular US Sales, LLC')",
             lambda item, mdl_num, defendants, flags:
             mdl_num == '2573' and self._check_list_contains_any_substring(defendants,
                                                                           ["Maquet Cardiovascular US Sales, LLC"])),

            ("Rule 5: MDL 3087 & Defendants ('Show, Inc.')",
             lambda item, mdl_num, defendants, flags:
             mdl_num == '3087' and self._check_list_contains_any_substring(defendants, ["Show, Inc."])),

            ("Rule 6: MDL 3014 & Defendants ('Wm. T. Burnett & Co.')",
             lambda item, mdl_num, defendants, flags:
             mdl_num == '3014' and self._check_list_contains_any_substring(defendants, ["Wm. T. Burnett & Co."])),

            ("Rule 7: MDL 02610 (Exact Match)",
             lambda item, mdl_num, defendants, flags:
             mdl_num == '02610'),

            ("Rule 8: Missing MDL & Flags ('MDL 3026' OR 'MDL 3037')",
             lambda item, mdl_num, defendants, flags:
             (mdl_num == '' or mdl_num.upper() == "NA") and self._check_list_contains_any_substring(flags, ["MDL 3026",
                                                                                                            "MDL 3037"])),

            ("Rule 9: MDL 2857 OR 2197 (Exact Match)",
             lambda item, mdl_num, defendants, flags:
             mdl_num in ['2857', '2197']),

            ("Rule 10: MDL 3109 (Exact Match)",
             lambda item, mdl_num, defendants, flags:
             mdl_num == '3109'),
        ]

        with Progress(
                SpinnerColumn(), TextColumn("[progress.description]{task.description}"), BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                TextColumn("({task.completed} of {task.total} items)"), TimeElapsedColumn(),
                console=self.console, transient=False
        ) as progress:
            filter_task = progress.add_task("Filtering Pacer items", total=len(self.pacer_items))

            for item in self.pacer_items:
                mdl_num_raw = item.get('MdlNum')
                mdl_num = str(mdl_num_raw).strip() if mdl_num_raw is not None else ""

                flags_val = item.get('Flags')
                defendants_val = item.get('Defendants')

                item_context_key = f"item (PK: {item.get('FilingDate', 'N/A')}/{item.get('DocketNum', 'N/A')})"

                parsed_flags = self._parse_attribute_list(flags_val, f"Flags for {item_context_key}")
                parsed_defendants = self._parse_attribute_list(defendants_val, f"Defendants for {item_context_key}")

                for rule_name, rule_func in rules:
                    try:
                        if rule_func(item, mdl_num, parsed_defendants, parsed_flags):
                            self.filtered_results[rule_name].append(item)
                    except Exception as e:
                        if self.logger:
                            self.logger.error(f"Error applying rule '{rule_name}' to {item_context_key}: {e}",
                                              exc_info=True)
                        self.console.print(f"[red]Error applying rule '{rule_name}' to {item_context_key}: {e}[/red]")
                progress.update(filter_task, advance=1)

        self.console.print("[green]Filtering complete.[/green]")
        self.console.rule("[bold blue]Filtering Results Summary[/bold blue]")

        matched_rule_messages = []
        unmatched_rule_messages = []

        all_rule_names = [rule_name for rule_name, _ in rules]

        for rule_name in all_rule_names:
            items_for_rule = self.filtered_results.get(rule_name, [])

            if items_for_rule:
                matched_rule_messages.append(
                    f"[bold green]Rule: {rule_name}[/bold green] - [green]Found {len(items_for_rule)} matching items.[/green]")
            else:
                unmatched_rule_messages.append(
                    f"[bold yellow]Rule: {rule_name}[/bold yellow] - [yellow]No matching items found.[/yellow]")

        if matched_rule_messages:
            self.console.print("\n[bold]--- Rules with Matches ---[/bold]")
            for msg in matched_rule_messages:
                self.console.print(msg)
        else:
            self.console.print("[yellow]No items matched any of the specified cleaning rules.[/yellow]")

        if unmatched_rule_messages:
            self.console.print("\n[bold]--- Rules with No Matches ---[/bold]")
            for msg in unmatched_rule_messages:
                self.console.print(msg)

        self.console.print("\n[dim]All matching items will be saved to 'mdl_cleaning.json'.[/dim]")

    def save_results(self, filename: str = "mdl_cleaning.json") -> None:
        """Saves the filtered results to a JSON file."""
        if not self.filtered_results:
            self.console.print("[yellow]No filtered data to save.[/yellow]")
            return

        results_to_save = {
            rule_name: items
            for rule_name, items in self.filtered_results.items()
            if items
        }

        if not results_to_save:
            self.console.print("[yellow]No items matched any rule to save to JSON.[/yellow]")
            return

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results_to_save, f, indent=2, ensure_ascii=False)
            self.console.print(f"[green]Filtered data successfully saved to '{filename}'.[/green]")
        except Exception as e:
            self.console.print(f"[bold red]Error saving data to '{filename}': {e}[/bold red]")

    def interactive_loop(self) -> None:
        """
        Provides an interactive command-line interface for the Pacer MDL Cleaner.
        """
        if not self.pacer_manager:
            self.console.print(
                "[bold red]PacerManager could not be initialized. Analyzer cannot run. Check import and initialization errors.[/bold red]")
            return

        self.console.print("[bold cyan]Pacer MDL Cleaner Initialized (interactive loop).[/bold cyan]")
        loaded_df = None

        while True:
            self.console.print("\n[bold cyan]Select an option:[/bold cyan]")
            self.console.print("1. Load Pacer Table Data (from Local DynamoDB)")
            self.console.print("2. Analyze and Filter MDL Data (output to mdl_cleaning.json)")
            self.console.print(
                "3. Prepare Pacer Dockets Environment (Load data, Create PacerDockets Table, & Save Data)")
            self.console.print("q. Quit")

            choice = Prompt.ask("Select an option", choices=["1", "2", "3", "q", "Q"], default="1",
                                console=self.console)

            if choice == "1":
                self.load_pacer_data()
            elif choice == "2":
                self.analyze_and_filter_mdl_data()
                self.save_results("mdl_cleaning.json")
            elif choice == "3":
                # Ensure DynamoDB client/resource is available BEFORE attempting
                # to prepare environment.
                if not self.dockets_db_client or not self.dockets_db_resource:
                    self.console.print(
                        "[bold red]Cannot prepare environment: DynamoDB clients/resources are not initialized. Please check previous error messages regarding DynamoDB connection.[/bold red]")
                    continue  # Go back to menu

                loaded_df = self.prepare_pacer_dockets_environment()
                if loaded_df is not None:
                    self.console.print(
                        f"[green]DataFrame loaded and potentially saved successfully. First 5 rows:[/green]")
                    self.console.print(loaded_df.head().to_string())
            elif choice.lower() == "q":
                self.console.print("[bold]Exiting Pacer MDL Cleaner.[/bold]")
                break
            else:
                self.console.print("[red]Invalid choice. Please try again.[/red]")


def main():
    import logging

    # Configure basic logging; PacerManager might use this.
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                        handlers=[logging.StreamHandler(sys.stdout)])

    # Silence overly verbose loggers from external libraries
    logging.getLogger('boto3').setLevel(logging.WARNING)
    logging.getLogger('botocore').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

    # Use a direct rich.console for the initial main message
    Console().print("[bold green]Starting Pacer MDL Cleaner application...[/bold green]")

    cleaner = PacerMdlCleaner(max_workers=os.cpu_count() or 4)

    cleaner.interactive_loop()


if __name__ == "__main__":
    main()