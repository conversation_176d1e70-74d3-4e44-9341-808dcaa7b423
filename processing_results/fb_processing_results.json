{"failed_firms": [{"ID": "171590176191415", "Name": "The Yost Legal Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250421"]}, {"ID": "572100006190208", "Name": "<PERSON> PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250406", "20250412", "20250425"]}, {"ID": "394505647070902", "Name": "Case Legal Media", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250406", "20250427"]}, {"ID": "136411669549999", "Name": "Robins Cloud LLP / Royal-Mack & Cordova PC", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "433256763210956", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413"]}, {"ID": "123132371126483", "Name": "Lipsitz, Ponterio & Comerford LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412", "20250422"]}, {"ID": "296357023566483", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "368344253030922", "Name": "Legal Settlement For Children", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250406", "20250413"]}, {"ID": "360632997130427", "Name": "Weitz & Luxenberg PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250427"]}, {"ID": "178769718655979", "Name": "Blue Sky Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250427"]}, {"ID": "324733975945", "Name": "Weitz & Luxenberg PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250401", "20250405", "20250407", "20250415"]}, {"ID": "539014875958290", "Name": "LS5 Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250415", "20250421", "20250423", "20250503"]}, {"ID": "273570285996048", "Name": "Schmidt Law Firm, PLLC", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "12830064274", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250406", "20250419", "20250422", "20250429"]}, {"ID": "444082182129763", "Name": "Watts Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250508"]}, {"ID": "1667434743521564", "Name": "The Sentinel Group", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "180941071763671", "Name": "Keller <PERSON>man Injury Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413"]}, {"ID": "100738128726172", "Name": "Sokolove Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250428"]}, {"ID": "510803552738279", "Name": "<PERSON>s Cloud LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250428", "20250429"]}, {"ID": "109558614834438", "Name": "Stop Consumer Harm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250414"]}, {"ID": "101968656177901", "Name": "Case Legal Media", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250410"]}, {"ID": "408229205710153", "Name": "Tort Group LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250407", "20250411", "20250415"]}, {"ID": "491534547386685", "Name": "Feller <PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250326", "20250411"]}, {"ID": "290827161037824", "Name": "<PERSON><PERSON>y, Bucci & Kent, LLP", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "427641727105546", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250427"]}, {"ID": "274747559049795", "Name": "Landfill Justice", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250415"]}, {"ID": "453289314535295", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250404", "20250407", "20250412", "20250413", "20250415"]}, {"ID": "183449754850227", "Name": "Broughton Partners", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250427"]}, {"ID": "301100319750499", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412", "20250414"]}, {"ID": "262673803585177", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250425"]}, {"ID": "122574865054518", "Name": "<PERSON><PERSON><PERSON>, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250415", "20250418", "20250421", "20250426", "20250429"]}, {"ID": "162615037095776", "Name": "MCT Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250420", "20250421", "20250428", "20250430"]}, {"ID": "517599365381041", "Name": "The Braslow Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412"]}, {"ID": "342754878931793", "Name": "Case Legal Media", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412"]}, {"ID": "61569120780188", "Name": "Bursor & Fisher PA", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "124621759456", "Name": "Aylstock, Witkin, Kreis & Overholtz", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409", "20250415", "20250428"]}, {"ID": "93356843939", "Name": "Hughes & Coleman Injury Lawyers", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250404", "20250413"]}, {"ID": "103254958861107", "Name": "Brouhgton Partners", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250410", "20250415", "20250419", "20250424", "20250508"]}, {"ID": "747524905257677", "Name": "Fibich, Leebron, Copeland & Briggs", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250411", "20250412", "20250425"]}, {"ID": "369154569618670", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250415"]}, {"ID": "461461240377447", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413"]}, {"ID": "615789048540160", "Name": "<PERSON> Attorneys at Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412"]}, {"ID": "107086532382051", "Name": "NIB", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412", "20250413"]}, {"ID": "2074497659232877", "Name": "Rosen Injury Lawyers", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "108679592280575", "Name": "Martin & Davis Injury Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250407", "20250413", "20250427"]}, {"ID": "100556646129635", "Name": "The RH Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412", "20250419", "20250428", "20250430"]}, {"ID": "403478006192607", "Name": "Troxel Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250409"]}, {"ID": "291843034020037", "Name": "Weitz & Luxenberg PC", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "112298350556261", "Name": "Justpoint", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250426", "20250505", "20250516", "20250518"]}, {"ID": "499415716582390", "Name": "Justpoint", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413"]}, {"ID": "215455481644178", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "110161308606738", "Name": "Mass Tort Strategies", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413"]}, {"ID": "553596357833546", "Name": "Shapiro Legal Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250414", "20250508"]}, {"ID": "912425535601595", "Name": "Fiore Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250417", "20250430"]}, {"ID": "100258746254661", "Name": "Case Legal Media", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "413219001884969", "Name": "Ashcraft & Gerel, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412", "20250421"]}, {"ID": "164248630306303", "Name": "The Nations Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409", "20250413"]}, {"ID": "104912525852233", "Name": "Almeida Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250425"]}, {"ID": "520924724438292", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250409", "20250412", "20250417", "20250422", "20250426", "20250508"]}, {"ID": "416709304858490", "Name": "Allied Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413"]}, {"ID": "103392219156533", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250418", "20250428", "20250508"]}, {"ID": "290932574644424", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250428"]}, {"ID": "212964082092054", "Name": "<PERSON><PERSON><PERSON><PERSON> Montgomery LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409", "20250415", "20250518"]}, {"ID": "246064115495860", "Name": "The Driscoll Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250425", "20250427"]}, {"ID": "132688979930490", "Name": "Allied Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250412", "20250427"]}, {"ID": "667317530043728", "Name": "Consumer Safety Watch", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250406", "20250420", "20250429"]}, {"ID": "109243472006968", "Name": "Broughton Partners", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413"]}, {"ID": "100148265769122", "Name": "Shield Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409", "20250413"]}, {"ID": "108274174275729", "Name": "Reliance Litigation", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413"]}, {"ID": "391510997660333", "Name": "<PERSON> Worley PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250410", "20250412"]}, {"ID": "101799133240754", "Name": "Jalilvand Law Corporation", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250326", "20250411"]}, {"ID": "387766944430241", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250425"]}, {"ID": "422742060912283", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413"]}, {"ID": "470169939516508", "Name": "Baron & Budd PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250518"]}, {"ID": "108811365091069", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250410", "20250413", "20250427", "20250429"]}, {"ID": "530162316835979", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250414", "20250415", "20250424", "20250508"]}, {"ID": "436781836190723", "Name": "Pulaski Kherker PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405"]}, {"ID": "256153444247576", "Name": "Gibbs Law Group LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250415", "20250420", "20250429"]}, {"ID": "1687940978108979", "Name": "Miller Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250411", "20250429", "20250508"]}, {"ID": "569633489731920", "Name": "Attorney Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250407", "20250412", "20250427", "20250508"]}, {"ID": "308570855538", "Name": "Motley Rice", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409"]}, {"ID": "388853754320035", "Name": "Keller Postman LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250415"]}, {"ID": "271392009871", "Name": "Pogust Goodhead US", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250429"]}, {"ID": "104136289375152", "Name": "Tort Defenders", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412", "20250420", "20250429"]}, {"ID": "456141487913366", "Name": "TruLaw", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250406", "20250409", "20250424"]}, {"ID": "105163544339024", "Name": "Barnow and Associates, P.C.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250425"]}, {"ID": "1686716458216383", "Name": "Goldwater Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409"]}, {"ID": "2320726594644136", "Name": "Scott+<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412", "20250420"]}, {"ID": "215039935184588", "Name": "Ratzan Weissman & Boldt", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "423032228218961", "Name": "Valles Law Firm PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250407", "20250412", "20250413", "20250425"]}, {"ID": "489671624569463", "Name": "Jason Joy & Associates, PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250426"]}, {"ID": "217164058361304", "Name": "The Spencer Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409", "20250419"]}, {"ID": "513770465161679", "Name": "Alan Beck & Partners", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "217991901392200", "Name": "Pintas & Mullins", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "102670742794826", "Name": "PPC Squared", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "158562757337561", "Name": "Allied Law", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "216602451542768", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250427"]}, {"ID": "110958371093865", "Name": "NIB", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250413"]}, {"ID": "104578099411173", "Name": "All Tort Solutions", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250425"]}, {"ID": "379298805522053", "Name": "Frantz Law Group, APLC", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250326", "20250411"]}, {"ID": "385836538694261", "Name": "The Fitzgerald Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250506"]}, {"ID": "138883376140482", "Name": "Sullivan Papain Block McManus Coffinas & Cannavo P.C.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250427", "20250429", "20250503"]}, {"ID": "478317085355330", "Name": "NIB", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "546484218545805", "Name": "Suboxone Film Lawsuit: Justpoint Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413"]}, {"ID": "101209345169885", "Name": "Keller Postman LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250410", "20250425"]}, {"ID": "608256235874723", "Name": "Meyers & Flowers", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409", "20250428"]}, {"ID": "111179558089932", "Name": "<PERSON> Attorneys at Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250413"]}, {"ID": "105745892510526", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250422"]}, {"ID": "474116129126321", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409", "20250427"]}, {"ID": "108584328300764", "Name": "Social Media Victims Law Center", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250418", "20250428", "20250429", "20250430"]}, {"ID": "100340806304880", "Name": "Nigh Goldenberg Raso & Vaughn PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250425"]}, {"ID": "104878091120684", "Name": "<PERSON>, PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412", "20250417", "20250503"]}, {"ID": "225144284011706", "Name": "Mendez & Sanchez APC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250407", "20250420", "20250429", "20250430"]}, {"ID": "286656651190028", "Name": "Watts Law Firm LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250401", "20250415", "20250417", "20250423", "20250426", "20250428", "20250429", "20250518"]}, {"ID": "1871295253123170", "Name": "<PERSON><PERSON><PERSON> Injury <PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250406", "20250413", "20250425"]}, {"ID": "105459947922475", "Name": "Sheldon Law Group", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "125123434167781", "Name": "<PERSON>, LLP Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412", "20250413", "20250419", "20250427", "20250428"]}, {"ID": "102312344711237", "Name": "Vigna Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250407", "20250504"]}, {"ID": "233697937141864", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405"]}, {"ID": "428357133702200", "Name": "AdWire Media", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250425"]}, {"ID": "134957179700989", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "131222893607517", "Name": "W<PERSON>ley Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250427", "20250509"]}, {"ID": "452737447921563", "Name": "Aaronson & Rash", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "146388715410491", "Name": "Drugwatch", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250415", "20250420", "20250423", "20250424", "20250508"]}, {"ID": "348606649169958", "Name": "Fegan Scott LLC", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250326", "20250411", "20250413", "20250419", "20250426"]}, {"ID": "292723181666209", "Name": "Sheldon Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250410", "20250411", "20250415"]}, {"ID": "134515036700219", "Name": "Hearn Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250428", "20250505", "20250513"]}, {"ID": "537209516139946", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250410", "20250413", "20250415"]}, {"ID": "181053038667348", "Name": "Cole & Van Note", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250407", "20250425"]}, {"ID": "108804601593444", "Name": "NIB", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250407"]}, {"ID": "474883502562105", "Name": "The Miller Law Firm P.C.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250409", "20250423", "20250428", "20250429"]}, {"ID": "408977202917590", "Name": "<PERSON><PERSON>, LLP", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "1890460624378949", "Name": "Wright & Schulte LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409", "20250413", "20250415", "20250418", "20250423"]}, {"ID": "167104866494201", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409", "20250410", "20250428"]}, {"ID": "130709230604064", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250426"]}, {"ID": "779309092233298", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412"]}, {"ID": "101170392891953", "Name": "NIB", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250412"]}, {"ID": "547265605127007", "Name": "<PERSON> Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250415", "20250516"]}, {"ID": "100069561416960", "Name": "Shield Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250425"]}, {"ID": "554656651055666", "Name": "Pulaski Law Firm / Garza Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250413"]}, {"ID": "156913404373893", "Name": "Carey Danis & Lowe", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250326", "20250406", "20250411"]}, {"ID": "170434479676750", "Name": "Pintas & Mullins Law Firm", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "202056713289203", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "313003965229540", "Name": "Weitz & Luxenberg PC", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "112502702127622", "Name": "Law Offices of <PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250406"]}, {"ID": "106469240890813", "Name": "NIB", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250427"]}, {"ID": "100744029656861", "Name": "Saddle Rock Legal Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413"]}, {"ID": "139318532609384", "Name": "Diablo Media / Law Scout", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250419", "20250423", "20250430", "20250506"]}, {"ID": "102197802513455", "Name": "<PERSON>lman Law Group Accident Injury Lawyers, PA", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250422", "20250424", "20250430"]}, {"ID": "1719010998386079", "Name": "Fitzgerald Law Group, LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250401", "20250414", "20250421", "20250422", "20250426", "20250427", "20250429", "20250508"]}, {"ID": "270551333101864", "Name": "Childers, Schlueter & Smith, LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250425"]}, {"ID": "1094134943976769", "Name": "The Consumer Protection Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250410"]}, {"ID": "1560539887516421", "Name": "<PERSON><PERSON> Kaplan <PERSON>", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "536781276178646", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250407"]}, {"ID": "279773092034036", "Name": "Florin <PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250419"]}, {"ID": "1589261434655726", "Name": "Advocate Alliance Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250401", "20250404", "20250407", "20250410", "20250411", "20250430"]}, {"ID": "361653344319447", "Name": "Wagstaff & Cartmell LLP", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "383276498212765", "Name": "Money Team Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250415"]}, {"ID": "112133755142471", "Name": "Shield Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250504", "20250508"]}, {"ID": "412299685311383", "Name": "Depo-Provera Litigation Indiana by Ciobanu Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250407"]}, {"ID": "169463988510253", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250428", "20250506", "20250508"]}, {"ID": "254705261056584", "Name": "GP Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250404"]}, {"ID": "556842674174070", "Name": "Troxel Law LLP", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250326", "20250415"]}, {"ID": "832929756896250", "Name": "<PERSON><PERSON><PERSON> LLP", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "150405215029000", "Name": "Baron & Budd PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250407"]}, {"ID": "105630735935186", "Name": "<PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "540813312450374", "Name": "<PERSON> Law PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250425", "20250427"]}, {"ID": "475744282281002", "Name": "Keller Postman LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412", "20250425"]}, {"ID": "168711849812675", "Name": "Golomb • Spirt P.C.", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "102907127873927", "Name": "Diablo Media / Law Scout", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250413"]}, {"ID": "147456421957526", "Name": "<PERSON><PERSON>wati Law Group", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "358950333967679", "Name": "Weitz & Luxenberg PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405", "20250413", "20250425"]}, {"ID": "543156668889083", "Name": "<PERSON><PERSON> LLP ", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250427"]}, {"ID": "107588885493152", "Name": "NIB", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250412", "20250413", "20250425"]}, {"ID": "384411538085841", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409"]}, {"ID": "255894697603308", "Name": "Ozempic Advocates", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250427"]}, {"ID": "331120893428354", "Name": "The Sentinel Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250425"]}, {"ID": "687410351316117", "Name": "Finkelstein, Blankinship, Frei-Pearson & Garber, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250429"]}, {"ID": "453782291150728", "Name": "Stinar Gould Grieco & Hensley", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250409", "20250413"]}, {"ID": "415893774950156", "Name": "Sheldon Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250401", "20250404", "20250405", "20250406", "20250407", "20250409", "20250410", "20250411", "20250418", "20250420", "20250428", "20250430", "20250518"]}, {"ID": "61567753417724", "Name": "Allied Law", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "127435673950066", "Name": "Slater & Zurz LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250427", "20250428", "20250429"]}, {"ID": "854603314672686", "Name": "The Cates Law Firm, LLC", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "113947198676115", "Name": "The Law Offices of Federman & Sherwood - Oklahoma City and Dallas", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250410", "20250412", "20250428", "20250519"]}, {"ID": "514866818377216", "Name": "Money Team Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250401", "20250409", "20250424"]}, {"ID": "102384191140881", "Name": "<PERSON><PERSON>man Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250414", "20250424", "20250508"]}, {"ID": "178322762199979", "Name": "<PERSON> Attorneys", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250415", "20250426", "20250428", "20250510"]}, {"ID": "404692206052802", "Name": "Case Legal Media", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "122453908781", "Name": "<PERSON><PERSON><PERSON> Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250405"]}, {"ID": "162213204363587", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "131349460222943", "Name": "The Buchanan Law Office, P.C.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250427"]}, {"ID": "113337660435521", "Name": "Reliance Litigation", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250427"]}, {"ID": "380440671813232", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250425"]}, {"ID": "633314823410650", "Name": "<PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250414", "20250415", "20250421"]}, {"ID": "464669966735109", "Name": "TSEG", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250416", "20250420"]}, {"ID": "123091144394339", "Name": "Law offices of Rudolph F<PERSON>, P.C.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250413", "20250419", "20250421"]}, {"ID": "486813011185575", "Name": "Schlichter Bogard", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250508"]}, {"ID": "103831367914768", "Name": "Broughton Partners", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250427"]}, {"ID": "337832014811", "Name": "Ashcraft & Gerel, LLP", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250326"]}, {"ID": "590498404372635", "Name": "Shamis & Gentile, P.A.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250326", "20250401", "20250404", "20250405", "20250406", "20250407", "20250409", "20250410", "20250411", "20250501", "20250508", "20250518"]}, {"ID": "105595725296103", "Name": "Saddle Rock Legal Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250330", "20250409", "20250414"]}, {"ID": "125907387426944", "Name": "Edelson P.C.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250404", "20250410", "20250411", "20250413", "20250418", "20250427", "20250429"]}, {"ID": "106518951019913", "Name": "Farrell & Fuller", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250415"]}, {"ID": "110450935166997", "Name": "Gados Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250407", "20250411", "20250420", "20250504"]}, {"ID": "105332609316430", "Name": "In<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250410", "20250415", "20250418"]}, {"ID": "441392479066870", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250504"]}, {"ID": "537105702812748", "Name": "NIB", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250405", "20250414", "20250423", "20250428", "20250516"]}, {"ID": "129342070251791", "Name": "Mass Tort Intake Center", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250429", "20250516"]}, {"ID": "565195703541743", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250405", "20250427"]}, {"ID": "111404133327", "Name": "<PERSON><PERSON><PERSON>, Injury Attorneys", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250518"]}, {"ID": "111436228168536", "Name": "Grant & Eisenhofer P.A.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250411", "20250413", "20250415", "20250418", "20250429", "20250430"]}, {"ID": "295985383057", "Name": "Greenberg & Bederman, LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250404", "20250407", "20250419", "20250508"]}, {"ID": "103516641702977", "Name": "Storms Dworak LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401"]}, {"ID": "903351869752606", "Name": "Bridgford, Gleason & Artinian", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250413", "20250429"]}, {"ID": "189439140917262", "Name": "Diablo Media / Law Scout", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250427"]}, {"ID": "106003988701851", "Name": "Makarem & Associates", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250419", "20250508"]}, {"ID": "373393466603080", "Name": "Consumers Protection Law", "Error": "FBAdArchive DB Save/Upsert Failed for 1 items (check logs)", "Date": ["20250401", "20250404", "20250405", "20250407", "20250410", "20250413", "20250429", "20250502"]}, {"ID": "1150093035177691", "Name": "Hissey, Mulderig & Friend", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401"]}, {"ID": "313942829424", "Name": "Morgan & Morgan", "Error": "FBAdArchive DB Save/Upsert Failed for 25 items (check logs)", "Date": ["20250401", "20250404", "20250405", "20250406", "20250407", "20250409", "20250410", "20250416", "20250514", "20250515"]}, {"ID": "452804281259609", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250413", "20250418", "20250425", "20250426", "20250427"]}, {"ID": "123651254408327", "Name": "Sokolove Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250406", "20250409", "20250428", "20250503"]}, {"ID": "770082826348904", "Name": "Clarkson Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250404", "20250410", "20250419", "20250428"]}, {"ID": "443225505533171", "Name": "X Social Media", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250404", "20250405", "20250406", "20250407", "20250409", "20250410", "20250414", "20250501", "20250508"]}, {"ID": "476051432261493", "Name": "Bursor & Fisher PA", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250401", "20250405", "20250506"]}, {"ID": "1467020753587372", "Name": "Heidari Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250404", "20250415", "20250423", "20250425", "20250506", "20250508"]}, {"ID": "76535259356", "Name": "Farah and Farah", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250404", "20250407", "20250422"]}, {"ID": "114294708334472", "Name": "Bursor & Fisher PA", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250404", "20250425", "20250428"]}, {"ID": "75879583894", "Name": "<PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250404", "20250413", "20250505"]}, {"ID": "520685411126638", "Name": "Tort Defenders", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250404", "20250407", "20250504", "20250518"]}, {"ID": "144657095647733", "Name": "Downtown L.A. Law Group", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250404", "20250411"]}, {"ID": "1439962152903216", "Name": "<PERSON><PERSON><PERSON> Kane Conway & Wise", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250404", "20250411", "20250428"]}, {"ID": "250784648112081", "Name": "Ferrell Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250404", "20250410", "20250508", "20250518"]}, {"ID": "332850209918493", "Name": "Weitz & Luxenberg PC", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250404"]}, {"ID": "199303123272627", "Name": "Broughton Partners", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250404", "20250411", "20250428", "20250506"]}, {"ID": "100525535472448", "Name": "Bursor & Fisher PA", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250404", "20250416", "20250428", "20250508"]}, {"ID": "568119339709547", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250516", "20250518"]}, {"ID": "1699467556946703", "Name": "Kwartler Manus LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250406", "20250412"]}, {"ID": "145825141947708", "Name": "Legal Claim Assistant", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250405", "20250413"]}, {"ID": "185193864836741", "Name": "Cotchett, Pitre & McCarthy, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250415", "20250428", "20250430", "20250504"]}, {"ID": "119623954748526", "Name": "Owen, Patterson & Owen Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250410", "20250425", "20250426"]}, {"ID": "111828843535650", "Name": "<PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250412", "20250418", "20250429"]}, {"ID": "198683619191", "Name": "Messa & Associates", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250407", "20250410", "20250415", "20250419", "20250428"]}, {"ID": "190842507655668", "Name": "Cohen & Malad LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250413", "20250414", "20250418", "20250420", "20250423", "20250429", "20250504", "20250508"]}, {"ID": "108003550007991", "Name": "Sauder Schelkopf LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405"]}, {"ID": "316217208251244", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250407", "20250410"]}, {"ID": "719913581472398", "Name": "Napoli Shkolnik", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250426"]}, {"ID": "107669727482334", "Name": "Mason LLP", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250405", "20250409", "20250411"]}, {"ID": "367131863140591", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250420", "20250423"]}, {"ID": "551377124717650", "Name": "The Driscoll Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250409", "20250425", "20250426"]}, {"ID": "106974561225210", "Name": "Clapp & Lauinger LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250415", "20250418"]}, {"ID": "161509293715318", "Name": "<PERSON>man Injury Law", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250405", "20250423"]}, {"ID": "627152827369095", "Name": "KJT Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405"]}, {"ID": "405306419323819", "Name": "Reliance Litigation", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250428"]}, {"ID": "107433804914030", "Name": "Don Bivens PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250406", "20250407", "20250410", "20250518"]}, {"ID": "106272754860582", "Name": "Mass Tort Alliance", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250409", "20250506"]}, {"ID": "104531742072192", "Name": "Saddle Rock Legal Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250405", "20250422"]}, {"ID": "308047219048724", "Name": "Matthews & Associates", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250414", "20250428"]}, {"ID": "112921200359331", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406"]}, {"ID": "194692310868947", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250407", "20250410", "20250426", "20250504", "20250518"]}, {"ID": "118539804847886", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250410", "20250414", "20250424"]}, {"ID": "458819923981669", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250410", "20250419", "20250428"]}, {"ID": "249980436765", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250419", "20250421", "20250430"]}, {"ID": "187399111117909", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250426", "20250428", "20250503", "20250518"]}, {"ID": "162836567766", "Name": "The Carlson Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250409", "20250429"]}, {"ID": "436258079571919", "Name": "Broughton Partners", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250412", "20250428", "20250430", "20250508"]}, {"ID": "160435873820500", "Name": "<PERSON><PERSON>ley Brain Stephens PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250416", "20250428", "20250429"]}, {"ID": "143179312376063", "Name": "<PERSON> LL<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250426", "20250429"]}, {"ID": "182023564985353", "Name": "The Stein Law Group PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250407", "20250428"]}, {"ID": "1678111815767974", "Name": "<PERSON>, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250411", "20250429"]}, {"ID": "472495509279826", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250407", "20250411", "20250412", "20250421", "20250428", "20250429", "20250430", "20250506", "20250508"]}, {"ID": "445698711960427", "Name": "Tort Defenders", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250407", "20250409", "20250410", "20250411", "20250413", "20250423"]}, {"ID": "627749827081116", "Name": "Goldwater Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250409", "20250412", "20250417"]}, {"ID": "2026499640963293", "Name": "Wocl Leydon, LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250414", "20250419", "20250422", "20250508"]}, {"ID": "443111702425065", "Name": "<PERSON><PERSON> Keller <PERSON>w LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250406", "20250416", "20250426", "20250428"]}, {"ID": "132426330159243", "Name": "Levi & Korsinsky LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407", "20250411", "20250415", "20250427", "20250430"]}, {"ID": "122559051171703", "Name": "Robinson Calcagnie, Inc.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407", "20250409"]}, {"ID": "567524533341020", "Name": "Bryant Law Center", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407"]}, {"ID": "170677506302459", "Name": "Gervelis Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407", "20250425", "20250429"]}, {"ID": "294044823782921", "Name": "Frantz Law Group, APLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407", "20250411", "20250412"]}, {"ID": "454845101036218", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407", "20250411", "20250412", "20250415", "20250425", "20250427"]}, {"ID": "162616206937313", "Name": "Shine Legal Network", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407", "20250413", "20250419", "20250425", "20250426", "20250429"]}, {"ID": "344686115387884", "Name": "Casefolio", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407"]}, {"ID": "115227421531456", "Name": "Apex Attorneys", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407", "20250409"]}, {"ID": "151978091605961", "Name": "Oliver Bell Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407", "20250409", "20250412", "20250428", "20250429", "20250430"]}, {"ID": "527683030420450", "Name": "Perfected Claims", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407", "20250413"]}, {"ID": "156082241076438", "Name": "<PERSON>, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407", "20250423", "20250428", "20250508"]}, {"ID": "342658772492315", "Name": "<PERSON>hlesinger Law Offices, P.A.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250407", "20250411", "20250412", "20250414"]}, {"ID": "1675911772520247", "Name": "Danziger & De LLano", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250409", "20250413", "20250420"]}, {"ID": "1427193037597544", "Name": "Gibbs Law Group LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250409"]}, {"ID": "104552684923211", "Name": "Shield Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250409", "20250411", "20250513", "20250516"]}, {"ID": "112820508787790", "Name": "ClassAction.org", "Error": "FBAdArchive DB Save/Upsert Failed for 1 items (check logs)", "Date": ["20250409", "20250411", "20250428", "20250501"]}, {"ID": "392505280892129", "Name": "OnderLaw", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250409", "20250429"]}, {"ID": "528560513676712", "Name": "CHJ Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250409"]}, {"ID": "117446868316737", "Name": "<PERSON><PERSON> Injury Attorneys", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250409", "20250429", "20250516"]}, {"ID": "107455248703655", "Name": "Mass Tort Strategies", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250409", "20250413"]}, {"ID": "545781805462366", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250409", "20250413", "20250418"]}, {"ID": "295549338384", "Name": "<PERSON><PERSON><PERSON> Baum", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250409", "20250415", "20250425", "20250428", "20250504"]}, {"ID": "453867107819024", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250410", "20250415", "20250425", "20250427"]}, {"ID": "105408955531306", "Name": "Environmental Litigation Group PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250410", "20250411", "20250428", "20250518"]}, {"ID": "431837476682934", "Name": "Goldwater Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250410", "20250430"]}, {"ID": "288368431020874", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250410", "20250413", "20250508"]}, {"ID": "1520483958208446", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250410"]}, {"ID": "110148417398569", "Name": "Denlea & Carton LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250410", "20250417"]}, {"ID": "150036738356219", "Name": "Leeds Brown Law P.C.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250410", "20250414", "20250422"]}, {"ID": "548720971655182", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250410"]}, {"ID": "872058572913476", "Name": "Robertson & Associates, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250506"]}, {"ID": "377441128775011", "Name": "<PERSON><PERSON><PERSON>ucci D’Andrea <PERSON> & Ryan", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250429", "20250508"]}, {"ID": "162641930438037", "Name": "Tycko & Zavareei LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250423", "20250424"]}, {"ID": "266126583258460", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250413", "20250429"]}, {"ID": "445287115334360", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250413", "20250426", "20250429"]}, {"ID": "195160607020385", "Name": "Legal Case Advisor", "Error": "Failed to fetch ad payloads (NetworkError)", "Date": ["20250411"]}, {"ID": "109175092262457", "Name": "Broughton Partners", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250425"]}, {"ID": "428218373708056", "Name": "Diablo Media / Law Scout", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250425", "20250506"]}, {"ID": "226385600565304", "Name": "Bridge Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250415", "20250422", "20250429", "20250506"]}, {"ID": "257795697418904", "Name": "Kopelowitz Ostrow Ferguson <PERSON> Gilbert", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250413", "20250508"]}, {"ID": "1471741549809576", "Name": "The Flynn Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250427"]}, {"ID": "105117061950191", "Name": "Diablo Media / Law Scout", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250415", "20250508"]}, {"ID": "513525298504138", "Name": "Meshbesher & Spence", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250420"]}, {"ID": "299805050444886", "Name": "<PERSON><PERSON><PERSON><PERSON> Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250411", "20250412", "20250427"]}, {"ID": "165957366591235", "Name": "Aaronson & Rash", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250412", "20250414", "20250420"]}, {"ID": "478178125373324", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250412", "20250419", "20250505"]}, {"ID": "449396962058597", "Name": "Schmidt National Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250412", "20250415", "20250422", "20250503"]}, {"ID": "132963683424040", "Name": "<PERSON> LL<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250412", "20250425", "20250426"]}, {"ID": "470295082837388", "Name": "TSEG", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250412", "20250420", "20250427"]}, {"ID": "173849275810857", "Name": "Becker Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250412", "20250414", "20250427", "20250505"]}, {"ID": "192802980856263", "Name": "Feld<PERSON> <PERSON> Tanner Weinstock Dodig LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250412", "20250413", "20250417", "20250506"]}, {"ID": "102311585474669", "Name": "Starpoint Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250412", "20250424", "20250428"]}, {"ID": "167561156627974", "Name": "Peterson & Associates, P.C.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250412", "20250425", "20250429", "20250508"]}, {"ID": "200387133335232", "Name": "<PERSON><PERSON>r Heimann & Bernstein, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250412", "20250428"]}, {"ID": "321558891250844", "Name": "Hayber, McKenna & Dinsmore, LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250412", "20250413", "20250428"]}, {"ID": "590938747429065", "Name": "Freedl<PERSON> <PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413"]}, {"ID": "510022445537673", "Name": "Sokolove Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250414", "20250429"]}, {"ID": "185748328256863", "Name": "Schwaba Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250414", "20250429", "20250508"]}, {"ID": "100554405803996", "Name": "Shield Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413"]}, {"ID": "1025055787535714", "Name": "A Case for Women", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250419", "20250427", "20250516"]}, {"ID": "105891162806593", "Name": "Hach & Rose, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250518"]}, {"ID": "106641014873190", "Name": "White Heart Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250422"]}, {"ID": "101479542560743", "Name": "CAMG", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250430", "20250508"]}, {"ID": "101543475240818", "Name": "LS5 Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250516"]}, {"ID": "104221008207715", "Name": "Shield Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250418", "20250420", "20250430"]}, {"ID": "432866253583344", "Name": "<PERSON>s Cloud LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250418", "20250428", "20250429"]}, {"ID": "111138423766243", "Name": "Saddle Rock Legal Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250415"]}, {"ID": "512690881919782", "Name": "TruLaw", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413"]}, {"ID": "168513949864339", "Name": "<PERSON><PERSON><PERSON> Accident Injury Lawyers", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250426"]}, {"ID": "359262567280586", "Name": "X Social Media", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250428", "20250504", "20250508", "20250516"]}, {"ID": "325583687452173", "Name": "Bighorn Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250418", "20250428", "20250508"]}, {"ID": "265692306618546", "Name": "Ignition Point, LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250508"]}, {"ID": "103944082393354", "Name": "MLK Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250419"]}, {"ID": "33038675093", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413", "20250415", "20250428", "20250508"]}, {"ID": "399174406622925", "Name": "<PERSON><PERSON> LL<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250413"]}, {"ID": "358177307389884", "Name": "Keller Postman LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250415", "20250429", "20250508"]}, {"ID": "102403108447809", "Name": "Dovel & Luner", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250415", "20250426", "20250516"]}, {"ID": "264015943691065", "Name": "Kazerouni Law Group APC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250420", "20250428", "20250506", "20250509"]}, {"ID": "169456799781938", "Name": "Begum Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250505"]}, {"ID": "505779935959495", "Name": "<PERSON> Law Firm PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250429", "20250506"]}, {"ID": "446784771842651", "Name": "<PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250518"]}, {"ID": "522887614239088", "Name": "Drive Digital Advertising", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250429"]}, {"ID": "1597798397205781", "Name": "Strauss <PERSON>rrelli PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250504"]}, {"ID": "1430369473691855", "Name": "Cochran, Kroll & Associates, P.C.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250518"]}, {"ID": "255518321128513", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250420", "20250425", "20250505"]}, {"ID": "66401254358", "Name": "Salvi, Schostok & Pritchard P.C.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250430"]}, {"ID": "307177253512", "Name": "Thomas J. Henry Injury Attorneys", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414", "20250420", "20250428"]}, {"ID": "258371987557825", "Name": "Rosenfeld Injury Lawyers LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250414"]}, {"ID": "432761353258399", "Name": "<PERSON> Injury <PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250415", "20250416", "20250419"]}, {"ID": "110132760781487", "Name": "Cafferty Clobes Meriwether & Sprengel LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250415", "20250419", "20250505"]}, {"ID": "132684703503274", "Name": "Hilliard Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250415", "20250428", "20250429", "20250518"]}, {"ID": "272786344624", "Name": "Law Office of Cohen & Jaffe, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250415"]}, {"ID": "212496482912817", "Name": "Mancini Law Group PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250415", "20250418", "20250427"]}, {"ID": "273284849194589", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250415", "20250429"]}, {"ID": "732117766804738", "Name": "Wilshire Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250415", "20250418", "20250425", "20250506"]}, {"ID": "494535350666986", "Name": "Kitsinian Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250415", "20250416", "20250423", "20250428"]}, {"ID": "509708892229800", "Name": "Bridge Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250415", "20250428"]}, {"ID": "348307415742394", "Name": "<PERSON> Law Firm, LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250415", "20250419", "20250422"]}, {"ID": "1411841615571392", "Name": "<PERSON>onigle APC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250416", "20250427", "20250506"]}, {"ID": "193055207427704", "Name": "Crosner Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250416"]}, {"ID": "69672514432", "Name": "Elk + Elk", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250416", "20250509"]}, {"ID": "117863808284077", "Name": "ASK LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250416", "20250425", "20250428"]}, {"ID": "100661122011874", "Name": "Shield Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250418", "20250419", "20250428", "20250506"]}, {"ID": "484468491424066", "Name": "Danziger & De Llano LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250418", "20250428", "20250508"]}, {"ID": "283514438182874", "Name": "Console & Associates PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250418", "20250429", "20250504"]}, {"ID": "227774377094918", "Name": "AllConsumer.com", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250418", "20250428"]}, {"ID": "48570030993", "Name": "The Lanier Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250418", "20250506"]}, {"ID": "443561038832857", "Name": "Law Offices of <PERSON>, PLLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250418", "20250427", "20250429"]}, {"ID": "100863422322275", "Name": "The Samuel Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250418", "20250419", "20250508", "20250513"]}, {"ID": "129376430578424", "Name": "Carmichael Law Group LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250418", "20250419", "20250428"]}, {"ID": "136366840902", "Name": "<PERSON> Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250418", "20250424"]}, {"ID": "113217288326572", "Name": "Cafferty Clobes Meriwether & Sprengel LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250418", "20250426", "20250430"]}, {"ID": "589606670895245", "Name": "<PERSON><PERSON>witz Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250419", "20250428"]}, {"ID": "93020432468", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250419"]}, {"ID": "100977681377926", "Name": "Rosen Injury Lawyers", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250419", "20250502", "20250506", "20250508"]}, {"ID": "186720016113", "Name": "<PERSON><PERSON><PERSON> Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250419", "20250425", "20250429"]}, {"ID": "132397746811255", "Name": "Dax F. Garza PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250420", "20250428", "20250508"]}, {"ID": "125981420603061", "Name": "Hoestenbach Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250420", "20250427", "20250508", "20250519"]}, {"ID": "113551715070462", "Name": "Reliance Litigation", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250420"]}, {"ID": "118070921551259", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250420", "20250422"]}, {"ID": "184044921654375", "Name": "<PERSON><PERSON> and <PERSON>, S.C.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250420", "20250425"]}, {"ID": "254754421054879", "Name": "TSEG", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250420", "20250428"]}, {"ID": "172706572778052", "Name": "Ahdoot & Wolfson, PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250421", "20250427"]}, {"ID": "101596948922774", "Name": "Pintas & Mullins Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250421", "20250423", "20250428", "20250429"]}, {"ID": "194705169489", "Name": "Bell Legal Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250422", "20250427"]}, {"ID": "129507503880100", "Name": "The Geisheker Group, Inc.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250422", "20250428", "20250504"]}, {"ID": "102246226535913", "Name": "Farr Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250422", "20250428"]}, {"ID": "10150124349775072", "Name": "Riddle & Brantley, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250422", "20250508", "20250518"]}, {"ID": "239993239188540", "Name": "<PERSON><PERSON>witz Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250422", "20250428"]}, {"ID": "575075125680073", "Name": "Keller Postman LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250422", "20250423", "20250425"]}, {"ID": "468309006362663", "Name": "Saddle Rock Legal Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250423", "20250426"]}, {"ID": "104240345623535", "Name": "Shield Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250423"]}, {"ID": "526755323843483", "Name": "Justpoint", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250423", "20250428", "20250508"]}, {"ID": "588727584314283", "Name": "<PERSON><PERSON>witz Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250423", "20250428", "20250429", "20250430"]}, {"ID": "123685730831742", "Name": "Grewal Law PLLC", "Error": "Failed to fetch ad payloads (APIError)", "Date": ["20250423"]}, {"ID": "106565265656523", "Name": "<PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250423", "20250425", "20250428", "20250430"]}, {"ID": "429632077078674", "Name": "Schlichter Bogard", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250423", "20250428"]}, {"ID": "108018778320005", "Name": "Bridge Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250423", "20250429"]}, {"ID": "103819978219698", "Name": "McGuire Law PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250423", "20250426", "20250428"]}, {"ID": "426292130878895", "Name": "Keefe Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250423"]}, {"ID": "100386825647471", "Name": "Shield Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250423", "20250428"]}, {"ID": "510631232130804", "Name": "Legal Case Info", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250423"]}, {"ID": "561667680365932", "Name": "Keller Postman LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250424"]}, {"ID": "298057500050247", "Name": "Jennings & Earley", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250424", "20250508"]}, {"ID": "760005110702201", "Name": "Rogers, Patrick, Westbrook & Brickman", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250424", "20250428", "20250429", "20250504"]}, {"ID": "204064346755200", "Name": "Saddle Rock Legal Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250424", "20250426", "20250428", "20250503", "20250505"]}, {"ID": "270810792101", "Name": "Portner & Shure, P.A.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250425"]}, {"ID": "545941898613348", "Name": "Justpoint", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250425", "20250429", "20250518"]}, {"ID": "270192996775595", "Name": "<PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250425"]}, {"ID": "113061550605438", "Name": "eClassActions", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250425", "20250428"]}, {"ID": "123368351181305", "Name": "<PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250425", "20250508"]}, {"ID": "348747078320730", "Name": "Jenning Trial Lawyers", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250425"]}, {"ID": "105842484884256", "Name": "<PERSON>, LP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250425"]}, {"ID": "468311546368043", "Name": "Demand <PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250425"]}, {"ID": "1570658903183901", "Name": "<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250426", "20250429", "20250508"]}, {"ID": "118890394634881", "Name": "<PERSON><PERSON> Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250426"]}, {"ID": "105501635457931", "Name": "Bridge Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250426", "20250518"]}, {"ID": "275170805668823", "Name": "<PERSON><PERSON>witz Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250427", "20250428", "20250516"]}, {"ID": "106365767555815", "Name": "<PERSON><PERSON> LL<PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250427"]}, {"ID": "423517227512582", "Name": "Justpoint", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250427", "20250506"]}, {"ID": "103572892613537", "Name": "Shield Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250427"]}, {"ID": "108503689016664", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250427", "20250428"]}, {"ID": "120905021283966", "Name": "Johnson Law Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250427"]}, {"ID": "455606874313169", "Name": "NIB", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250427"]}, {"ID": "193316310528550", "Name": "<PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250427"]}, {"ID": "171654152857450", "Name": "<PERSON>utter Mills LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428"]}, {"ID": "150608971673807", "Name": "BD&J, PC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428"]}, {"ID": "105452181898784", "Name": "<PERSON><PERSON><PERSON> LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428", "20250430"]}, {"ID": "310950025442433", "Name": "Shapiro Legal Group", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428"]}, {"ID": "125044637545415", "Name": "Ventura Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428"]}, {"ID": "499411919911692", "Name": "Justpoint", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428", "20250516", "20250518"]}, {"ID": "223315101050132", "Name": "Waters Kraus Paul & Siegel", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428"]}, {"ID": "1458612937754686", "Name": "A<PERSON>gados, Portner & Shure, P.A.", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428", "20250504"]}, {"ID": "116417805046019", "Name": "<PERSON> Attorneys At Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428"]}, {"ID": "604351039428603", "Name": "Justpoint", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428"]}, {"ID": "552993884753040", "Name": "King Law: A Criminal Defense and Personal Injury Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428"]}, {"ID": "100732948041863", "Name": "Broughton Partners", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428", "20250508", "20250513"]}, {"ID": "348328468361900", "Name": "<PERSON><PERSON>witz Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428", "20250508"]}, {"ID": "1716942118545567", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250428", "20250429", "20250505"]}, {"ID": "561213890415940", "Name": "NIB", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250429", "20250516"]}, {"ID": "108830758894189", "Name": "Shine Legal", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250429"]}, {"ID": "232645613269243", "Name": "The Mitchell Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250430", "20250503"]}, {"ID": "590699717452972", "Name": "Legal Claim Assistant", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250430", "20250505", "20250525"]}, {"ID": "46251887039", "Name": "Top Class Actions", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250430", "20250504"]}, {"ID": "309188829252721", "Name": "McNicholas & McNicholas, LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250430"]}, {"ID": "643655185487996", "Name": "Broughton Partners", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250430"]}, {"ID": "537560389440828", "Name": "Case Legal Media", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250430"]}, {"ID": "955660747844721", "Name": "The Lampin Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250504", "20250508"]}, {"ID": "623044097555644", "Name": "Justpoint", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250504", "20250508"]}, {"ID": "610310652164098", "Name": "Justpoint", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250504"]}, {"ID": "487423161132260", "Name": "<PERSON><PERSON>witz Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250504", "20250518"]}, {"ID": "1568395179936536", "Name": "<PERSON> Carpenter LLP", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250504"]}, {"ID": "196042763600868", "Name": "<PERSON><PERSON>witz Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250505", "20250518"]}, {"ID": "612615795270938", "Name": "Justpoint", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250505"]}, {"ID": "571835572677806", "Name": "Hilliard Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250506", "20250519"]}, {"ID": "100863939045172", "Name": "Total Injury Help", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250506"]}, {"ID": "344283455431257", "Name": "<PERSON><PERSON>witz Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250506", "20250518"]}, {"ID": "110018438480836", "Name": "DJC Law", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250506"]}, {"ID": "239662259237362", "Name": "Broughton Partners", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250508"]}, {"ID": "108670568668664", "Name": "Stop Consumer Harm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250508", "20250518"]}, {"ID": "608987448966389", "Name": "<PERSON><PERSON>", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250508", "20250519"]}, {"ID": "104005283198", "Name": "Meshbesher & Spence", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250508"]}, {"ID": "113751568422310", "Name": "Caesar Marketing LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250509"]}, {"ID": "371119959589764", "Name": "Saiontz & Kirk, P.A. - YouHaveALawyer.com", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250509"]}, {"ID": "929096003945397", "Name": "Justice Alert LLC", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250518"]}, {"ID": "546113975254192", "Name": "<PERSON><PERSON>witz Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250518", "20250519"]}, {"ID": "1700693819975566", "Name": "The Carlson Law Firm", "Error": "Failed to fetch ad payloads (PayloadBlock)", "Date": ["20250518"]}]}