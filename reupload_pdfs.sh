#!/bin/bash

# Check if date parameter is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <date_str>"
    echo "Example: $0 20241122"
    exit 1
fi

DATE_STR=$1
BASE_DIR="/Users/<USER>/PycharmProjects/lexgenius/data"
DATE_DIR="$BASE_DIR/$DATE_STR"
BUCKET_NAME="lexgenius-dockets"

echo "Force re-uploading PDFs for date: $DATE_STR"

# Check if date directory exists
if [ ! -d "$DATE_DIR" ]; then
    echo "Error: Date directory not found: $DATE_DIR"
    exit 1
fi

# Find all PDF files
PDF_FILES=$(find "$DATE_DIR" -name "*.pdf")
PDF_COUNT=$(echo "$PDF_FILES" | wc -l)

echo "Found $PDF_COUNT PDF files in $DATE_DIR"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "Error: AWS CLI is not installed. Please install it first."
    exit 1
fi

# Upload each PDF file
SUCCESS_COUNT=0
ERROR_COUNT=0

for PDF_FILE in $PDF_FILES; do
    FILENAME=$(basename "$PDF_FILE")
    S3_PATH="s3://$BUCKET_NAME/$DATE_STR/dockets/$FILENAME"
    
    echo "Uploading $PDF_FILE to $S3_PATH"
    
    # Upload with content-type and content-disposition
    CONTENT_TYPE=$(file --mime-type -b "$PDF_FILE")
    
    if aws s3 cp "$PDF_FILE" "$S3_PATH" --content-type "$CONTENT_TYPE" --content-disposition "inline"; then
        echo "Successfully uploaded: $S3_PATH"
        ((SUCCESS_COUNT++))
    else
        echo "Error uploading: $S3_PATH"
        ((ERROR_COUNT++))
    fi
done

echo "Upload complete. Success: $SUCCESS_COUNT, Errors: $ERROR_COUNT"
