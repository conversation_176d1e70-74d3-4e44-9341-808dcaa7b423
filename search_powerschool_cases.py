#!/usr/bin/env python3

import os
import json
import re
from pathlib import Path
from rich.console import Console
from rich.table import Table

console = Console()

def search_powerschool_cases(base_dir="/Users/<USER>/PycharmProjects/mt_competitive_analysis/src/data"):
    """
    Search all YYYYMMDD folders for JSON files where "versus" contains "powerschool holdings"
    """
    results = []
    
    # Get all date directories
    date_dirs = [d for d in os.listdir(base_dir) 
                if os.path.isdir(os.path.join(base_dir, d)) and re.match(r'\d{8}', d)]
    
    for date_dir in sorted(date_dirs):
        docket_dir = os.path.join(base_dir, date_dir, "dockets")
        
        if not os.path.exists(docket_dir):
            continue
            
        for filename in os.listdir(docket_dir):
            if not filename.endswith('.json'):
                continue
                
            file_path = os.path.join(docket_dir, filename)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                versus = data.get('versus', '')
                
                if versus and 'powerschool holdings' in versus.lower():
                    results.append({
                        'date': date_dir,
                        'filename': filename,
                        'versus': versus
                    })
            except Exception as e:
                console.print(f"[red]Error processing {file_path}: {str(e)}[/red]")
    
    return results

def main():
    console.print("[bold green]Searching for PowerSchool Holdings cases...[/bold green]")
    
    results = search_powerschool_cases()
    
    if not results:
        console.print("[yellow]No matching cases found.[/yellow]")
        return
    
    # Display results in a table
    table = Table(title="PowerSchool Holdings Cases")
    table.add_column("Date", style="cyan")
    table.add_column("Filename", style="green")
    table.add_column("Versus", style="yellow")
    
    for result in results:
        table.add_row(
            result['date'],
            result['filename'],
            result['versus']
        )
    
    console.print(table)
    console.print(f"\n[green]Total matching cases found: {len(results)}[/green]")

if __name__ == "__main__":
    main()