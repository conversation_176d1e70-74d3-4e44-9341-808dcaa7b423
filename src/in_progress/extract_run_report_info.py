from pprint import pprint


import re
from bs4 import BeautifulSoup

html1 = """<html><head><link rel="shortcut icon" href="/favicon.ico"><title>CAND-ECF</title>
<script type="text/javascript">var default_base_path = "/"; </script><script type="text/javascript">if (top!=self) {top.location.replace(location.href);}</script><link rel="stylesheet" type="text/css" href="/css/default.css"><script type="text/javascript" src="/lib/core.js"></script><link rel="stylesheet" type="text/css" href="/css/print.css" media="print"><script type="text/javascript" src="/cgi-bin/menu.pl?id=-1"></script></head><body bgcolor="CCFFFF" text="000000"><iframe id="_yuiResizeMonitor" style="position: absolute; visibility: visible; width: 10em; height: 10em; top: -160px; left: -160px; border-width: 0px;"></iframe>        <div id="topmenu" class="yuimenubar yui-module yui-overlay visible" style="position: static; display: block; z-index: 30; visibility: visible;">
				<div class="bd"><img src="/graphics/logo-cmecf-sm.png" class="cmecfLogo" id="cmecfLogo" alt="CM/ECF" title="">
				<ul class="first-of-type">
<li class="yuimenubaritem first-of-type" id="yui-gen0" groupindex="0" index="0"><a class="yuimenubaritemlabel" href="/cgi-bin/iquery.pl"><u>Q</u>uery</a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen1" groupindex="0" index="1"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Reports">Reports <div class="spritedownarrow"></div></a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen2" groupindex="0" index="2"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Utilities"><u>U</u>tilities <div class="spritedownarrow"></div></a></li>
				<li class="yuimenubaritem" id="yui-gen3" groupindex="0" index="3">
				<a class="yuimenubaritemlabel" onclick="CMECF.MainMenu.showHelpPage(); return false">Help</a></li>

<li class="yuimenubaritem" id="yui-gen4" groupindex="0" index="4"><a class="yuimenubaritemlabel" href="/cgi-bin/login.pl?logout">Log Out</a></li></ul><hr class="hrmenuseparator"></div></div><script type="text/javascript">if (navigator.appVersion.indexOf("MSIE")==-1){window.setTimeout(CMECF.MainMenu.createMenu, 0);}else{CMECF.util.Event.addListener(window, "load", CMECF.MainMenu.createMenu);}</script> <div id="cmecfMainContent" style="height: 957px;"><input type="hidden" id="cmecfMainContentScroll" value="0"><h3 align="center">U.S. District Court<br>
California Northern District (San Francisco)<br>
CIVIL DOCKET FOR CASE #: 3:24-cv-03622-VC</h3>
<table width="100%" border="0" cellspacing="5"><tbody><tr>
<td valign="top" width="60%"><br>Kassabian v. Monsanto Company<br>
Assigned to: Judge Vince Chhabria<br>
Demand: $75,000<br>
Lead case: <a href="/cgi-bin/DktRpt.pl?303681">3:16-md-02741-VC</a><br>
Member case: <a href="/cgi-bin/AsccaseDisplay.pl?303681">(View Member Case)</a><br>
<table border="0" cellspacing="0"><tbody><tr><td valign="top">Case&nbsp;in&nbsp;other&nbsp;court:</td><td>&nbsp;USDC-District of Massachusetts (Boston), 1:24-cv-11402-GAO</td></tr></tbody></table>
Cause: 28:1332 Diversity-Product Liability</td>
<td valign="top" width="40%"><br>Date Filed: 06/14/2024<br>
Jury Demand: Plaintiff<br>
Nature of Suit: 365 Personal Inj. Prod. Liability<br>
Jurisdiction: Diversity</td>
</tr></tbody></table>
<table width="100%" border="0" cellspacing="5">
				<tbody><tr>
					<td><b><u>Plaintiff                               </u></b></td>
				</tr>

			<tr>
				<td valign="top" width="40%">
					<b>Jay Kassabian</b>
		</td>
<td valign="top" width="20%" align="right">represented&nbsp;by</td><td valign="top" width="40%"><b>Julie                E.              Lamkin                                            </b>
<br>The Law Offices of Jeffrey S. Glassman, LLC                 
<br>One International Place, Suite 1810                         
<br>Boston, MA 02110          
<br>************             
<br><i>ATTORNEY TO BE NOTICED</i></td></tr><tr><td></td></tr>
<tr><td valign="top"><br>V.<br></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>

			<tr>
				<td valign="top" width="40%">
					<b>Monsanto Company</b>
		</td>
</tr><tr><td></td></tr>
</tbody></table>
<br><table align="center" width="99%" border="1" rules="all" cellpadding="5" cellspacing="0">
<tbody><tr><td style="font-weight:bold; width=94; white-space:nowrap">Date Filed</td>
<th>#</th><td style="font-weight:bold">Docket Text</td></tr>
<tr><td width="94" valign="top" nowrap="">05/28/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.cand.uscourts.gov/doc1/035024513736" onclick="goDLS('/doc1/035024513736','431143','3','','1','1','','','');return(false);">1</a>&nbsp;</td><td valign="top"><!--SB-->COMPLAINT against Monsanto Company Filing fee: $ 405, receipt number AMADC-10436145 (Fee Status: Filing Fee paid), filed by Jay Kassabian. (Attachments: # <a href="https://ecf.cand.uscourts.gov/doc1/035124513737" onclick="goDLS('/doc1/035124513737','431143','3','','1','1','','','');return(false);">1</a> Civil Cover Sheet, # <a href="https://ecf.cand.uscourts.gov/doc1/035124513738" onclick="goDLS('/doc1/035124513738','431143','3','','1','1','','','');return(false);">2</a> Category Form)(Lamkin, Julie). (Entered: 05/28/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">05/28/2024</td><td style="white-space:nowrap" valign="top" align="right">2&nbsp;</td><td valign="top"><!--SB-->ELECTRONIC NOTICE of Case Assignment. Judge George A. OToole, Jr assigned to case. If the trial Judge issues an Order of Reference of any matter in this case to a Magistrate Judge, the matter will be transmitted to Magistrate Judge M. Page Kelley. (Cook, Savannah) (Entered: 05/28/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">05/29/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.cand.uscourts.gov/doc1/035124513743" onclick="goDLS('/doc1/035124513743','431143','9','','1','1','','','');return(false);">3</a>&nbsp;</td><td valign="top"><!--SB-->Summons Issued as to All Defendants.<b><font color="red"> Counsel receiving this notice electronically should download this summons, complete one for each defendant and serve it in accordance with Fed.R.Civ.P. 4 and LR 4.1. Summons will be mailed to plaintiff(s) not receiving notice electronically for completion of service.</font></b> (Barbosa, Nilsa) (Entered: 05/29/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/14/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.cand.uscourts.gov/doc1/035124513746" onclick="goDLS('/doc1/035124513746','431143','11','','1','1','','','');return(false);">4</a>&nbsp;</td><td valign="top"><!--SB-->Certified copy of MDL Conditional Transfer Order number 464 in MDL Case 2741 received from the Northern District of California requesting transfer of case for consolidated pretrial proceedings. (Belpedio, Lisa) (Entered: 06/14/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/14/2024</td><td style="white-space:nowrap" valign="top" align="right">5&nbsp;</td><td valign="top"><!--SB-->Case transferred to the Northern District of California pursuant to Conditional Transfer Order entered by the MDL Panel on 464. Case file electronically extracted to the Clerk in that district. (Belpedio, Lisa) (Entered: 06/14/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/14/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.cand.uscourts.gov/doc1/035024513751" onclick="goDLS('/doc1/035024513751','431143','15','','1','1','','','');return(false);">6</a>&nbsp;</td><td valign="top"><!--SB-->Case Transferred in from United States District Court for the District of Massachusetts (Boston); Case Number 1:24-cv-11402-GAO. Original file certified copy of transfer order and docket sheet received. (Entered: 06/14/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/14/2024</td><td style="white-space:nowrap" valign="top" align="right">&nbsp;</td><td valign="top"><!--SB-->MEMBER CASE OPENED re MDL 2741: United States District Court for the District of Massachusetts (Boston), 1:24-cv-11402-GAO, Kassabian v. Monsanto Company, Opened in California Northern District as 3:24-cv-03622-VC pursuant to Conditional Transfer Order (CTO-464) cc: JPMDL (tn, COURT STAFF) (Filed on 6/14/2024) (Entered: 06/14/2024)</td></tr>
</tbody></table><br>
<hr><center><table border="1" bgcolor="white" width="400"><tbody><tr><th colspan="4"><font size="+1" color="DARKRED">PACER Service Center </font></th></tr><tr><th colspan="4"><font color="DARKBLUE">Transaction Receipt </font></th></tr><tr></tr><tr></tr><tr><td colspan="4" align="CENTER"><font size="-1" color="DARKBLUE">06/14/2024 20:07:18</font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> PACER Login: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> gratefuldave </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Client Code: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE">  </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Description: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> Docket Report </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Search Criteria: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 3:24-cv-03622-VC     </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Billable Pages: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 1 </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Cost: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 0.10 </font></td></tr><tr></tr><tr></tr></tbody></table></center></div></body></html>"""

html2 = """<html><head><link rel="shortcut icon" href="/favicon.ico"><title>CM/ECF - azd</title>
<script type="text/javascript">var default_base_path = "/"; </script><script type="text/javascript">if (top!=self) {top.location.replace(location.href);}</script><link rel="stylesheet" type="text/css" href="/css/default.css"><script type="text/javascript" src="/lib/core.js"></script><link rel="stylesheet" type="text/css" href="/css/print.css" media="print"><script type="text/javascript" src="/cgi-bin/menu.pl?id=-1"></script></head><body bgcolor="FFFAFA" text="000000"><iframe id="_yuiResizeMonitor" style="position: absolute; visibility: visible; width: 10em; height: 10em; top: -160px; left: -160px; border-width: 0px;"></iframe>        <div id="topmenu" class="yuimenubar yui-module yui-overlay visible" style="position: static; display: block; z-index: 30; visibility: visible;">
				<div class="bd"><img src="/graphics/logo-cmecf-sm.png" class="cmecfLogo" id="cmecfLogo" alt="CM/ECF" title="">
				<ul class="first-of-type">
<li class="yuimenubaritem first-of-type" id="yui-gen0" groupindex="0" index="0"><a class="yuimenubaritemlabel" href="/cgi-bin/iquery.pl"><u>Q</u>uery</a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen1" groupindex="0" index="1"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Reports">Reports <div class="spritedownarrow"></div></a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen2" groupindex="0" index="2"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Utilities"><u>U</u>tilities <div class="spritedownarrow"></div></a></li>
				<li class="yuimenubaritem" id="yui-gen3" groupindex="0" index="3">
				<a class="yuimenubaritemlabel" onclick="CMECF.MainMenu.showHelpPage(); return false">Help</a></li>

<li class="yuimenubaritem" id="yui-gen4" groupindex="0" index="4"><a class="yuimenubaritemlabel" href="/cgi-bin/login.pl?logout">Log Out</a></li></ul><hr class="hrmenuseparator"></div></div><script type="text/javascript">if (navigator.appVersion.indexOf("MSIE")==-1){window.setTimeout(CMECF.MainMenu.createMenu, 0);}else{CMECF.util.Event.addListener(window, "load", CMECF.MainMenu.createMenu);}</script> <div id="cmecfMainContent" style="height: 957px;"><input type="hidden" id="cmecfMainContentScroll" value="0">
				<table border="0" cellspacing="0" width="100%">
					<tbody><tr><td align="right"><span style="color:green">CONSOLIDATED</span>,<span style="color:green">MULTI-DISTRICT</span></td></tr>
				</tbody></table>
			<h3 align="center">U.S. District Court<br>
DISTRICT OF ARIZONA (Phoenix Division)<br>
CIVIL DOCKET FOR CASE #: 2:24-cv-01416-DGC</h3>
<table width="100%" border="0" cellspacing="5"><tbody><tr>
<td valign="top" width="60%"><br>Simon v. Becton Dickinson and Company et al<br>
Assigned to: Senior Judge David G Campbell<br>
Lead case: <a href="/cgi-bin/DktRpt.pl?1342941">2:23-md-03081-DGC</a><br>
Member case: <a href="/cgi-bin/AsccaseDisplay.pl?1342941">(View Member Case)</a><br>
Cause: 28:1332 Diversity-Product Liability</td>
<td valign="top" width="40%"><br>Date Filed: 06/13/2024<br>
Jury Demand: Plaintiff<br>
Nature of Suit: 365 Personal Injury: Prod. Liability<br>
Jurisdiction: Diversity</td>
</tr></tbody></table>
<table width="100%" border="0" cellspacing="5">
				<tbody><tr>
					<td><b><u>Plaintiff                               </u></b></td>
				</tr>

			<tr>
				<td valign="top" width="40%">
					<b>Katlyn Ann Simon</b>
		</td>
<td valign="top" width="20%" align="right">represented&nbsp;by</td><td valign="top" width="40%"><b>Jon                  Collins         Conlin                                            </b>
<br>Cory Watson PC                                              
<br>2131 Magnolia Ave., Ste. 200                                
<br>Birmingham, AL 35205          
<br>************             
<br>Email: <EMAIL>
<br><i>LEAD ATTORNEY</i>
<br><i>ATTORNEY TO BE NOTICED</i></td></tr><tr><td></td></tr>
<tr><td valign="top"><br>V.<br></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>

			<tr>
				<td valign="top" width="40%">
					<b>Becton Dickinson and Company</b>
		</td>
</tr><tr><td></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>

			<tr>
				<td valign="top" width="40%">
					<b>C R Bard Incorporated</b>
		</td>
</tr><tr><td></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>

			<tr>
				<td valign="top" width="40%">
					<b>Bard Access Systems Incorporated</b>
		</td>
</tr><tr><td></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>

			<tr>
				<td valign="top" width="40%">
					<b>Bard Peripheral Vascular Incorporated</b>
		</td>
</tr><tr><td></td></tr>
</tbody></table>
<br><table align="center" width="99%" border="1" rules="all" cellpadding="5" cellspacing="0">
<tbody><tr><td style="font-weight:bold; width=94; white-space:nowrap">Date Filed</td>
<th>#</th><td style="font-weight:bold">Docket Text</td></tr>
<tr><td width="94" valign="top" nowrap="">06/13/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.azd.uscourts.gov/doc1/025027854848" onclick="goDLS('/doc1/025027854848','1383316','8','','1','1','','','');return(false);">1</a>&nbsp;</td><td valign="top"><!--SB-->COMPLAINT. Filing fee received: $ 405.00, receipt number AAZDC-23146143 filed by Katlyn Ann Simon. (Attachments: # <a href="https://ecf.azd.uscourts.gov/doc1/025127854849" onclick="goDLS('/doc1/025127854849','1383316','8','','1','1','','','');return(false);">1</a> Civil Cover Sheet)(MAP) (Entered: 06/14/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/13/2024</td><td style="white-space:nowrap" valign="top" align="right">2&nbsp;</td><td valign="top"><!--SB-->This case has been assigned to the Honorable David G. Campbell, with member case number: CV-24-1416-PHX-DGC. This case is included in MDL-23-3081. All future pleadings or documents should be filed in the Lead Case: 2:23-md-3081-DGC. This is a TEXT ENTRY ONLY. There is no PDF document associated with this entry. (MAP) (Entered: 06/14/2024)</td></tr>
</tbody></table><br>
<hr><center><table border="1" bgcolor="white" width="400"><tbody><tr><th colspan="4"><font size="+1" color="DARKRED">PACER Service Center </font></th></tr><tr><th colspan="4"><font color="DARKBLUE">Transaction Receipt </font></th></tr><tr></tr><tr></tr><tr><td colspan="4" align="CENTER"><font size="-1" color="DARKBLUE">06/14/2024 20:03:20</font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> PACER Login: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> gratefuldave </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Client Code: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE">  </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Description: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> Docket Report </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Search Criteria: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 2:24-cv-01416-DGC     </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Billable Pages: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 1 </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Cost: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 0.10 </font></td></tr><tr></tr><tr></tr></tbody></table></center></div></body></html>"""

html3 = """<html><head><link rel="shortcut icon" href="/favicon.ico"><title>CAND-ECF</title>
<script type="text/javascript">var default_base_path = "/"; </script><script type="text/javascript">if (top!=self) {top.location.replace(location.href);}</script><link rel="stylesheet" type="text/css" href="/css/default.css"><script type="text/javascript" src="/lib/core.js"></script><link rel="stylesheet" type="text/css" href="/css/print.css" media="print"><script type="text/javascript" src="/cgi-bin/menu.pl?id=-1"></script></head><body bgcolor="CCFFFF" text="000000"><iframe id="_yuiResizeMonitor" style="position: absolute; visibility: visible; width: 10em; height: 10em; top: -160px; left: -160px; border-width: 0px;"></iframe>        <div id="topmenu" class="yuimenubar yui-module yui-overlay visible" style="position: static; display: block; z-index: 30; visibility: visible;">
				<div class="bd"><img src="/graphics/logo-cmecf-sm.png" class="cmecfLogo" id="cmecfLogo" alt="CM/ECF" title="">
				<ul class="first-of-type">
<li class="yuimenubaritem first-of-type" id="yui-gen0" groupindex="0" index="0"><a class="yuimenubaritemlabel" href="/cgi-bin/iquery.pl"><u>Q</u>uery</a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen1" groupindex="0" index="1"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Reports">Reports <div class="spritedownarrow"></div></a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen2" groupindex="0" index="2"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Utilities"><u>U</u>tilities <div class="spritedownarrow"></div></a></li>
				<li class="yuimenubaritem" id="yui-gen3" groupindex="0" index="3">
				<a class="yuimenubaritemlabel" onclick="CMECF.MainMenu.showHelpPage(); return false">Help</a></li>

<li class="yuimenubaritem" id="yui-gen4" groupindex="0" index="4"><a class="yuimenubaritemlabel" href="/cgi-bin/login.pl?logout">Log Out</a></li></ul><hr class="hrmenuseparator"></div></div><script type="text/javascript">if (navigator.appVersion.indexOf("MSIE")==-1){window.setTimeout(CMECF.MainMenu.createMenu, 0);}else{CMECF.util.Event.addListener(window, "load", CMECF.MainMenu.createMenu);}</script> <div id="cmecfMainContent" style="height: 957px;"><input type="hidden" id="cmecfMainContentScroll" value="0">
				<table border="0" cellspacing="0" width="100%">
					<tbody><tr><td align="right"><span>ADRMOP</span></td></tr>
				</tbody></table>
			<h3 align="center">U.S. District Court<br>
California Northern District (Oakland)<br>
CIVIL DOCKET FOR CASE #: 4:24-cv-03536-KAW</h3>
<table width="100%" border="0" cellspacing="5"><tbody><tr>
<td valign="top" width="60%"><br>J.J. et al v. The Cooper Companies, Inc. et al<br>
Assigned to: Magistrate Judge Kandis A. Westmore<br>
Cause: 28:1332 Diversity-Personal Injury</td>
<td valign="top" width="40%"><br>Date Filed: 06/12/2024<br>
Jury Demand: Plaintiff<br>
Nature of Suit: 367 Personal Injury: Health Care/Pharmaceutical Personal Injury Product Liability<br>
Jurisdiction: Diversity</td>
</tr></tbody></table>
<table width="100%" border="0" cellspacing="5">
				<tbody><tr>
					<td><b><u>Plaintiff                               </u></b></td>
				</tr>

			<tr>
				<td valign="top" width="40%">
					<b>J.J.</b>
		</td>
<td valign="top" width="20%" align="right">represented&nbsp;by</td><td valign="top" width="40%"><b>Sarah                Robin           London                                            </b>
<br>Lieff Cabraser Heimann &amp; Bernstein LLP                      
<br>275 Battery Street, 29th Floor                              
<br>San Francisco, CA 94111          
<br>(*************           
<br>Fax: (*************      
<br>Email: <EMAIL>
<br><i>LEAD ATTORNEY</i>
<br><i>ATTORNEY TO BE NOTICED</i><br><br>
<b>Caitlin              M.              Nelson                                            </b>
<br>Lieff Cabraser Heimann and Bernstein, LLP                   
<br>275 Battery Street                                          
<br>29th Floor                                                  
<br>San Francisco, CA 94111-3339     
<br>United Sta
<br>(*************           
<br>Fax: (*************      
<br>Email: <EMAIL>
<br><i>ATTORNEY TO BE NOTICED</i><br><br>
<b>Tiseme               Gabriella       Zegeye                                            </b>
<br>Lieff Cabraser Heimann and Bernstein, LLP                   
<br>275 Battery Street                                          
<br>29th Floor                                                  
<br>San Francisco, CA 94111-3339     
<br>415-956-1000             
<br>Fax: 415-956-1008        
<br>Email: <EMAIL>
<br><i>ATTORNEY TO BE NOTICED</i></td></tr><tr><td></td></tr>

				<tr>
					<td><b><u>Plaintiff                               </u></b></td>
				</tr>

			<tr>
				<td valign="top" width="40%">
					<b>K.K.</b>
		</td>
<td valign="top" width="20%" align="right">represented&nbsp;by</td><td valign="top" width="40%"><b>Sarah                Robin           London                                            </b>
<br>(See above for address)
<br><i>LEAD ATTORNEY</i>
<br><i>ATTORNEY TO BE NOTICED</i><br><br>
<b>Caitlin              M.              Nelson                                            </b>
<br>(See above for address)
<br><i>ATTORNEY TO BE NOTICED</i><br><br>
<b>Tiseme               Gabriella       Zegeye                                            </b>
<br>(See above for address)
<br><i>ATTORNEY TO BE NOTICED</i></td></tr><tr><td></td></tr>
<tr><td valign="top"><br>V.<br></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>

			<tr>
				<td valign="top" width="40%">
					<b>The Cooper Companies, Inc.</b>
		</td>
</tr><tr><td></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>

			<tr>
				<td valign="top" width="40%">
					<b>CooperSurgical, Inc.</b>
		</td>
</tr><tr><td></td></tr>
</tbody></table>
<br><table align="center" width="99%" border="1" rules="all" cellpadding="5" cellspacing="0">
<tbody><tr><td style="font-weight:bold; width=94; white-space:nowrap">Date Filed</td>
<th>#</th><td style="font-weight:bold">Docket Text</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.cand.uscourts.gov/doc1/035024500407" onclick="goDLS('/doc1/035024500407','430979','5','','1','1','','','');return(false);">1</a>&nbsp;</td><td valign="top"><!--SB-->COMPLAINT <i>(Filing Fee $405, receipt number ACANDC-19511985.)</i> against CooperSurgical, Inc., The Cooper Companies, Inc.. Filed by J.J., K.K.. (Attachments: # <a href="https://ecf.cand.uscourts.gov/doc1/035124500408" onclick="goDLS('/doc1/035124500408','430979','5','','1','1','','','');return(false);">1</a> Civil Cover Sheet)(London, Sarah) (Filed on 6/12/2024) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.cand.uscourts.gov/doc1/035124500450" onclick="goDLS('/doc1/035124500450','430979','9','','1','1','','','');return(false);">2</a>&nbsp;</td><td valign="top"><!--SB-->Proposed Summons. (London, Sarah) (Filed on 6/12/2024) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right">3&nbsp;</td><td valign="top"><!--SB-->Case assigned to Magistrate Judge Kandis A. Westmore. <p></p><p>Counsel for plaintiff or the removing party is responsible for serving the Complaint or Notice of Removal, Summons and the assigned judge's standing orders and all other new case documents upon the opposing parties. For information, visit <i>E-Filing A New Civil Case</i> at http://cand.uscourts.gov/ecf/caseopening.</p><p>Standing orders can be downloaded from the court's web page at www.cand.uscourts.gov/judges. Upon receipt, the summons will be issued and returned electronically. A scheduling order will be sent by Notice of Electronic Filing (NEF) within two business days. Consent/Declination due by 6/26/2024. (mbc, COURT STAFF) (Filed on 6/12/2024) (Entered: 06/12/2024)</p></td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.cand.uscourts.gov/doc1/035024500539" onclick="goDLS('/doc1/035024500539','430979','14','','1','1','','','');return(false);">4</a>&nbsp;</td><td valign="top"><!--SB-->NOTICE by J.J., K.K. <i>of Motion and Motion for Leave to Proceed Under Pseudonyms</i> (Attachments: # <a href="https://ecf.cand.uscourts.gov/doc1/035124500540" onclick="goDLS('/doc1/035124500540','430979','14','','1','1','','','');return(false);">1</a> Proposed Order)(London, Sarah) (Filed on 6/12/2024) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right">&nbsp;</td><td valign="top"><!--SB--><a href="https://www.cand.uscourts.gov/pages/1166" target="_Blank">Electronic filing error</a>. Incorrect event used. [err101]. USE Motions and Related Filings - Motions General - Miscellaneous Relief. Text in the name of your motion. This filing will not be processed by the clerks office.Please re-file in its entirety. Re: <a href="https://ecf.cand.uscourts.gov/doc1/035024500539" onclick="goDLS('/doc1/035024500539','430979','14','','1','1','','','');return(false);">4</a> Notice (Other) filed by J.J., K.K. (far, COURT STAFF) (Filed on 6/12/2024) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.cand.uscourts.gov/doc1/035124500645" onclick="goDLS('/doc1/035124500645','430979','19','','1','1','','','');return(false);">5</a>&nbsp;</td><td valign="top"><!--SB--><b>Initial Case Management Scheduling Order with ADR Deadlines: Case Management Statement due by 9/3/2024. Initial Case Management Conference set for 9/10/2024 01:30 PM in Oakland, - Videoconference Only. (far, COURT STAFF) (Filed on 6/12/2024) (Entered: 06/12/2024)</b></td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.cand.uscourts.gov/doc1/035124500655" onclick="goDLS('/doc1/035124500655','430979','21','','1','1','','','');return(false);">6</a>&nbsp;</td><td valign="top"><!--SB-->Summons Issued as to CooperSurgical, Inc., The Cooper Companies, Inc.. (far, COURT STAFF) (Filed on 6/12/2024) (Entered: 06/12/2024)</td></tr>
</tbody></table><br>
<hr><center><table border="1" bgcolor="white" width="400"><tbody><tr><th colspan="4"><font size="+1" color="DARKRED">PACER Service Center </font></th></tr><tr><th colspan="4"><font color="DARKBLUE">Transaction Receipt </font></th></tr><tr></tr><tr></tr><tr><td colspan="4" align="CENTER"><font size="-1" color="DARKBLUE">06/12/2024 19:22:48</font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> PACER Login: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> gratefuldave </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Client Code: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE">  </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Description: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> Docket Report </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Search Criteria: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 4:24-cv-03536-KAW     </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Billable Pages: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 2 </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Cost: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 0.20 </font></td></tr><tr></tr><tr></tr></tbody></table></center></div></body></html>"""

html4 = """<html><head><link rel="shortcut icon" href="/favicon.ico"><title>Eastern District of New York - LIVE Database 1.7 (Revision *******)</title>
<script type="text/javascript">var default_base_path = "/"; </script><script type="text/javascript">if (top!=self) {top.location.replace(location.href);}</script><link rel="stylesheet" type="text/css" href="/css/default.css"><script type="text/javascript" src="/lib/core.js"></script><link rel="stylesheet" type="text/css" href="/css/print.css" media="print"><script type="text/javascript" src="/cgi-bin/menu.pl?id=-1"></script></head><body bgcolor="FFFFF0" text="000000"><iframe id="_yuiResizeMonitor" style="position: absolute; visibility: visible; width: 10em; height: 10em; top: -160px; left: -160px; border-width: 0px;"></iframe>        <div id="topmenu" class="yuimenubar yui-module yui-overlay visible" style="position: static; display: block; z-index: 30; visibility: visible;">
				<div class="bd"><img src="/graphics/logo-cmecf-sm.png" class="cmecfLogo" id="cmecfLogo" alt="CM/ECF" title="">
				<ul class="first-of-type">
<li class="yuimenubaritem first-of-type" id="yui-gen0" groupindex="0" index="0"><a class="yuimenubaritemlabel" href="/cgi-bin/iquery.pl"><u>Q</u>uery</a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen1" groupindex="0" index="1"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Reports">Reports <div class="spritedownarrow"></div></a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen2" groupindex="0" index="2"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Utilities"><u>U</u>tilities <div class="spritedownarrow"></div></a></li>
				<li class="yuimenubaritem" id="yui-gen3" groupindex="0" index="3">
				<a class="yuimenubaritemlabel" onclick="CMECF.MainMenu.showHelpPage(); return false">Help</a></li>
				
<li class="yuimenubaritem" id="yui-gen4" groupindex="0" index="4"><a class="yuimenubaritemlabel" href="/cgi-bin/login.pl?logout">Log Out</a></li></ul><hr class="hrmenuseparator"></div></div><script type="text/javascript">if (navigator.appVersion.indexOf("MSIE")==-1){window.setTimeout(CMECF.MainMenu.createMenu, 0);}else{CMECF.util.Event.addListener(window, "load", CMECF.MainMenu.createMenu);}</script> <div id="cmecfMainContent" style="height: 957px;"><input type="hidden" id="cmecfMainContentScroll" value="0">
				<table border="0" cellspacing="0" width="100%">
					<tbody><tr><td align="right"><span style="color:green">ACO</span>,<span style="color:blue">MDL3044</span></td></tr>
				</tbody></table>
			<h3 align="center">U.S. District Court<br>
Eastern District of New York (Brooklyn)<br>
CIVIL DOCKET FOR CASE #: 1:24-cv-04178-NGG-MMH</h3>
<table width="100%" border="0" cellspacing="5"><tbody><tr>
<td valign="top" width="60%"><br>Greffly et al v. Exactech, Inc. et al<br>
Assigned to: Judge Nicholas G. Garaufis<br>
Referred to: Magistrate Judge Marcia M. Henry<br>
Lead case: <a href="/cgi-bin/DktRpt.pl?486904">1:22-md-03044-NGG-MMH</a><br>
Member case: <a href="/cgi-bin/AsccaseDisplay.pl?486904">(View Member Case)</a><br>
Cause: 28:1332 Diversity-Product Liability</td>
<td valign="top" width="40%"><br>Date Filed: 06/12/2024<br>
Jury Demand: Plaintiff<br>
Nature of Suit: 365 Personal Inj. Prod. Liability<br>
Jurisdiction: Diversity</td>
</tr></tbody></table>
<table width="100%" border="0" cellspacing="5">
				<tbody><tr>
					<td><b><u>Plaintiff                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>James Greffly</b>
		</td>
<td valign="top" width="20%" align="right">represented&nbsp;by</td><td valign="top" width="40%"><b>C.                   Calvin          Warriner                                          , III  </b>
<br>Searcy Denney Scarola Barnhart &amp; Shipley PA                 
<br>2139 Palm Beach Lakes Blvd.                                 
<br>West Palm Beach, FL 33409          
<br>************             
<br>Fax: ************        
<br>Email: <EMAIL>
<br><i>ATTORNEY TO BE NOTICED</i></td></tr><tr><td></td></tr>

				<tr>
					<td><b><u>Plaintiff                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>Kathleen Greffly</b>
		</td>
<td valign="top" width="20%" align="right">represented&nbsp;by</td><td valign="top" width="40%"><b>C.                   Calvin          Warriner                                          , III  </b>
<br>(See above for address)
<br><i>ATTORNEY TO BE NOTICED</i></td></tr><tr><td></td></tr>
<tr><td valign="top"><br>V.<br></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>Exactech, Inc.</b>
		</td>
</tr><tr><td></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>Exactech US, Inc.</b>
		</td>
</tr><tr><td></td></tr>
</tbody></table>
<br><table align="center" width="99%" border="1" rules="all" cellpadding="5" cellspacing="0">
<tbody><tr><td style="font-weight:bold; width=94; white-space:nowrap">Date Filed</td>
<th>#</th><td style="font-weight:bold">Docket Text</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.nyed.uscourts.gov/doc1/123021390944" onclick="goDLS('/doc1/123021390944','516412','5','','1','1','','','');return(false);">1</a>&nbsp;</td><td valign="top"><!--SB-->COMPLAINT <i></i> against All Defendants filing fee $ 405, receipt number ANYEDC-17969339 Was the Disclosure Statement on Civil Cover Sheet completed -Yes,, filed by James Greffly, Kathleen Greffly. (Attachments: # <a href="https://ecf.nyed.uscourts.gov/doc1/123121390945" onclick="goDLS('/doc1/123121390945','516412','5','','1','1','','','');return(false);">1</a> Civil Cover Sheet) (Warriner, C.) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.nyed.uscourts.gov/doc1/123121390951" onclick="goDLS('/doc1/123121390951','516412','9','','1','1','','','');return(false);">2</a>&nbsp;</td><td valign="top"><!--SB-->Proposed Summons. Re <a href="https://ecf.nyed.uscourts.gov/doc1/123021390944" onclick="goDLS('/doc1/123021390944','516412','5','','1','1','','','');return(false);">1</a> Complaint, by James Greffly, Kathleen Greffly (Warriner, C.) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right">&nbsp;</td><td valign="top"><!--SB-->Case Assigned to Judge Nicholas G. Garaufis and Magistrate Judge Marcia M. Henry. Please download and review the Individual Practices of the assigned Judges, located on our <a href="https://www.nyed.uscourts.gov/judges-info">website</a>. Attorneys are responsible for providing courtesy copies to judges where their Individual Practices require such. (KD) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.nyed.uscourts.gov/doc1/123121393435" onclick="goDLS('/doc1/123121393435','516412','18','','1','1','','','');return(false);">3</a>&nbsp;</td><td valign="top"><!--SB-->Clerk's Notice Re: Consent. A United States Magistrate Judge has been assigned to this case and is available to conduct all proceedings. In accordance with Rule 73 of the Federal Rules of Civil Procedure, Local Rule 73.1, the parties are notified that if all parties consent, the assigned Magistrate Judge is available to conduct all proceedings in this action including a (jury or nonjury) trial and to order the entry of a final judgment. Attached to this Notice is a blank copy of the consent form that should be filled out, signed and filed electronically only if all parties wish to consent. Any party may withhold its consent without adverse substantive consequences. Do NOT return or file the consent unless all parties have signed the consent.The form may also be accessed at the following link: <a href="https://img.nyed.uscourts.gov/files/forms/MJConsentForm.pdf">https://img.nyed.uscourts.gov/files/forms/MJConsentForm.pdf</a> (KD) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.nyed.uscourts.gov/doc1/123121393445" onclick="goDLS('/doc1/123121393445','516412','20','','1','1','','','');return(false);">4</a>&nbsp;</td><td valign="top"><!--SB-->This attorney case opening filing has been checked for quality control. See the attachment for corrections that were made, if any. (KD) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.nyed.uscourts.gov/doc1/123121393455" onclick="goDLS('/doc1/123121393455','516412','22','','1','1','','','');return(false);">5</a>&nbsp;</td><td valign="top"><!--SB-->Summons Issued as to Exactech US, Inc., Exactech, Inc.. (KD) (Entered: 06/12/2024)</td></tr>
</tbody></table><br>
<hr><center><table border="1" bgcolor="white" width="400"><tbody><tr><th colspan="4"><font size="+1" color="DARKRED">PACER Service Center </font></th></tr><tr><th colspan="4"><font color="DARKBLUE">Transaction Receipt </font></th></tr><tr></tr><tr></tr><tr><td colspan="4" align="CENTER"><font size="-1" color="DARKBLUE">06/12/2024 22:21:36</font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> PACER Login: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> gratefuldave </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Client Code: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE">  </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Description: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> Docket Report </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Search Criteria: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 1:24-cv-04178-NGG-MMH     </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Billable Pages: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 2 </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Cost: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 0.20 </font></td></tr><tr></tr><tr></tr></tbody></table></center></div></body></html>"""

html5 = """<html><head><link rel="shortcut icon" href="/favicon.ico"><title>CM/ECF - District of Minnesota - Live</title>
<script type="text/javascript">var default_base_path = "/"; </script><script type="text/javascript">if (top!=self) {top.location.replace(location.href);}</script><link rel="stylesheet" type="text/css" href="/css/default.css"><script type="text/javascript" src="/lib/core.js"></script><link rel="stylesheet" type="text/css" href="/css/print.css" media="print"><script type="text/javascript" src="/cgi-bin/menu.pl?id=-1"></script></head><body bgcolor="F9F9F9" text="000000"><iframe id="_yuiResizeMonitor" style="position: absolute; visibility: visible; width: 10em; height: 10em; top: -160px; left: -160px; border-width: 0px;"></iframe>        <div id="topmenu" class="yuimenubar yui-module yui-overlay visible" style="position: static; display: block; z-index: 30; visibility: visible;">
				<div class="bd"><img src="/graphics/logo-cmecf-sm.png" class="cmecfLogo" id="cmecfLogo" alt="CM/ECF" title="">
				<ul class="first-of-type">
<li class="yuimenubaritem first-of-type" id="yui-gen0" groupindex="0" index="0"><a class="yuimenubaritemlabel" href="/cgi-bin/iquery.pl"><u>Q</u>uery</a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen1" groupindex="0" index="1"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Reports">Reports <div class="spritedownarrow"></div></a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen2" groupindex="0" index="2"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Utilities"><u>U</u>tilities <div class="spritedownarrow"></div></a></li>
				<li class="yuimenubaritem" id="yui-gen3" groupindex="0" index="3">
				<a class="yuimenubaritemlabel" onclick="CMECF.MainMenu.showHelpPage(); return false">Help</a></li>
				
<li class="yuimenubaritem" id="yui-gen4" groupindex="0" index="4"><a class="yuimenubaritemlabel" href="/cgi-bin/login.pl?logout">Log Out</a></li></ul><hr class="hrmenuseparator"></div></div><script type="text/javascript">if (navigator.appVersion.indexOf("MSIE")==-1){window.setTimeout(CMECF.MainMenu.createMenu, 0);}else{CMECF.util.Event.addListener(window, "load", CMECF.MainMenu.createMenu);}</script> <div id="cmecfMainContent" style="height: 957px;"><input type="hidden" id="cmecfMainContentScroll" value="0">
				<table border="0" cellspacing="0" width="100%">
					<tbody><tr><td align="right"><span style="color:#000099">CV</span></td></tr>
				</tbody></table>
			<h3 align="center">U.S. District Court<br>
U.S. District of Minnesota (DMN)<br>
CIVIL DOCKET FOR CASE #: 0:24-cv-02256</h3>
<table width="100%" border="0" cellspacing="5"><tbody><tr>
<td valign="top" width="60%"><br>Roberts v. 3M  Company et al<br>
Assigned to: <br>
Demand: $9,999,000<br>
Cause: 28:1332-pip-Diversity-Personal Injury, Product Liability</td>
<td valign="top" width="40%"><br>Date Filed: 06/12/2024<br>
Jury Demand: Plaintiff<br>
Nature of Suit: 367 Personal Injury: Health Care/Pharmaceutical Personal Injury Product Liability<br>
Jurisdiction: Diversity</td>
</tr></tbody></table>
<table width="100%" border="0" cellspacing="5">
				<tbody><tr>
					<td><b><u>Plaintiff                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>Clarence R. Roberts</b>
		</td>
<td valign="top" width="20%" align="right">represented&nbsp;by</td><td valign="top" width="40%"><b>Gabriel              Assaad                                            </b>
<br>McDonald Worley                                             
<br>1770 St. James Place                                        
<br>Suite 100                                                   
<br>Houston, TX 77056          
<br>************             
<br>Fax: ************        
<br>Email: <EMAIL>
<br><i>ATTORNEY TO BE NOTICED</i></td></tr><tr><td></td></tr>
<tr><td valign="top"><br>V.<br></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>3M  Company</b>
		</td>
</tr><tr><td></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>Arizant  Healthcare, Inc. </b>
		</td>
</tr><tr><td></td></tr>
</tbody></table>
<br><table align="center" width="99%" border="1" rules="all" cellpadding="5" cellspacing="0">
<tbody><tr><td style="font-weight:bold; width=94; white-space:nowrap">Date Filed</td>
<th>#</th><td style="font-weight:bold">Docket Text</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.mnd.uscourts.gov/doc1/101010428893" onclick="goDLS('/doc1/101010428893','216111','4','','1','1','','','');return(false);">1</a>&nbsp;</td><td valign="top"><!--SB-->COMPLAINT <i>Clarence R. Roberts</i> against All Defendants (filing fee $ 405, receipt number AMNDC-11124722) filed by Clarence R. Roberts.<font color="green"> Filer requests summons issued.</font> (Attachments: # <a href="https://ecf.mnd.uscourts.gov/doc1/101110428894" onclick="goDLS('/doc1/101110428894','216111','4','','1','1','','','');return(false);">1</a> Civil Cover Sheet) (Assaad, Gabriel) (Entered: 06/12/2024)</td></tr>
</tbody></table><br>
<hr><center><table border="1" bgcolor="white" width="400"><tbody><tr><th colspan="4"><font size="+1" color="DARKRED">PACER Service Center </font></th></tr><tr><th colspan="4"><font color="DARKBLUE">Transaction Receipt </font></th></tr><tr></tr><tr></tr><tr><td colspan="4" align="CENTER"><font size="-1" color="DARKBLUE">06/12/2024 21:19:33</font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> PACER Login: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> gratefuldave </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Client Code: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE">  </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Description: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> Docket Report </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Search Criteria: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 0:24-cv-02256     </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Billable Pages: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 1 </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Cost: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 0.10 </font></td></tr><tr></tr><tr></tr></tbody></table></center></div></body></html>"""

html6 = """<html><head><link rel="shortcut icon" href="/favicon.ico"><title>CM/ECF LIVE - U.S. District Court for the District of New Jersey</title>
<script type="text/javascript">var default_base_path = "/"; </script><script type="text/javascript">if (top!=self) {top.location.replace(location.href);}</script><link rel="stylesheet" type="text/css" href="/css/default.css"><script type="text/javascript" src="/lib/core.js"></script><link rel="stylesheet" type="text/css" href="/css/print.css" media="print"><script type="text/javascript" src="/cgi-bin/menu.pl?id=-1"></script></head><body bgcolor="FFFFFF" text="000000"><iframe id="_yuiResizeMonitor" style="position: absolute; visibility: visible; width: 10em; height: 10em; top: -160px; left: -160px; border-width: 0px;"></iframe>        <div id="topmenu" class="yuimenubar yui-module yui-overlay visible" style="position: static; display: block; z-index: 30; visibility: visible;">
				<div class="bd"><img src="/graphics/logo-cmecf-sm.png" class="cmecfLogo" id="cmecfLogo" alt="CM/ECF" title="">
				<ul class="first-of-type">
<li class="yuimenubaritem first-of-type" id="yui-gen0" groupindex="0" index="0"><a class="yuimenubaritemlabel" href="/cgi-bin/iquery.pl"><u>Q</u>uery</a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen1" groupindex="0" index="1"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Reports">Reports <div class="spritedownarrow"></div></a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen2" groupindex="0" index="2"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Utilities"><u>U</u>tilities <div class="spritedownarrow"></div></a></li>
				<li class="yuimenubaritem" id="yui-gen3" groupindex="0" index="3">
				<a class="yuimenubaritemlabel" onclick="CMECF.MainMenu.showHelpPage(); return false">Help</a></li>
				
<li class="yuimenubaritem" id="yui-gen4" groupindex="0" index="4"><a class="yuimenubaritemlabel" href="/cgi-bin/login.pl?logout">Log Out</a></li></ul><hr class="hrmenuseparator"></div></div><script type="text/javascript">if (navigator.appVersion.indexOf("MSIE")==-1){window.setTimeout(CMECF.MainMenu.createMenu, 0);}else{CMECF.util.Event.addListener(window, "load", CMECF.MainMenu.createMenu);}</script> <div id="cmecfMainContent" style="height: 957px;"><input type="hidden" id="cmecfMainContentScroll" value="0">
				<table border="0" cellspacing="0" width="100%">
					<tbody><tr><td align="right"><span style="color:green">MDL2738</span></td></tr>
				</tbody></table>
			<h3 align="center">U.S. District Court<br>
District of New Jersey [LIVE] (Trenton)<br>
CIVIL DOCKET FOR CASE #: 3:24-cv-06962-MAS-RLS</h3>
<table width="100%" border="0" cellspacing="5"><tbody><tr>
<td valign="top" width="60%"><br>GARCIA v. JOHNSON &amp; JOHNSON et al<br>
Assigned to: Judge Michael A. Shipp<br>
Referred to: Magistrate Judge Rukhsanah L. Singh<br>
Cause: 28:1332 Diversity-Product Liability</td>
<td valign="top" width="40%"><br>Date Filed: 06/12/2024<br>
Jury Demand: Plaintiff<br>
Nature of Suit: 367 Personal Injury: Health Care/Pharmaceutical Personal Injury Product Liability<br>
Jurisdiction: Diversity</td>
</tr></tbody></table>
<table width="100%" border="0" cellspacing="5">
				<tbody><tr>
					<td><b><u>Plaintiff                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>JOSE GARCIA</b> <br><i>INDIVIDUALLY AND ON BEHALF OF MARIA GARCIA, DECEASED</i>
		</td>
<td valign="top" width="20%" align="right">represented&nbsp;by</td><td valign="top" width="40%"><b>WARREN               T               BURNS                                             </b>
<br>BURNS CHAREST LLP                                           
<br>900 Jackson Street                                          
<br>Suite 500                                                   
<br>DALLAS, TX 75202          
<br>469/904-4550             
<br>Fax: 469/444-5002        
<br>Email: <EMAIL>
<br><i>ATTORNEY TO BE NOTICED</i></td></tr><tr><td></td></tr>
<tr><td valign="top"><br>V.<br></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>JOHNSON &amp; JOHNSON</b>
		</td>
</tr><tr><td></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>JOHNSON &amp; JOHNSON CONSUMER INC.</b>
		</td>
</tr><tr><td></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>LLT MANAGEMENT LLC</b> <br><i>F/K/A LTL MANAGEMENT LLC</i>
		</td>
</tr><tr><td></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>JOHNSON &amp; JOHNSON HOLDCO (NA) INC.</b>
		</td>
</tr><tr><td></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>JANSSEN PHARMACEUTICALS, INC.</b>
		</td>
</tr><tr><td></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>KENVUE, INC.</b>
		</td>
</tr><tr><td></td></tr>
</tbody></table>
<br><table align="center" width="99%" border="1" rules="all" cellpadding="5" cellspacing="0">
<tbody><tr><td style="font-weight:bold; width=94; white-space:nowrap">Date Filed</td>
<th>#</th><td style="font-weight:bold">Docket Text</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.njd.uscourts.gov/doc1/119021427179" onclick="goDLS('/doc1/119021427179','550060','10','','2','','','','');return(false);">1</a>&nbsp;</td><td valign="top"><!--SB-->COMPLAINT against JANSSEN PHARMACEUTICALS, INC., JOHNSON &amp; JOHNSON, JOHNSON &amp; JOHNSON CONSUMER INC., JOHNSON &amp; JOHNSON HOLDCO (NA) INC., KENVUE, INC., LLT MANAGEMENT LLC ( Filing and Admin fee $ 405 receipt number ANJDC-15440251) with JURY DEMAND, filed by JOSE GARCIA. (Attachments: # <a href="https://ecf.njd.uscourts.gov/doc1/119121427180" onclick="goDLS('/doc1/119121427180','550060','10','','2','','','','');return(false);">1</a> Civil Cover Sheet)(BURNS, WARREN) (Entered: 06/12/2024)</td></tr>
</tbody></table><br>
<hr><center><table border="1" bgcolor="white" width="400"><tbody><tr><th colspan="4"><font size="+1" color="DARKRED">PACER Service Center </font></th></tr><tr><th colspan="4"><font color="DARKBLUE">Transaction Receipt </font></th></tr><tr></tr><tr></tr><tr><td colspan="4" align="CENTER"><font size="-1" color="DARKBLUE">06/12/2024 22:22:16</font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> PACER Login: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> gratefuldave </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Client Code: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE">  </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Description: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> Docket Report </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Search Criteria: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 3:24-cv-06962-MAS-RLS Start date: 1/1/1980  End date: 6/12/2024   </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Billable Pages: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 1 </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Cost: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 0.10 </font></td></tr><tr></tr><tr></tr></tbody></table></center></div></body></html>"""

html7 = """<html><head><link rel="shortcut icon" href="/favicon.ico"><title>ECF Western District of Wisconsin</title>
<script type="text/javascript">var default_base_path = "/"; </script><script type="text/javascript">if (top!=self) {top.location.replace(location.href);}</script><link rel="stylesheet" type="text/css" href="/css/default.css"><script type="text/javascript" src="/lib/core.js"></script><link rel="stylesheet" type="text/css" href="/css/print.css" media="print"><script type="text/javascript" src="/cgi-bin/menu.pl?id=-1"></script></head><body bgcolor="f5f5f5" text="000000"><iframe id="_yuiResizeMonitor" style="position: absolute; visibility: visible; width: 10em; height: 10em; top: -160px; left: -160px; border-width: 0px;"></iframe>        <div id="topmenu" class="yuimenubar yui-module yui-overlay visible" style="position: static; display: block; z-index: 30; visibility: visible;">
				<div class="bd"><img src="/graphics/logo-cmecf-sm.png" class="cmecfLogo" id="cmecfLogo" alt="CM/ECF" title="">
				<ul class="first-of-type">
<li class="yuimenubaritem first-of-type" id="yui-gen0" groupindex="0" index="0"><a class="yuimenubaritemlabel" href="/cgi-bin/iquery.pl"><u>Q</u>uery</a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen1" groupindex="0" index="1"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Reports">Reports <div class="spritedownarrow"></div></a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen2" groupindex="0" index="2"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Utilities"><u>U</u>tilities <div class="spritedownarrow"></div></a></li>
				<li class="yuimenubaritem" id="yui-gen3" groupindex="0" index="3">
				<a class="yuimenubaritemlabel" onclick="CMECF.MainMenu.showHelpPage(); return false">Help</a></li>
				
<li class="yuimenubaritem" id="yui-gen4" groupindex="0" index="4"><a class="yuimenubaritemlabel" href="/cgi-bin/login.pl?logout">Log Out</a></li></ul><hr class="hrmenuseparator"></div></div><script type="text/javascript">if (navigator.appVersion.indexOf("MSIE")==-1){window.setTimeout(CMECF.MainMenu.createMenu, 0);}else{CMECF.util.Event.addListener(window, "load", CMECF.MainMenu.createMenu);}</script> <div id="cmecfMainContent" style="height: 957px;"><input type="hidden" id="cmecfMainContentScroll" value="0"><h3 align="center">U.S. District Court<br>
Western District of Wisconsin (Madison)<br>
CIVIL DOCKET FOR CASE #: 3:24-cv-00396-wmc</h3>
<table width="100%" border="0" cellspacing="5"><tbody><tr>
<td valign="top" width="60%"><br>Droessler, Thomas v. Dell Technologies, Inc.<br>
Assigned to: District Judge William M. Conley<br>
Referred to: Magistrate Judge Anita M. Boor<br>
Cause: 28:1332 Diversity-Product Liability</td>
<td valign="top" width="40%"><br>Date Filed: 06/12/2024<br>
Jury Demand: Plaintiff<br>
Nature of Suit: 365 Personal Inj. Prod. Liability<br>
Jurisdiction: Diversity</td>
</tr></tbody></table>
<table width="100%" border="0" cellspacing="5">
				<tbody><tr>
					<td><b><u>Plaintiff                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>Thomas James Droessler</b>
		</td>
<td valign="top" width="20%" align="right">represented&nbsp;by</td><td valign="top" width="40%"><b>Adam                 John            Kress                                             </b>
<br>Johnson Becker, PLLC                                        
<br>444 Cedar Street                                            
<br>Suite 1800                                                  
<br>St. Paul, MN 55101          
<br>************             
<br>Fax: ************        
<br>Email: <EMAIL>
<br><i>ATTORNEY TO BE NOTICED</i></td></tr><tr><td></td></tr>
<tr><td valign="top"><br>V.<br></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>Dell Technologies, Inc.</b>
		</td>
</tr><tr><td></td></tr>
</tbody></table>
<br><table align="center" width="99%" border="1" rules="all" cellpadding="5" cellspacing="0">
<tbody><tr><td style="font-weight:bold; width=94; white-space:nowrap">Date Filed</td>
<th>#</th><td style="font-weight:bold">Docket Text</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.wiwd.uscourts.gov/doc1/20506222663" onclick="goDLS('/doc1/20506222663','52794','3','','1','1','','','');return(false);">1</a>&nbsp;</td><td valign="top"><!--SB-->COMPLAINT <i></i> against Dell Technologies, Inc.. ( Filing fee $ 405 receipt number AWIWDC-3441560.), filed by Thomas James Droessler. (Attachments: <br># <a href="https://ecf.wiwd.uscourts.gov/doc1/20516222664" onclick="goDLS('/doc1/20516222664','52794','3','','1','1','','','');return(false);">1</a> JS-44 Civil Cover Sheet, <br># <a href="https://ecf.wiwd.uscourts.gov/doc1/20516222665" onclick="goDLS('/doc1/20516222665','52794','3','','1','1','','','');return(false);">2</a> Summons) (Kress, Adam) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right">&nbsp;</td><td valign="top"><!--SB-->Case randomly assigned to District Judge William M. Conley and Magistrate Judge Anita M. Boor. (lak) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right">&nbsp;</td><td valign="top"><!--SB-->Standard attachments for Judge William M. Conley required to be served on all parties with summons or waiver of service: <a href="http://www.wiwd.uscourts.gov/sites/default/files/AO_85.pdf"> NORTC</a>, <a href="http://www.wiwd.uscourts.gov/sites/default/files/Corporate_Disclosure_Statement.pdf">Corporate Disclosure Statement</a>. (lak) (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.wiwd.uscourts.gov/doc1/20516222887" onclick="goDLS('/doc1/20516222887','52794','12','','1','1','','','');return(false);">2</a>&nbsp;</td><td valign="top"><!--SB-->Summons Issued as to Dell Technologies, Inc. (lak) (Entered: 06/12/2024)</td></tr>
</tbody></table><br>
<hr><center><table border="1" bgcolor="white" width="400"><tbody><tr><th colspan="4"><font size="+1" color="DARKRED">PACER Service Center </font></th></tr><tr><th colspan="4"><font color="DARKBLUE">Transaction Receipt </font></th></tr><tr></tr><tr></tr><tr><td colspan="4" align="CENTER"><font size="-1" color="DARKBLUE">06/12/2024 21:23:39</font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> PACER Login: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> gratefuldave </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Client Code: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE">  </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Description: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> Docket Report </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Search Criteria: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 3:24-cv-00396-wmc     </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Billable Pages: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 1 </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Cost: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 0.10 </font></td></tr><tr></tr><tr></tr></tbody></table></center></div></body></html>"""

html8 = """<html><head><link rel="shortcut icon" href="/favicon.ico"><title>Electronic Case Filing |  U.S. District Court - Middle District of Florida</title>
<script type="text/javascript">var default_base_path = "/"; </script><script type="text/javascript">if (top!=self) {top.location.replace(location.href);}</script><link rel="stylesheet" type="text/css" href="/css/default.css"><script type="text/javascript" src="/lib/core.js"></script><link rel="stylesheet" type="text/css" href="/css/print.css" media="print"><script type="text/javascript" src="/cgi-bin/menu.pl?id=-1"></script></head><body bgcolor="FFFFFF" text="000099" alink="0000FF" link="0000FF" a:hover="FF0000" vlink="purple"><iframe id="_yuiResizeMonitor" style="position: absolute; visibility: visible; width: 10em; height: 10em; top: -160px; left: -160px; border-width: 0px;"></iframe>        <div id="topmenu" class="yuimenubar yui-module yui-overlay visible" style="position: static; display: block; z-index: 30; visibility: visible;">
				<div class="bd"><img src="/graphics/logo-cmecf-sm.png" class="cmecfLogo" id="cmecfLogo" alt="CM/ECF" title="">
				<ul class="first-of-type">
<li class="yuimenubaritem first-of-type" id="yui-gen0" groupindex="0" index="0"><a class="yuimenubaritemlabel" href="/cgi-bin/iquery.pl"><u>Q</u>uery</a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen1" groupindex="0" index="1"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Reports">Reports <div class="spritedownarrow"></div></a></li>
<li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen2" groupindex="0" index="2"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Utilities"><u>U</u>tilities <div class="spritedownarrow"></div></a></li>
				<li class="yuimenubaritem" id="yui-gen3" groupindex="0" index="3">
				<a class="yuimenubaritemlabel" onclick="CMECF.MainMenu.showHelpPage(); return false">Help</a></li>
				
<li class="yuimenubaritem" id="yui-gen4" groupindex="0" index="4"><a class="yuimenubaritemlabel" href="/cgi-bin/login.pl?logout">Log Out</a></li></ul><hr class="hrmenuseparator"></div></div><script type="text/javascript">if (navigator.appVersion.indexOf("MSIE")==-1){window.setTimeout(CMECF.MainMenu.createMenu, 0);}else{CMECF.util.Event.addListener(window, "load", CMECF.MainMenu.createMenu);}</script> <div id="cmecfMainContent" style="height: 957px;"><input type="hidden" id="cmecfMainContentScroll" value="0"><h3 align="center">U.S. District Court<br>
Middle District of Florida (Orlando)<br>
CIVIL DOCKET FOR CASE #: 6:24-cv-01088-WWB-DCI</h3>
<table width="100%" border="0" cellspacing="5"><tbody><tr>
<td valign="top" width="60%"><br>Hahn, as PNG of CH v. Future Motion, Inc.<br>
Assigned to: Judge Wendy W. Berger<br>
Referred to: Magistrate Judge Daniel C. Irick<br>
Cause: 28:1332 Diversity-Product Liability</td>
<td valign="top" width="40%"><br>Date Filed: 06/12/2024<br>
Jury Demand: Plaintiff<br>
Nature of Suit: 365 Personal Inj. Prod. Liability<br>
Jurisdiction: Diversity</td>
</tr></tbody></table>
<table width="100%" border="0" cellspacing="5">
				<tbody><tr>
					<td><b><u>Plaintiff                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>Maija Hahn, as PNG of CH</b>
		</td>
<td valign="top" width="20%" align="right">represented&nbsp;by</td><td valign="top" width="40%"><b>Daniel               C.              Jensen                                            </b>
<br>Lytal Reiter Smith Ivey &amp; Fronrath                          
<br>515 North Flagler Drive, 10th Floor                         
<br>West Palm Beach, FL 33401          
<br>************             
<br>Fax: ************        
<br>Email: <EMAIL>
<br><i>ATTORNEY TO BE NOTICED</i></td></tr><tr><td></td></tr>
<tr><td valign="top"><br>V.<br></td></tr>

				<tr>
					<td><b><u>Defendant                               </u></b></td>
				</tr>
			
			<tr>
				<td valign="top" width="40%">
					<b>Future Motion, Inc.</b>
		</td>
</tr><tr><td></td></tr>
</tbody></table>
<br><table align="center" width="99%" border="1" rules="all" cellpadding="5" cellspacing="0">
<tbody><tr><td style="font-weight:bold; width=94; white-space:nowrap">Date Filed</td>
<th>#</th><td style="font-weight:bold">Docket Text</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right"><a href="https://ecf.flmd.uscourts.gov/doc1/047027144423" onclick="goDLS('/doc1/047027144423','428805','3','','1','1','','','');return(false);">1</a>&nbsp;</td><td valign="top"><!--SB--><font color="CD3278">COMPLAINT <i></i> against Future Motion, Inc. with Jury Demand (Filing fee $405 receipt number AFLMDC-22190945) filed by Maija Hahn, as PNG of CH. (Attachments: # <a href="https://ecf.flmd.uscourts.gov/doc1/047127144424" onclick="goDLS('/doc1/047127144424','428805','3','','1','1','','','');return(false);">1</a> Civil Cover Sheet, # <a href="https://ecf.flmd.uscourts.gov/doc1/047127144425" onclick="goDLS('/doc1/047127144425','428805','3','','1','1','','','');return(false);">2</a> Proposed Summons)(Jensen, Daniel)</font> (Entered: 06/12/2024)</td></tr>
<tr><td width="94" valign="top" nowrap="">06/12/2024</td><td style="white-space:nowrap" valign="top" align="right">2&nbsp;</td><td valign="top"><!--SB--> NEW CASE ASSIGNED to Judge Wendy W. Berger and Magistrate Judge Daniel C. Irick. New case number: 6:24-cv-1088-WWB-DCI. (RLK) (Entered: 06/12/2024)</td></tr>
</tbody></table><br>
<hr><center><table border="1" bgcolor="white" width="400"><tbody><tr><th colspan="4"><font size="+1" color="DARKRED">PACER Service Center </font></th></tr><tr><th colspan="4"><font color="DARKBLUE">Transaction Receipt </font></th></tr><tr></tr><tr></tr><tr><td colspan="4" align="CENTER"><font size="-1" color="DARKBLUE">06/12/2024 22:18:41</font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> PACER Login: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> gratefuldave </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Client Code: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE">  </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Description: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> Docket Report </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Search Criteria: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 6:24-cv-01088-WWB-DCI     </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Billable Pages: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 1 </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Cost: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 0.10 </font></td></tr><tr></tr><tr></tr></tbody></table></center></div></body></html>"""


def parse_html(html):
    soup = BeautifulSoup(html, 'html.parser')

    def strip_whitespace(text):
        return ' '.join(text.strip().split()) if text else ''

    def format_phone_number(phone):
        # Convert (XXX) XXX-XXXX to XXX-XXX-XXXX format
        phone = re.sub(r'\((\d{3})\) (\d{3})-(\d{4})', r'\1-\2-\3', phone)
        # Convert XXX/XXX-XXXX to XXX-XXX-XXXX format
        phone = re.sub(r'(\d{3})/(\d{3})-(\d{4})', r'\1-\2-\3', phone)
        # Add more patterns as needed

        return phone

    def process_attorney_parts(parts, previous_attorneys):
        attorney_info = {}
        attorney_info['attorney_name'] = parts[0]
        attorney_info['law_firm'] = parts[1]

        if attorney_info['law_firm'] == '(See above for address)':
            for previous_attorney in previous_attorneys:
                if previous_attorney['attorney_name'] == attorney_info['attorney_name']:
                    return previous_attorney.copy()
        else:
            address1 = parts[2]
            # Check if the city, state zip is in parts[3]
            if re.search(r'[A-Z]{2} \d{5}', parts[3]):
                city_state_zip = parts[3].split(',')
                attorney_info['city'] = city_state_zip[0].strip()
                state_zip = re.search(r'([A-Z]{2})\s*(\d{5})', city_state_zip[1])
                if state_zip:
                    attorney_info['state'] = state_zip.group(1)
                    attorney_info['zip_code'] = state_zip.group(2)
                attorney_info['address1'] = address1.split(',')[0]
                if ',' in address1:
                    attorney_info['address2'] = address1.split(',', 1)[1].strip()
            else:
                attorney_info['address1'] = address1
                attorney_info['address2'] = parts[3]

            attorney_info['phone'] = format_phone_number(parts[4].strip()) if parts[4].strip() else ''
            attorney_info['fax'] = ''
            attorney_info['email'] = ''
            attorney_info['lead_attorney'] = 'LEAD ATTORNEY' in parts

            for part in parts:
                if part.startswith('Fax:'):
                    attorney_info['fax'] = format_phone_number(part.split('Fax:')[-1].strip())
                elif part.startswith('Email:'):
                    attorney_info['email'] = part.split('Email:')[-1].strip()

        return attorney_info

    case_info = {}
    plaintiffs = []
    defendants = []
    previous_attorneys = []
    case_details = []

    main_content = soup.find('div', id='cmecfMainContent')

    if not main_content:
        return []

    case_info['flags'] = []
    first_table = main_content.find('table')
    if first_table and first_table.find('td', align='right'):
        spans = first_table.find_all('span')
        case_info['flags'] = [strip_whitespace(span.text) for span in spans]

    h3_element = main_content.find('h3', align='center')
    if h3_element:
        h3_text = h3_element.decode_contents().replace('<br>', '').split('<br/>\n')
        if 'U.S. District Court' in h3_text[0]:
            court_and_office = h3_text[1].split('(')
            case_info['court_name'] = strip_whitespace(court_and_office[0])
            case_info['office'] = strip_whitespace(court_and_office[1].strip(')')) if len(
                court_and_office) > 1 else ''
            if 'CIVIL DOCKET FOR CASE #' in h3_text[2]:
                case_info['docket_num'] = strip_whitespace(h3_text[2].split(':')[-1])

    case_table = h3_element.find_next('table')
    if case_table:
        rows = case_table.find_all('td', valign='top')
        for row in rows:
            case_details.extend(row.text.split('\n'))
    case_info['versus'] = case_details[0]
    case_details.pop(0)

    if len(case_details) >= 2:
        for detail in case_details:
            if 'Assigned to:' in detail:
                case_info['assigned_to'] = strip_whitespace(detail.split(':')[1])
            elif 'Referred to:' in detail:
                case_info['referred_to'] = strip_whitespace(detail.split(':')[1])
            elif 'Case in other court:' in detail:
                case_info['case_in_other_court'] = strip_whitespace(detail.split(':')[1])
            elif 'Date Filed:' in detail:
                case_info['date_filed'] = strip_whitespace(detail.split(':')[1])
            elif 'Jury Demand:' in detail:
                case_info['jury_demand'] = strip_whitespace(detail.split(':')[1])
            elif 'Nature of Suit:' in detail:
                case_info['nos'] = strip_whitespace(detail.split(':')[1])
            elif 'Jurisdiction:' in detail:
                case_info['jurisdiction'] = strip_whitespace(detail.split(':')[1])
            elif 'Demand:' in detail:
                case_info['demand'] = strip_whitespace(detail.split(':')[1])
            elif 'Lead case:' in detail:
                case_info['lead_case'] = detail.split(':', 1)[1].strip() if ':' in detail else None
            elif 'Cause:' in detail:
                case_info['cause'] = detail.split(':', 1)[1].strip() if ':' in detail else None

    attorney_and_parties_table = case_table.find_next('table')
    while attorney_and_parties_table:
        first_row = attorney_and_parties_table.find('tr')
        while first_row:
            row_text = first_row.text
            if 'Plaintiff' in row_text:
                first_row = first_row.find_next('tr')
                if first_row:
                    plaintiff_td, _, attorney_td = first_row.find_all('td')
                    plaintiff = {'name': strip_whitespace(plaintiff_td.find('b').text), 'attorneys': []}
                    attorneys = attorney_td.text.split('\n')
                    attorneys = [re.sub(r'\s+', ' ', item.strip()) for item in attorneys if
                                 item.strip() and not item.startswith(
                                     'United Sta')]  # Remove excess whitespace and empty parts

                    attorney_info = []
                    for attorney in attorneys:
                        if attorney == 'ATTORNEY TO BE NOTICED':
                            if attorney_info:
                                plaintiff['attorneys'].append(process_attorney_parts(attorney_info, previous_attorneys))
                            attorney_info = []
                        else:
                            attorney_info.append(attorney)
                    if attorney_info:
                        attorney_record = process_attorney_parts(attorney_info, previous_attorneys)
                        plaintiff['attorneys'].append(attorney_record)
                        previous_attorneys.append(attorney_record)
                    plaintiffs.append(plaintiff)
            elif 'Defendant' in row_text:
                first_row = first_row.find_next('tr')
                while first_row:
                    row_text = first_row.text.strip()
                    if 'Defendant' in row_text:
                        first_row = first_row.find_next('tr')
                        continue
                    if not row_text:  # Skip empty rows
                        first_row = first_row.find_next('tr')
                        continue
                    defendant_td = first_row.find('td')
                    if defendant_td and defendant_td.find('b') and defendant_td.find('b').text.strip():
                        defendants.append(strip_whitespace(defendant_td.find('b').text))
                    first_row = first_row.find_next('tr')

            first_row = first_row.find_next('tr') if first_row else None

        attorney_and_parties_table = attorney_and_parties_table.find_next('table')

    result = {
        'case_info': case_info,
        'plaintiffs': plaintiffs,
        'defendants': defendants
    }

    return result


pprint(parse_html(html3))

# Types: COMPLAINT. Special Master in


# handle_proceedings_not_available: Proceedings for case 1:24-cv-04900 are not available

# First table exists? Get flags.

# First line:
# Types: COMPLAINT filing_date. Need to check with JPML for SYZ.
#   SHORT-FORM COMPLAINT

# Filings:
# How to handle first amended or amended complaint?
# Master Short Form Complaint, Short-Form Complaint
#  Notice of potential tag-along
# Type:
