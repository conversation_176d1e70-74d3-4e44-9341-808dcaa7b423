import os
import re
from datetime import datetime
import pandas as pd
import requests
from dotenv import load_dotenv
from urllib.parse import quote


class FacebookAdLibraryAPI:
    API_BASE_URL = "https://graph.facebook.com/{}/ads_archive"
    DEFAULT_API_VERSION = "v19.0"

    def __init__(self, access_token, api_version=None):
        self.access_token = access_token
        self.api_version = api_version or self.DEFAULT_API_VERSION

    def query_ads(self, params, limit=500):
        params.setdefault('ad_reached_countries', ['US'])  # Default to US if not specified
        url = self.API_BASE_URL.format(self.api_version)
        all_ads = []
        next_page = url

        while next_page:
            response = requests.get(next_page, params=params)
            if response.status_code != 200:
                raise Exception(f"API Error: {response.json().get('error', 'Unknown error')}")

            data = response.json()
            ads = data.get('data', [])
            all_ads.extend(ads)

            paging = data.get('paging', {})
            next_page = paging.get('next')
            params = {}  # Clear params because next_page URL contains all necessary parameters

        return all_ads

    def get_page_id_by_name(self, page_name):
        params = {
            'access_token': self.access_token,
            'fields': 'id,page_id,page_name',
            'search_terms': quote(page_name),  # Ensure the name is URL-encoded
            'ad_type': 'ALL',
            'ad_reached_countries': ['US']  # Specify the country as required
        }
        url = self.API_BASE_URL.format(self.api_version)
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            if data.get('data'):  # Check if data field is present
                return data['data'][0].get('id')  # Safely access the first item and id
            else:
                print("No data returned from API:", data)  # Debugging output
        else:
            print("Failed to fetch page ID:", response.status_code, response.text)  # Error details

        return None

    def query_ads_by_keywords(self, keywords, fields=None):
        params = {
            'access_token': self.access_token,
            'fields': fields or 'id,page_name,ad_creative_bodies,ad_delivery_start_time',
            'search_terms': quote(keywords),
            'ad_type': 'ALL',
            'ad_reached_countries': ['US'],
            'limit': 25
        }
        return self.query_ads(params)

    def query_ads_by_page_id(self, page_id, fields=None):
        params = {
            'access_token': self.access_token,
            'fields': fields or 'id,page_name,ad_creative_body,ad_delivery_start_time',
            'search_page_ids': [int(page_id)],
            'status': 'ACTIVE',
            'ad_type': 'ALL',
        }
        return self.query_ads(params)


if __name__ == "__main__":
    load_dotenv()
    access_token = os.getenv('FACEBOOK_API_KEY')
    fb_api = FacebookAdLibraryAPI(access_token)

    # Get Page ID by page name
    page_name = "Get Guardian Legal"
    page_id = fb_api.get_page_id_by_name(page_name)
    if page_id:
        print(f"Page ID for '{page_name}': {page_id}")
    else:
        print(f"No results found for page name: {page_name}")

    try:
        ads_by_page_id = fb_api.query_ads_by_page_id(page_id)
        print("Ads found by page ID:")
        for ad in ads_by_page_id:
            print(ad)
    except Exception as e:
        print(f"Error querying ads by page ID: {e}")

    # Optionally, query ads by keywords
    # Uncomment and adjust if you want to use it
    # try:
    #     ads_by_keywords = fb_api.query_ads_by_keywords("Morgan and Morgan")
    #     print("Ads found by keywords:")
    #     for ad in ads_by_keywords:
    #         print(ad)
    # except Exception as e:
    #     print(f"Error querying ads by keywords: {e}")
    #
    #     ads_by_page_id = fb_api.query_ads_by_page_id(page_id)
    #     print("Ads found by page ID:")
    #     for ad in ads_by_page_id:
    #         print(ad)
    # except Exception as e:
    #     print(f"Error querying ads by page ID: {e}")

    # Query ads by keywords
    # try:
    #     ads_by_keywords = fb_api.query_ads_by_keywords("Morgan and Morgan")
    #     print("Ads found by keywords:")
    #     for ad in ads_by_keywords:
    #         print(ad)
    # except Exception as e:
    #     print(f"Error querying ads by keywords: {e}")
