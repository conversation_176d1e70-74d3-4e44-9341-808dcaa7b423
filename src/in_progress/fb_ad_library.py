import os
import re
from datetime import datetime
import pandas as pd
import requests
from dotenv import load_dotenv

class FacebookAdLibraryAPI:
    API_BASE_URL = "https://graph.facebook.com/{}/ads_archive"
    DEFAULT_API_VERSION = "v19.0"

    def __init__(self, access_token, api_version=None):
        self.access_token = access_token
        self.api_version = api_version or self.DEFAULT_API_VERSION

    def query_ads(self, params, limit=500):
        url = self.API_BASE_URL.format(self.api_version)
        all_ads = []
        next_page = url

        while next_page:
            response = requests.get(next_page, params=params)
            if response.status_code != 200:
                raise Exception(f"API Error: {response.json().get('error', 'Unknown error')}")

            data = response.json()
            ads = data.get('data', [])
            all_ads.extend(ads)

            paging = data.get('paging', {})
            next_page = paging.get('next')
            params = {}  # clear params because next_page URL contains all necessary parameters

        return all_ads

    @staticmethod
    def extract_ad_archive_id(ad_snapshot_url):
        """
        Extract ad_archive_id from ad_snapshot_url
        """
        return re.search(r"/\?id=([0-9]+)", ad_snapshot_url).group(1)

    @staticmethod
    def filter_ads_by_date(ads, after_date):
        """
        Filter ads to include only those that started after a specific date.
        """
        after_timestamp = datetime.strptime(after_date, "%Y-%m-%d").timestamp()
        filtered_ads = [ad for ad in ads if
                        datetime.strptime(ad["ad_delivery_start_time"], "%Y-%m-%d").timestamp() >= after_timestamp]
        return filtered_ads


# Usage Example
if __name__ == "__main__":
    load_dotenv()
    access_token = os.getenv('FACEBOOK_API_KEY')
    fb_api = FacebookAdLibraryAPI(access_token)
    params = {
        'access_token': access_token,
        'fields': 'id,page_name,page_id,ad_creative_bodies,ad_creative_link_captions,ad_creative_link_descriptions,ad_creative_link_titles,ad_snapshot_url,ad_delivery_start_time',
        'search_terms': 'Ankin Law Offices',
        'ad_reached_countries': ['US'],
        'active_status': ['ACTIVE'],
        # 'search_page_ids': ["291843034020037"],
        'limit': 30,
        'ad_type': ['ALL'],
        'ad_delivery_date_min': '2024-05-01',
        'search_type': 'KEYWORD_UNORDERED'
    }

    ads = fb_api.query_ads(params=params)

    col_names = 'id,page_name,page_id,ad_creative_bodies,ad_creative_link_captions,ad_creative_link_descriptions,ad_creative_link_titles,ad_snapshot_url,ad_delivery_start_time'.split(
        ',')
    columns_to_convert = [
        'ad_creative_bodies',
        'ad_creative_link_captions',
        'ad_creative_link_descriptions',
        'ad_creative_link_titles'
    ]
    # Create DataFrame
    df = pd.DataFrame(ads, columns=col_names)
    for col in columns_to_convert:
        df[col] = df[col].apply(lambda x: x[0] if isinstance(x, list) and len(x) == 1 else x)

    print("Updated DataFrame:")
    print(df)

    print(df.head())



