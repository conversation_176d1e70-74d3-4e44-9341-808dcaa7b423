# Make imports conditional to avoid breaking everything when a single module is missing
from typing import TYPE_CHECKING

# These imports are only for type checking / linter support
# They help PyCharm understand our lazy-loading and module structure without affecting runtime
if TYPE_CHECKING:  # pragma: no cover
    # Using noqa to prevent linters from complaining about unused imports
    from .pacer import (  # noqa: F401
        PacerNavigator,
        PacerAuthenticator,
        PacerFileManager,
        DocketProcessor,
        ReportHandler,
        BrowserService,
        CaseRelevanceEngine,
        CaseTransferHandler,
        PacerDocumentDownloader,
        load_config_local,
        get_court_ids_async
    )
def safe_import(module_path, module_name):
    try:
        module = __import__(module_path, fromlist=[module_name])
        globals()[module_name] = getattr(module, module_name)
        return True
    except (ImportError, AttributeError):
        print(f"Warning: Could not import {module_name} from {module_path}")
        return False

# Import core utilities first
try:
    from .utils import try_remove, extract_from_zip, ensure_dir_exists
except ImportError as e:
    print(f"Failed to import try_remove: {e}")
    try_remove = None

# Import the required modules for fb_ad_categorizer
try:
    from .fb_archive_manager import FBAdArchiveManager
    from .deepseek_client_aiohttp import (
        DeepSeekClient,
        ServiceUnavailableError as DeepSeekServiceUnavailableError,
        LLMResponseError as DeepSeekLLMResponseError,
        ConfigurationError as DeepSeekConfigurationError,
        FreeTierRateLimitError as DeepSeekFreeTierRateLimitError
    )
    from .gpt_client_aiohttp import (
        GPTClient,
        ServiceUnavailableError as GPTServiceUnavailableError,
        RequestException as GPTRequestException,
        RetryableError as GPTRetryableError
    )
except ImportError as e:
    print(f"Failed to import FB Ad Categorizer dependencies: {e}")

# Import just what's needed for the core functionality
try:
    from .config import *
    # from .post_processing import PostProcessor
    from .s3_manager import S3Manager, S3_ACCESS_DENIED_MARKER
    from .llava_vision import LlavaImageExtractor
    from .data_transformer.law_firm_processor import LawFirmProcessor
    from .data_transformer.docket_processor import DocketProcessor
except ImportError as e:
    print(f"Critical import error: {str(e)}")

# These imports are used by main.py directly
try:
    from .pacer.orchestrator import PacerOrchestrator, load_config_local
    from .pacer.pacer_utils import get_court_ids_async
    # from .fb_ads import FacebookAdsOrchestrator # Moved to fb_ads
    from .report_generator import ReportGenerator
    from .deepseek_client import DeepSeekClient
    from .data_transformer.docket_file_manager import DocketFileManager
    from .html_case_parser import HTMLCaseParser
    from .cleanup_utils import *
    # from .fb_ads import FacebookSessionManager # Moved to fb_ads
    from .utils.date import DateUtils
    # from .fb_ads import FacebookAPIClient # Moved to fb_ads
    # from .image_handler import ImageHandler # Moved to fb_ads
    from .pdf_extractor import PDFExtractor
    from .ad_df_processor import AdDataFrameProcessor
    # from .fb_ads import ProcessingTracker # Moved to fb_ads
    # from .fb_ads import AdProcessor # Moved to fb_ads
    from .utils.law_firm import LawFirmNameHandler as LawFirmUtils
    from .docket_activity_manager import DocketActivityManager
    from .data_transformer.law_firm_processor import LawFirmProcessor
    from .data_transformer.cached_pdf_data import CachedPdfData
    from .data_transformer.docket_validator import DocketValidator
    from .data_transformer.litigation_classifier import LitigationClassifier
    # from .fb_ads import FBImageHashManager # Moved to fb_ads
except ImportError:
    pass

# Import modules moved to src.lib.fb_ads
try:
    from .fb_ads.categorizer import FBAdCategorizer
    from .fb_ads.classifier import LegalAdAnalyzer
    from .fb_ads.orchestrator import FacebookAdsOrchestrator
    from .fb_ads.processing_tracker import ProcessingTracker
    from .fb_ads.processor import AdProcessor
    from .fb_ads.api_client import FacebookAPIClient
    from .fb_ads.image_handler import ImageHandler
    from .fb_ads.image_utils import (
        FBImageHashManager,
        calculate_image_hash,
        extract_ids_from_key
    )
    from .fb_ads.session_manager import FacebookSessionManager, SSLAdapter
except ImportError as e:
    print(f"Failed to import modules from src.lib.fb_ads: {e}")

# Add imports for PACER components
try:
    from .pacer import (
        PacerNavigator,
        PacerAuthenticator,
        PacerFileManager,
        DocketProcessor,  # Note: DocketProcessor is imported from .pacer here
        ReportHandler,
        BrowserService
    )
except ImportError as e:
    # This would catch issues if '.pacer' itself cannot be imported as a module
    print(f"Failed to import modules from src.lib.pacer (ImportError): {e}")
except AttributeError as e:
    # This will catch the AttributeError raised by pacer/__init__.py's __getattr__
    # if the lazy loading failed internally.
    print(f"Failed to access attribute from src.lib.pacer (AttributeError): {e}")
    print("This often means an internal import within the 'pacer' sub-package failed.")
    print("Check console output for 'ERROR: Failed to import PACER component...' messages and tracebacks.")


__all__ = [
    'try_remove',  # Make sure it's listed first since it's a core utility
    'PostProcessor',
    'S3Manager',
    # 'ReportGenerator',
    'DocketFileManager',
    'HTMLCaseParser',
    'LlavaImageExtractor',
    'LawFirmProcessor',
    'PDFExtractor',
    # 'AdDataFrameProcessor',
    'DocketProcessor',
    'FBAdArchiveManager',
    'DeepSeekClient',
    'GPTClient',
    'DeepSeekServiceUnavailableError',
    'DeepSeekLLMResponseError',
    'DeepSeekConfigurationError',
    'DeepSeekFreeTierRateLimitError',
    'GPTServiceUnavailableError',
    'GPTRequestException',
    'DocketProcessor',
    'LawFirmProcessor',
    'CachedPdfData',
    'DocketValidator',
    'LitigationClassifier',
    'S3_ACCESS_DENIED_MARKER',
    # Expose components from fb_ads submodule
    'FBAdCategorizer',
    'LegalAdAnalyzer',
    'FacebookAdsOrchestrator',
    'ProcessingTracker',
    'AdProcessor',
    'FacebookAPIClient',
    'ImageHandler',
    'FBImageHashManager',
    'calculate_image_hash',
    'extract_ids_from_key',
    'FacebookSessionManager',
    'SSLAdapter',
    # Expose PACER components
    'PacerNavigator',
    'PacerAuthenticator',
    'PacerFileManager',
    'DocketProcessor',
    'ReportHandler',
    'BrowserService'
]

