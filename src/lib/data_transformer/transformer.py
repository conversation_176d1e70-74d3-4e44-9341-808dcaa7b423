# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
Post-processing module for legal document processing pipeline.
Applies simplified workflow logic as requested.
"""
import asyncio
import copy
import inspect
import logging
import os
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Set, Union, Tuple, Any  # Added Tuple, Any

# --- Third-Party Imports ---
import aiohttp
import pandas as pd
from rich.console import Console
from tqdm.asyncio import tqdm as tqdm_async

# --- Local Imports ---
# Assuming these are correctly located relative to the execution context
# If running as a script, adjust paths or ensure PYTHONPATH is set
try:
    # Use relative imports for modules in the same package
    from .cached_pdf_data import CachedPdfData
    from .docket_file_manager import DocketFileManager
    from .docket_processor import DocketProcessor
    from .docket_validator import DocketValidator
    from .file_handler import FileHandler
    from .law_firm_processor import LawFirmProcessor
    from .transfer_handler import TransferHandler
    from .uploader import Uploader
    # Use absolute imports for modules outside the package
    from src.lib.utils import try_remove, extract_from_zip, ensure_dir_exists

    print(f"DEBUG: Importing DocketFileManager from: {inspect.getfile(DocketFileManager)}")

    from src.lib.deepseek_client_aiohttp import DeepSeekClient
    from src.lib.gpt_client_aiohttp import GPTClient
    # Relative imports might fail if run directly as a script, use absolute if needed
    # Use '.' for same-directory imports when running as part of a package
    from src.lib.config import load_config
    from .mdl_processor import MDLProcessor
    from .litigation_classifier import LitigationClassifier  # Ensure this is imported

    # Import DocketProcessor here to avoid circular reference
    try:
        # First try direct imports from the source modules
        from src.lib.pacer_manager import PacerManager
    except ImportError:
        print("Warning: Failed to import PacerManager directly, using dummy implementation.")


        class PacerManager:
            """Dummy PacerManager class used when the original is not available."""

            def __init__(self, *args, **kwargs): pass

    # Import other required modules
    from src.lib.district_courts_manager import DistrictCourtsManager
    from src.lib.s3_manager import S3Manager
    from src.lib.core.html.html_data_updater import HTMLDataUpdater
    from src.lib.core.html.html_case_parser import HTMLCaseParser
    from src.lib.mistral_ocr import MistralOCR

except ImportError as e:
    logging.critical(
        f"PostProcessing4 Import Error: {e}. Check PYTHONPATH or script execution location. Some features might be disabled.")


    # Define dummy classes or exit if critical components are missing
    # For example:
    class Dummy:
        def __init__(self, *args, **kwargs): pass

        def __call__(self, *args, **kwargs): pass

        def __getattr__(self, name): return lambda *args, **kwargs: None  # Avoid AttributeError chain


    # Assign dummies if needed, e.g.:
    # if 'DocketFileManager' not in locals(): DocketFileManager = Dummy
    # ... etc. for all failed imports ...
    sys.exit(f"Critical import failed: {e}")  # Or handle more gracefully

try:
    from rich.traceback import install as install_rich_traceback

    install_rich_traceback()
    console = Console()
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    console = None
    logging.warning("Rich library not found. Falling back to standard logging.")

# Constants (Example - adjust if needed)
DISTRICT_COURTS_PATH = '/config/courts/district_courts.json'  # Keep absolute if needed for specific user
PROJECT_ROOT = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__))))  # More robust way to find project root
DATA_PATH = os.path.join(PROJECT_ROOT, 'data')


# --- Reporter Class Definition ---
# (No changes needed in this class)
class Reporter:
    """Generates and logs summary reports of processing results."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def generate_summary(self, process_summary: List[Dict], upload_results: Dict[str, List], initial_file_count: int):
        """Generate a summary of the processing and upload results."""
        processed_count = 0
        if process_summary:
            processed_files = [entry for entry in process_summary if
                               isinstance(entry, dict) and entry.get('status', '').startswith('Processed')]
            processed_count = len(processed_files)
        else:
            processed_count = 0
        failed_skipped_count = initial_file_count - processed_count
        uploaded_count = len(upload_results.get('uploaded', []))
        skipped_upload_count = len(upload_results.get('skipped', []))
        failed_upload_count = len(upload_results.get('failed', []))
        total_upload_outcomes = uploaded_count + skipped_upload_count + failed_upload_count
        self.logger.info("--- Overall Processing & Upload Summary ---")
        self.logger.info(f"Processing - Initially Found: {initial_file_count} files")
        self.logger.info(f"           - Succeeded (Processed/Renamed/No Change): {processed_count}")
        self.logger.info(f"           - Skipped/Failed: {failed_skipped_count}")
        self.logger.info(
            f"Uploads    - Succeeded: {uploaded_count}, Skipped: {skipped_upload_count}, Failed: {failed_upload_count} (Total outcomes reported: {total_upload_outcomes})")
        if failed_skipped_count > 0: self.logger.warning(
            f"Check logs for details on the {failed_skipped_count} processing failures/skips.")
        if failed_upload_count > 0: self.logger.error(
            f"Check logs (including Uploader summary) for details on the {failed_upload_count} upload failures.")
        self.logger.info("--- End Overall Summary ---")


# --- PostProcessorV2 Class Definition ---
class DataTransformer:
    """
    Orchestrates the post-processing workflow for legal dockets.
    Applies simplified workflow logic where upload is handled externally.
    Refactored _process_single_file_async for maintainability.
    """

    def __init__(self, config: Dict, logger: logging.Logger):
        """Initializes all components needed for post-processing."""
        self.start_time = time.time()
        self.config = config
        self.logger = logger  # DataTransformer uses the logger passed to it
        self.docket_summary: Optional[List[Dict]] = None

        # --- Component Initialization ---
        self.logger.info("Initializing DataTransformer components...")
        project_root = config.get('directories', {}).get('base_dir', PROJECT_ROOT)
        self.mdl_path = config.get('files', {}).get('mdl_lookup', os.path.join(project_root, 'src', 'config', 'mdl',
                                                                               'mdl_lookup.json'))

        mdl_litigations_df = self._load_mdl_data(self.mdl_path)

        self.s3_manager = S3Manager(config, config.get('bucket_name'))
        self.pacer_db = PacerManager(config)
        self.district_court_db = DistrictCourtsManager(config)
        # Removed logger argument from FileHandler instantiation
        self.file_handler = FileHandler(config, self.s3_manager)
        self.download_dir = self.file_handler.download_dir
        # Removed logger argument from DocketFileManager instantiation
        self.docket_manager = DocketFileManager(self.download_dir, self.file_handler)

        self.pdf_processor = self._initialize_pdf_processor(config)

        self.llm_provider = self.config.get('llm_provider', 'deepseek').lower()
        self.llm_client = self._initialize_llm_client(self.llm_provider, config)

        # Removed logger argument from LawFirmProcessor instantiation
        self.law_firm_processor = LawFirmProcessor(config)
        # Removed logger argument from HTMLDataUpdater instantiation
        self.html_data_updater = HTMLDataUpdater(config, self.s3_manager, self.pacer_db)
        # Removed logger argument from DocketValidator instantiation
        self.validator = DocketValidator()
        # Removed logger argument from Reporter instantiation
        self.reporter = Reporter()
        self.litigation_classifier = LitigationClassifier()
        self.pdf_cache = CachedPdfData()
        # Removed logger argument from MDLProcessor instantiation
        self.mdl_processor = MDLProcessor(mdl_litigations=mdl_litigations_df, mdl_path=self.mdl_path,
                                          file_handler=self.file_handler, gpt=self.llm_client,
                                          config=self.config, litigation_classifier=self.litigation_classifier,
                                          pdf_cache=self.pdf_cache, download_dir=self.download_dir,
                                          district_court_db=self.district_court_db)
        # Removed logger argument from TransferHandler instantiation
        self.transfer_handler = TransferHandler(config, self.pacer_db, self.district_court_db, self.mdl_processor)
        # Removed logger argument from DocketProcessor instantiation
        self.docket_processor = DocketProcessor(config=self.config, llm_client=self.llm_client,
                                                law_firm_processor=self.law_firm_processor,
                                                transfer_handler=self.transfer_handler,
                                                html_data_updater=self.html_data_updater, validator=self.validator,
                                                file_handler=self.file_handler, pdf_processor=self.pdf_processor)
        # Removed logger argument from Uploader instantiation
        self.uploader = Uploader(config, self.s3_manager, self.pacer_db, self.file_handler)

        self.incomplete_summaries_file = os.path.join(self.download_dir, 'incomplete_summaries.json')
        self.skip_files = set()
        self.num_workers = None
        self.initial_file_count = 0

        initialization_time = time.time() - self.start_time
        self.logger.info(f"DataTransformer initialization complete ({initialization_time:.2f} seconds)")

    # --- Initialization Helper Methods ---
    def _load_mdl_data(self, mdl_path: str) -> pd.DataFrame:
        """Loads MDL data from the specified JSON path."""
        default_columns = ['mdl_num', 'litigation', 'description', 'short_summary', 'summary']
        try:
            if os.path.exists(mdl_path):
                self.logger.info(f"Loading initial MDL data from: {mdl_path}")
                df = pd.read_json(mdl_path)
                if 'mdl_num' in df.columns:
                    df['mdl_num'] = df['mdl_num'].astype(str).str.replace(r'\.0$', '', regex=True)
                else:
                    self.logger.warning(f"Column 'mdl_num' not found in {mdl_path}. Using empty DataFrame.")
                    df = pd.DataFrame(columns=default_columns)
                # Ensure all required columns exist, add if missing
                for col in default_columns:
                    if col not in df.columns:
                        self.logger.warning(f"Adding missing required column '{col}' to MDL DataFrame.")
                        df[col] = None  # Add column with default None value
                return df  # Return all columns to ensure we have the data needed for title population
            else:
                self.logger.error(f"MDL lookup file not found: {mdl_path}. Using empty DataFrame.")
                return pd.DataFrame(columns=default_columns)
        except Exception as e_load:
            self.logger.error(f"Error loading MDL data from {mdl_path}: {e_load}", exc_info=True)
            return pd.DataFrame(columns=default_columns)

    def _initialize_pdf_processor(self, config: Dict) -> Optional[MistralOCR]:
        """Initializes the PDF Processor (MistralOCR)."""
        try:
            processor = MistralOCR(config, logger=self.logger)
            self.logger.info("MistralOCR PDF Processor initialized.")
            return processor
        except Exception as e:
            self.logger.error(f"Failed to initialize MistralOCR: {e}", exc_info=True)
            return None

    def _clean_duplicate_fields(self, data: Dict):
        """Remove duplicate fields while preserving necessary data"""
        if not isinstance(data, dict):
            self.logger.warning("Invalid data provided to _clean_duplicate_fields.")
            return data

        # Track removed fields for logging
        removed_fields = []

        # 1. Date field consolidation
        # Keep only FilingDate and AddedOn (both in YYYYMMDD format)
        if 'FilingDate' in data:
            # Ensure FilingDate is in YYYYMMDD format
            filing_date = data['FilingDate']
            if isinstance(filing_date, str) and len(filing_date) == 8 and filing_date.isdigit():
                # Already in correct format
                pass
            else:
                # Try to convert to YYYYMMDD
                try:
                    for fmt in ['%Y%m%d', '%m/%d/%Y', '%m/%d/%y', '%Y-%m-%d']:
                        try:
                            parsed_date = datetime.strptime(str(filing_date), fmt)
                            data['FilingDate'] = parsed_date.strftime('%Y%m%d')
                            break
                        except ValueError:
                            continue
                except Exception as e:
                    self.logger.warning(f"Failed to convert FilingDate '{filing_date}' to YYYYMMDD: {e}")

            # Remove redundant date fields
            for field in ['DateFiled', 'AddedDate', 'AddedDateIso']:
                if field in data:
                    removed_fields.append(field)
                    del data[field]

        # Ensure AddedOn is in YYYYMMDD format
        if 'AddedOn' in data:
            added_on = data['AddedOn']
            if isinstance(added_on, str) and len(added_on) == 8 and added_on.isdigit():
                # Already in correct format
                pass
            else:
                # Try to convert to YYYYMMDD
                try:
                    for fmt in ['%Y%m%d', '%m/%d/%Y', '%m/%d/%y', '%Y-%m-%d']:
                        try:
                            parsed_date = datetime.strptime(str(added_on), fmt)
                            data['AddedOn'] = parsed_date.strftime('%Y%m%d')
                            break
                        except ValueError:
                            continue
                except Exception as e:
                    self.logger.warning(f"Failed to convert AddedOn '{added_on}' to YYYYMMDD: {e}")

        # 2. Cause field consolidation
        if 'Cause' in data and 'CauseFromReport' in data:
            # Keep only Cause, remove CauseFromReport
            removed_fields.append('CauseFromReport')
            del data['CauseFromReport']

        # 3. NOS field consolidation
        if 'Nos' in data and 'NosFromReport' in data:
            # Keep only Nos, remove NosFromReport
            removed_fields.append('NosFromReport')
            del data['NosFromReport']

        if removed_fields:
            self.logger.info(f"Removed duplicate fields: {', '.join(removed_fields)}")

        return data

    async def _run_law_firm_normalization_only(self) -> List[Dict]:
        """
        Special mode: Iterates through all JSON files in the target directory,
        applies law firm normalization using LawFirmProcessor, and saves them.
        This mode is triggered if config 'normalize_law_firm_names' is True
        and the 'post_process' argument to start() is False.
        """
        self.logger.info("Starting law firm normalization ONLY for all JSON files in the target directory.")
        run_start_time = time.time()

        # Ensure download_dir is set; FileHandler should have it.
        # Uses the same directory logic as the main processing.
        json_files_to_normalize = self.file_handler.get_json_files()  # Sync method, returns list of paths

        if not json_files_to_normalize:
            self.logger.warning("No JSON files found in the target directory for normalization-only mode.")
            return []

        # self.num_workers should have been set by the calling start() method.
        if not self.num_workers:  # Fallback, though start() should set it.
            self.num_workers = (os.cpu_count() or 1) * 2
            self.logger.warning(f"num_workers was not set, defaulting to {self.num_workers} for normalization mode.")

        semaphore = asyncio.Semaphore(self.num_workers)
        summary_results: List[Dict] = []

        async def normalize_single_file_law_firms(json_path: str) -> Dict:
            base_name = os.path.basename(json_path)
            async with semaphore:
                self.logger.debug(f"Normalizing law firms in: {base_name}")
                try:
                    data = await self.file_handler.load_json_async(json_path)
                    if data is None:
                        self.logger.error(f"Failed to load {base_name} for law firm normalization.")
                        return {'filename': base_name, 'status': 'Load Failed'}

                    # self.law_firm_processor.process_law_firms(data) modifies 'data' in-place.
                    # This method handles extraction, cleaning (which includes normalization),
                    # and setting 'law_firms' and 'law_firm' keys.
                    self.law_firm_processor.process_law_firms(data)

                    save_success = await self.file_handler.save_json_async(json_path, data)
                    if save_success:
                        self.logger.debug(f"Successfully normalized law firms and saved {base_name}")
                        return {'filename': base_name, 'status': 'Normalized'}
                    else:
                        self.logger.error(f"Failed to save {base_name} after law firm normalization.")
                        return {'filename': base_name, 'status': 'Save Failed'}
                except Exception as e:
                    self.logger.error(f"Error during law firm normalization for {base_name}: {e}", exc_info=True)
                    return {'filename': base_name, 'status': f'Error: {str(e)[:100]}'}  # Truncate long error messages

        tasks = [normalize_single_file_law_firms(jp) for jp in json_files_to_normalize]

        for future in tqdm_async(asyncio.as_completed(tasks), total=len(tasks), desc="Normalizing law firms",
                                 unit="file"):
            result = await future
            if result:  # Should always get a result dict from normalize_single_file_law_firms
                summary_results.append(result)

        elapsed_time = time.time() - run_start_time
        self.logger.info(f"Law firm normalization ONLY mode finished in {self._format_elapsed_time(elapsed_time)}.")

        # Log summary for this specific operation
        successful_count = sum(1 for r in summary_results if r['status'] == 'Normalized')
        failed_count = len(summary_results) - successful_count
        self.logger.info(f"--- Law Firm Normalization ONLY Mode Summary ---")
        self.logger.info(f"Total files attempted: {len(summary_results)}")
        self.logger.info(f"Successfully normalized and saved: {successful_count}")
        self.logger.info(f"Failed (load/save/error): {failed_count}")
        if failed_count > 0:
            self.logger.warning("Check logs for details on failed normalizations.")
        self.logger.info(f"--- End Law Firm Normalization ONLY Mode Summary ---")

        return summary_results

    async def _get_files_to_process(self, reprocess_files: Union[bool, List[str], Set[str]],
                                    start_from_incomplete: bool, skip_files: Set[str]) -> List[str]:
        """Determines the list of JSON file paths to process based on flags."""
        self.logger.info("Determining files to process...")
        self.logger.debug(
            f"Params: reprocess_files type={type(reprocess_files).__name__} ({len(reprocess_files) if isinstance(reprocess_files, (list, set)) else reprocess_files}), start_from_incomplete={start_from_incomplete}, skip_files count={len(skip_files)}")

        target_docket_dir = self.file_handler.get_target_docket_directory()
        if not target_docket_dir:
            self.logger.error("Target docket directory not found. Cannot get files to process.")
            return []
        normalized_target_dir = os.path.normpath(target_docket_dir)
        self.logger.debug(f"Normalized target directory: {normalized_target_dir}")

        files_to_process = []
        skip_set = set(os.path.basename(f) for f in skip_files) if skip_files else set()
        self.logger.debug(f"Skip set (basenames): {skip_set}")

        # --- STRATEGY 1: Specific List Provided (Non-Empty) ---
        if isinstance(reprocess_files, (list, set)) and reprocess_files:
            self.logger.info(
                f"Processing specific list of {len(reprocess_files)} files provided via 'reprocess_files'.")
            valid_files_from_list = []
            for file_path_or_name in reprocess_files:
                if not isinstance(file_path_or_name, str):
                    self.logger.warning(f"Skipping non-string item in reprocess_files list: {file_path_or_name}")
                    continue
                abs_path_input = os.path.abspath(file_path_or_name)
                normalized_path = os.path.normpath(abs_path_input)
                base_name = os.path.basename(normalized_path)
                self.logger.debug(
                    f"Checking file from list: '{file_path_or_name}' -> Normalized path: '{normalized_path}', Basename: '{base_name}'")
                if base_name in skip_set:
                    self.logger.debug(f"Skipping specified file due to skip_files list: {base_name}")
                    continue
                if await asyncio.to_thread(os.path.exists, normalized_path):
                    file_dir_normalized = os.path.normpath(os.path.dirname(normalized_path))
                    if file_dir_normalized == normalized_target_dir:
                        valid_files_from_list.append(normalized_path)
                        self.logger.debug(f"  -> Found and validated: {normalized_path}")
                    else:
                        self.logger.warning(
                            f"File '{normalized_path}' from reprocess_files list exists but is not in the target directory '{normalized_target_dir}'. Skipping.")
                else:
                    self.logger.warning(
                        f"File specified in 'reprocess_files' list not found (checked normalized path): {normalized_path}")
            if not valid_files_from_list:
                self.logger.warning(
                    "The provided 'reprocess_files' list contained no valid, existing files in the target directory.")
            files_to_process = valid_files_from_list
            self.logger.info(f"Selected {len(files_to_process)} files based *only* on 'reprocess_files' list.")
            return files_to_process  # Exit early

        # --- STRATEGY 2: Reprocess All OR Start From Incomplete ---
        elif reprocess_files is True or start_from_incomplete:
            all_json_files = self.file_handler.get_json_files()
            self.logger.info(
                f"Found {len(all_json_files)} total JSON files in {target_docket_dir} for potential processing (S2).")
            unskipped_files = [f for f in all_json_files if os.path.basename(f) not in skip_set]
            if len(skip_set) > 0:
                skipped_count = len(all_json_files) - len(unskipped_files)
                self.logger.info(
                    f"Applied skip_files filter: {skipped_count} files skipped. Remaining: {len(unskipped_files)}")

            if reprocess_files is True:
                self.logger.info("Selecting all unskipped files for processing (reprocess_files=True).")
                files_to_process = unskipped_files
            elif start_from_incomplete:  # This implies reprocess_files is not True (could be False or an empty list)
                self.logger.info("Filtering unskipped files for incomplete status (start_from_incomplete=True).")
                incomplete_files = []
                for f_path in unskipped_files:
                    try:
                        needs_proc, reason = self.file_handler.check_if_needs_processing(f_path)
                        if needs_proc:
                            self.logger.debug(
                                f"Identified incomplete file: {os.path.basename(f_path)} (Reason: {reason})")
                            incomplete_files.append(f_path)
                    except Exception as check_err:
                        self.logger.error(
                            f"Error checking processing status for {os.path.basename(f_path)}: {check_err}",
                            exc_info=True)
                        self.logger.warning(f"Skipping file {os.path.basename(f_path)} due to status check error.")
                files_to_process = incomplete_files
                self.logger.info(f"Selected {len(files_to_process)} incomplete files for processing.")

        # --- STRATEGY 3: Normal Run (Default behavior) ---
        # This 'else' covers cases not handled by S1 or S2, specifically:
        #   - reprocess_files=False (boolean) AND start_from_incomplete=False
        #   - reprocess_files=[] (empty list/set) AND start_from_incomplete=False
        else:
            self.logger.info(
                "Normal processing run: Selecting all unskipped JSON files. Individual status will be checked in the loop.")
            all_json_files = self.file_handler.get_json_files()  # Use sync version
            unskipped_files = [f for f in all_json_files if os.path.basename(f) not in skip_set]
            if len(skip_set) > 0:
                skipped_count = len(all_json_files) - len(unskipped_files)
                self.logger.info(
                    f"Applied skip_files filter: {skipped_count} files skipped. Remaining: {len(unskipped_files)}")
            else:
                self.logger.info(f"No skip_files specified. Initial file count from directory: {len(unskipped_files)}")

            self.logger.info(
                f"Selected {len(unskipped_files)} unskipped files. Individual processing status will be checked in the main loop.")
            files_to_process = unskipped_files

        self.logger.info(
            f"Final file list determination complete. Identified {len(files_to_process)} files for the processing loop.")
        if files_to_process:
            self.logger.debug(
                f"Files to process (first 5 normalized): {[os.path.normpath(f) for f in files_to_process[:5]]}")
        return files_to_process

    def _initialize_llm_client(self, provider: str, config: Dict) -> Union[GPTClient, DeepSeekClient]:
        """Initializes the appropriate LLM Client based on the provider."""
        try:
            if provider == 'gpt-4o-mini':
                client = GPTClient(config, logger=self.logger)
            elif provider == 'deepseek':
                client = DeepSeekClient(config, logger=self.logger)
            else:
                self.logger.warning(f"Unknown llm_provider '{provider}', defaulting to DeepSeek.")
                client = DeepSeekClient(config, logger=self.logger)
            self.logger.info(f"Initialized LLM Client: {type(client).__name__}")
            return client
        except Exception as e:
            self.logger.critical(f"Failed to initialize LLM Client ({provider}): {e}", exc_info=True)
            raise RuntimeError(f"Could not initialize LLM client: {e}") from e

    # --- Static Helper Methods ---
    @staticmethod
    def _format_elapsed_time(seconds: float) -> str:
        # --- This method remains unchanged ---
        if seconds < 0: return "0 seconds"
        td = timedelta(seconds=seconds)
        days, hours, minutes = td.days, td.seconds // 3600, (td.seconds // 60) % 60
        secs = td.seconds % 60 + td.microseconds / 1e6
        parts = []
        if days > 0: parts.append(f"{days} day{'s' if days > 1 else ''}")
        if hours > 0: parts.append(f"{hours} hour{'s' if hours > 1 else ''}")
        if minutes > 0: parts.append(f"{minutes} minute{'s' if minutes > 1 else ''}")
        if secs > 0 or not parts: parts.append(f"{secs:.1f} second{'s' if secs != 1 else ''}")
        return ", ".join(parts) if parts else "0 seconds"

    # --- Core Workflow Methods ---
    async def start(self, upload: bool = False,  # Parameter retained but upload logic removed
                    reprocess_files: Optional[Union[List[str], Set[str], bool]] = False,
                    skip_files: Optional[List[str]] = None,
                    start_from_incomplete: bool = False,
                    upload_types: Optional[List[str]] = None,  # Retained for potential future use / logging
                    post_process: bool = True,  # Keep this flag
                    num_workers: Optional[int] = None):
        """
        Start the asynchronous post-processing workflow.
        If 'post_process' argument is False, it checks for special operational modes:
        1. Law firm normalization ONLY (if config 'normalize_law_firm_names' is True).
        2. MDL title update ONLY (if config 'reprocess_mdl_num' is True).
        Otherwise, it performs the full enrichment phase.
        Upload is handled separately by MainProcessor.
        """
        run_start_time = time.time()
        self.logger.info(
            "=== Starting DataTransformer.start ===")
        self.logger.info(f"Run Params: post_process={post_process}, "
                         f"reprocess_files='{type(reprocess_files).__name__}' ({len(reprocess_files) if isinstance(reprocess_files, (list, set)) else reprocess_files}), "
                         f"start_from_incomplete={start_from_incomplete}, workers={num_workers}")
        if upload: self.logger.warning("Received upload=True, but upload logic is handled externally in this version.")

        self.skip_files = set(os.path.realpath(p) for p in skip_files) if skip_files else set()
        skip_basenames_set = set(os.path.basename(p) for p in self.skip_files) if self.skip_files else set()
        self.file_handler.set_skip_files(self.skip_files)
        self.num_workers = num_workers if num_workers and num_workers > 0 else (os.cpu_count() or 1) * 2

        # Check for special operational modes if post_process is False
        normalize_law_firm_names_flag = self.config.get('normalize_law_firm_names', False)
        reprocess_mdl_num_flag = self.config.get('reprocess_mdl_num', False)  # New flag from YAML

        self.logger.debug(f"Config flag 'normalize_law_firm_names': {normalize_law_firm_names_flag}")
        self.logger.debug(f"Config flag 'reprocess_mdl_num': {reprocess_mdl_num_flag}")

        if not post_process:
            if normalize_law_firm_names_flag and reprocess_mdl_num_flag:
                self.logger.warning(
                    "Both 'normalize_law_firm_names' and 'reprocess_mdl_num' flags are True in config, "
                    "and 'post_process' is False. Prioritizing 'law firm normalization ONLY' mode."
                )
                self.logger.info(
                    "Running in law firm normalization ONLY mode (normalize_law_firm_names=True, post_process=False).")
                return await self._run_law_firm_normalization_only()
            elif normalize_law_firm_names_flag:
                self.logger.info(
                    "Running in law firm normalization ONLY mode (normalize_law_firm_names=True, post_process=False).")
                return await self._run_law_firm_normalization_only()
            elif reprocess_mdl_num_flag:
                self.logger.info(
                    "Running in MDL title update ONLY mode (reprocess_mdl_num=True, post_process=False).")
                return await self._run_mdl_title_update_only_async()
            else:
                self.logger.info(
                    "post_process is False, but no special mode (law_firm_norm or mdl_title_update) is active. No operation will be performed.")
                return []  # Return empty list for no-op
        # --- End of special mode checks ---

        # Proceed with normal post-processing (enrichment phase)
        self.logger.info("Proceeding with standard post-processing (enrichment phase).")
        force_reprocess_all = reprocess_files is True
        specific_reprocess_set = set(os.path.realpath(p) for p in reprocess_files if isinstance(p, str)) if isinstance(
            reprocess_files, (list, set)) else set()
        self.logger.info(f"Internal Force Reprocess All Flag: {force_reprocess_all}")
        if specific_reprocess_set:
            self.logger.info(f"Specific files requested for reprocessing: {len(specific_reprocess_set)} items.")

        self.docket_summary = []
        session = None

        try:
            connector = aiohttp.TCPConnector(limit_per_host=self.num_workers)
            session = aiohttp.ClientSession(connector=connector)

            if hasattr(self.llm_client, 'set_session'):
                set_session_method = getattr(self.llm_client, 'set_session')
                if asyncio.iscoroutinefunction(set_session_method):
                    await set_session_method(session)
                else:
                    set_session_method(session)

            self.logger.info("Identifying files for processing using _get_files_to_process...")
            json_files_to_process = await self._get_files_to_process(
                reprocess_files=reprocess_files,
                start_from_incomplete=start_from_incomplete,
                skip_files=skip_basenames_set
            )

            self.initial_file_count = len(json_files_to_process)
            self.logger.info(f"Identified {self.initial_file_count} files needing processing based on flags.")

            if not json_files_to_process:
                self.logger.warning("No JSON files selected for processing based on flags.")
            else:
                self.logger.info(f"--- Starting main processing loop for {self.initial_file_count} files ---")
                self.docket_summary = await self.process_federal_filings_async(
                    json_files_to_process=json_files_to_process,
                    force_reprocess_all=force_reprocess_all,
                    specific_reprocess_set=specific_reprocess_set
                )

            elapsed_time = time.time() - run_start_time
            self.logger.info(
                f"=== DataTransformer.start (Processing Mode) COMPLETED in {self._format_elapsed_time(elapsed_time)} ===")
            processed_count = len(self.docket_summary) if self.docket_summary else 0
            failed_skipped_count = self.initial_file_count - processed_count
            self.logger.info("--- Processing Summary (from this run) ---")
            self.logger.info(f"Files Selected by Flags: {self.initial_file_count}")
            self.logger.info(f"Successfully Processed & Summarized: {processed_count}")
            self.logger.info(f"Skipped/Failed during Processing Loop: {failed_skipped_count}")
            self.log_docket_summary()

            return self.docket_summary or []

        except Exception as e:
            elapsed_time = time.time() - run_start_time
            self.logger.critical(
                f"DataTransformer.start (Processing Mode) failed after {self._format_elapsed_time(elapsed_time)}. Error: {e}",
                exc_info=True)
            raise
        finally:
            if session and not session.closed: await session.close(); self.logger.debug("AIOHTTP session closed.")
            if hasattr(self.llm_client, 'close_session') and asyncio.iscoroutinefunction(self.llm_client.close_session):
                await self.llm_client.close_session()
            elif hasattr(self.llm_client, 'session') and hasattr(self.llm_client.session,
                                                                 'close') and self.llm_client.session and not self.llm_client.session.closed:
                await self.llm_client.session.close()

    async def _run_mdl_title_update_only_async(self) -> List[Dict]:
        """
        Special mode: Iterates through all JSON files in the target directory,
        applies MDL title update using MDLProcessor.add_mdl_info, and saves them.
        This mode is triggered if config 'reprocess_mdl_num' is True
        and the 'post_process' argument to start() is False.
        """
        self.logger.info("Starting MDL title update ONLY for all JSON files in the target directory.")
        run_start_time = time.time()

        json_files_to_process = self.file_handler.get_json_files()

        if not json_files_to_process:
            self.logger.warning("No JSON files found in the target directory for MDL title update-only mode.")
            return []

        if not self.num_workers:
            self.num_workers = (os.cpu_count() or 1) * 2
            self.logger.warning(f"num_workers was not set, defaulting to {self.num_workers} for MDL title update mode.")

        # Ensure MDLProcessor's lookup table is ready
        # MDLProcessor itself handles lazy loading of its mdl_litigations if needed.
        if self.mdl_processor and (self.mdl_processor.mdl_litigations is None or self.mdl_processor.mdl_litigations.empty):
            self.logger.info("MDL litigation data in MDLProcessor is not loaded. Attempting to load for title update mode.")
            # Attempt to load it; _load_mdl_litigation is synchronous in MDLProcessor
            try:
                 # Accessing protected member, but necessary if direct reload is desired here.
                 # Or rely on add_mdl_info's internal check.
                self.mdl_processor.mdl_litigations = self.mdl_processor._load_mdl_litigation()
                if self.mdl_processor.mdl_litigations.empty:
                    self.logger.error("Failed to load MDL litigation data for MDLProcessor. Title updates may fail.")
                else:
                    self.logger.info("MDL litigation data loaded successfully for MDLProcessor.")
            except Exception as load_err:
                self.logger.error(f"Error loading MDL litigation data for MDLProcessor: {load_err}", exc_info=True)


        semaphore = asyncio.Semaphore(self.num_workers)
        summary_results: List[Dict] = []

        async def update_single_file_mdl_title(json_path: str) -> Dict:
            base_name = os.path.basename(json_path)
            async with semaphore:
                self.logger.debug(f"Updating MDL title in: {base_name}")
                try:
                    data = await self.file_handler.load_json_async(json_path)
                    if data is None:
                        self.logger.error(f"Failed to load {base_name} for MDL title update.")
                        return {'filename': base_name, 'status': 'Load Failed'}

                    if self.mdl_processor:
                        # self.mdl_processor.add_mdl_info(data) modifies 'data' in-place.
                        self.mdl_processor.add_mdl_info(data)
                    else:
                        self.logger.error(f"MDLProcessor not available. Cannot update MDL title for {base_name}.")
                        return {'filename': base_name, 'status': 'MDLProcessor Missing'}


                    save_success = await self.file_handler.save_json_async(json_path, data)
                    if save_success:
                        self.logger.debug(f"Successfully updated MDL title and saved {base_name}")
                        return {'filename': base_name, 'status': 'MDL Title Updated'}
                    else:
                        self.logger.error(f"Failed to save {base_name} after MDL title update.")
                        return {'filename': base_name, 'status': 'Save Failed'}
                except Exception as e:
                    self.logger.error(f"Error during MDL title update for {base_name}: {e}", exc_info=True)
                    return {'filename': base_name, 'status': f'Error: {str(e)[:100]}'}

        tasks = [update_single_file_mdl_title(jp) for jp in json_files_to_process]

        for future in tqdm_async(asyncio.as_completed(tasks), total=len(tasks), desc="Updating MDL Titles",
                                 unit="file"):
            result = await future
            if result:
                summary_results.append(result)

        elapsed_time = time.time() - run_start_time
        self.logger.info(f"MDL title update ONLY mode finished in {self._format_elapsed_time(elapsed_time)}.")

        successful_count = sum(1 for r in summary_results if r['status'] == 'MDL Title Updated')
        failed_count = len(summary_results) - successful_count
        self.logger.info(f"--- MDL Title Update ONLY Mode Summary ---")
        self.logger.info(f"Total files attempted: {len(summary_results)}")
        self.logger.info(f"Successfully updated and saved: {successful_count}")
        self.logger.info(f"Failed (load/save/error/missing processor): {failed_count}")
        if failed_count > 0:
            self.logger.warning("Check logs for details on failed MDL title updates.")
        self.logger.info(f"--- End MDL Title Update ONLY Mode Summary ---")

        return summary_results

    async def process_federal_filings_async(self,
                                            json_files_to_process: List[str],
                                            force_reprocess_all: bool = False,
                                            specific_reprocess_set: Optional[Set[str]] = None) -> List[Dict]:
        """Processes each JSON file asynchronously using a semaphore."""
        # Normalize the paths in the specific reprocess set ONCE for efficient lookup
        _normalized_specific_reprocess_set = set(
            os.path.normpath(p) for p in specific_reprocess_set) if specific_reprocess_set else set()

        self.logger.info(
            f"Starting processing loop for {len(json_files_to_process)} files (force_reprocess_all={force_reprocess_all}, specific_set_size={len(_normalized_specific_reprocess_set)}, workers={self.num_workers})")
        if not json_files_to_process: return []

        semaphore = asyncio.Semaphore(self.num_workers)
        results = []

        async def process_with_semaphore(json_path):
            # Normalize the path of the current file for comparison
            normalized_current_path = os.path.normpath(json_path)
            async with semaphore:
                # Determine force_reprocess for THIS file using normalized paths
                force_reprocess_this_file = force_reprocess_all or (
                            normalized_current_path in _normalized_specific_reprocess_set)
                if normalized_current_path in _normalized_specific_reprocess_set and not force_reprocess_all:
                    self.logger.debug(
                        f"Forcing reprocess for specific file via normalized path check: {os.path.basename(normalized_current_path)}")

                # Pass the calculated flag and the potentially non-normalized json_path
                # _process_single_file_async will normalize it again internally if needed
                return await self._process_single_file_async(json_path, force_reprocess=force_reprocess_this_file)

        tasks = [process_with_semaphore(json_path) for json_path in json_files_to_process]
        for future in tqdm_async(asyncio.as_completed(tasks), total=len(tasks), desc="Processing dockets", unit="file"):
            try:
                result = await future
                if result is not None and isinstance(result, dict): results.append(result)
            except Exception as e:
                self.logger.error(f"Error processing task in outer loop: {e}", exc_info=True)

        success_count = len(results)
        fail_skip_count = len(json_files_to_process) - success_count
        self.logger.info(
            f"Docket processing loop complete: {success_count} successful outcomes reported, {fail_skip_count} skipped/failed.")
        return results

    # --- Refactored Single File Processing ---

    async def _process_single_file_async(self, current_json_path: str, force_reprocess: bool = False) -> Optional[Dict]:
        """
        Orchestrates the processing of a single docket JSON file asynchronously.
        Calls helper methods for each logical step.
        Integrates LitigationClassifier before LLM enrichment.
        """
        process_start_time = time.time()
        current_json_path = os.path.normpath(current_json_path)
        base_filename = os.path.splitext(os.path.basename(current_json_path))[0]
        current_dir = os.path.dirname(current_json_path)
        self.logger.info(
            f"[START PROC V4.16+ Safe Cleanup] Processing: {base_filename}.json (Force={force_reprocess})")  # Updated log

        # Define potential file paths
        current_pdf_path = os.path.join(current_dir, f"{base_filename}.pdf")
        # current_md_path = os.path.join(current_dir, f"{base_filename}.md") # MD path derived from actual PDF path later
        original_zip_path = os.path.join(current_dir, f"{base_filename}.zip")

        working_data: Optional[Dict] = None
        final_json_path: Optional[str] = None
        final_base_name_for_report: Optional[str] = None
        pdf_source_description = "Not found"
        pdf_text_content: Optional[str] = None
        pdf_path_used_for_processing: Optional[str] = None
        # Temp store for classifier result, MDLProcessor will use it if title is missing
        # litigation_name_from_classifier_for_fallback_title: Optional[str] = None

        try:
            # --- Step 0: Load Data & Initial Validation/Skip Logic ---
            load_result = await self._load_and_validate_initial_data(current_json_path, force_reprocess)
            if load_result is None: return None
            if isinstance(load_result, dict) and load_result.get('status') == 'skip':
                if await asyncio.to_thread(os.path.exists, original_zip_path):
                    await asyncio.to_thread(try_remove, original_zip_path, self.logger,
                                            f"redundant zip for skipped completed file {base_filename}")
                final_report_filename = base_filename
                try:
                    final_report_filename = self.file_handler.create_filename(load_result['data'])
                except Exception:
                    self.logger.warning(f"Could not generate final filename for skipped file {base_filename}.json")
                final_json_name_for_skip = f"{final_report_filename}.json"
                return {'filename': final_json_name_for_skip, 'status': "Processed (Skipped - Complete)"}
            working_data = load_result

            # --- Step 1: Ensure PDF is Available ---
            pdf_result = await self._ensure_pdf_available(working_data, base_filename, current_dir, current_pdf_path,
                                                          original_zip_path)
            if pdf_result is None:
                self.logger.error(f"[FAIL] CRITICAL: Step 1 Failed. No PDF source found for {base_filename}.json.")
                if working_data:
                    await self._update_error_status_and_save(working_data, current_json_path,
                                                             "Step 1 Error: PDF source not found/acquirable")
                return None
            pdf_path_used_for_processing, pdf_source_description = pdf_result

            # --- Step 2: Ensure MD is Available (via OCR if needed) ---
            md_path_to_use = pdf_path_used_for_processing.replace('.pdf', '.md')
            pdf_text_content = await self._ensure_md_available(pdf_path_used_for_processing, md_path_to_use,
                                                               base_filename)
            if pdf_text_content is None:
                self.logger.warning(
                    f"Step 2 Warning: Could not get/generate MD content for {base_filename}. Enrichment will proceed without it.")

            # --- Step 2.5: Run Litigation Classifier ---
            skip_case_info_llm = False
            # litigation_name_from_classifier_for_fallback_title = None # Initialize
            if pdf_text_content and self.litigation_classifier:
                try:
                    self.logger.debug(f"Running LitigationClassifier on text content for {base_filename}")
                    identified_lit_name, identified_mdl_num = self.litigation_classifier.identify_litigation_by_text(
                        pdf_text_content)

                    if identified_mdl_num and str(identified_mdl_num).strip().upper() not in ('NA', 'N/A', ''):
                        mdl_num_cleaned = str(identified_mdl_num).strip()
                        # litigation_name_from_classifier_for_fallback_title = str(identified_lit_name).strip() if identified_lit_name else "NA"

                        self.logger.info(
                            f"LitigationClassifier IDENTIFIED MDL: {mdl_num_cleaned} (Rule Name: '{identified_lit_name}').")
                        working_data['mdl_num'] = mdl_num_cleaned

                        # Don't set mdl_cat here. Let MDLProcessor handle it.
                        # Don't set title here. Let MDLProcessor handle it from lookup.
                        # Prime these fields to be potentially overwritten by MDLProcessor from lookup if LLM is skipped for them.
                        working_data['allegations'] = working_data.get('allegations')  # Preserve if already exists
                        working_data['summary'] = working_data.get('summary')  # Preserve if already exists
                        working_data['title'] = working_data.get('title')  # Preserve if already exists
                        skip_case_info_llm = True  # If classifier found an MDL, we might skip LLM for these generic fields.
                    else:
                        self.logger.info(
                            f"LitigationClassifier did not identify a specific MDL number from text for {base_filename}.")
                        # Ensure 'mdl_num' is 'NA' if not identified and not already present from input
                        working_data.setdefault('mdl_num', 'NA')
                except Exception as classifier_err:
                    self.logger.error(
                        f"Error during LitigationClassifier processing for {base_filename}: {classifier_err}",
                        exc_info=True)
                    working_data.setdefault('mdl_num', 'NA')
            elif not pdf_text_content:
                self.logger.warning(f"Skipping LitigationClassifier for {base_filename}: No MD text content available.")
                working_data.setdefault('mdl_num', 'NA')
            else:
                self.logger.warning(f"LitigationClassifier not initialized. Cannot classify {base_filename}.")
                working_data.setdefault('mdl_num', 'NA')

            # Ensure 'mdl_cat' is not set by classifier here; MDLProcessor will handle it.
            # If 'mdl_cat' was in the input JSON, it might be preserved or overwritten by MDLProcessor.
            # For a clean slate for MDLProcessor regarding mdl_cat, you could do:
            # working_data['mdl_cat'] = working_data.get('mdl_cat') # Preserve if from input, or MDLProcessor will set it.
            # Or, more assertively if MDLProcessor should always define it:
            # working_data['mdl_cat'] = None # Prime for MDLProcessor

            # --- Step 3: Enrich JSON Data ---
            enrich_success = await self._enrich_data(
                working_data,
                pdf_text_content,
                current_json_path,
                skip_case_info_llm  # Pass the flag
                # litigation_name_from_classifier_for_fallback_title # Pass the name for MDLProcessor
            )
            if not enrich_success:
                self.logger.error(f"[FAIL] Step 3 Failed. Enrichment errors for {base_filename}.json.")
                # Error status likely updated within _enrich_data or its callees
                return None

            # --- Step 4: Finalize Files (Rename/Archive) and Save ---
            finalization_result = await self._finalize_and_rename_files(working_data, base_filename, current_json_path,
                                                                        pdf_path_used_for_processing, md_path_to_use)
            if finalization_result is None:
                self.logger.error(f"[FAIL] Step 4 Failed. Final file operations error for {base_filename}.json.")
                return None
            final_json_path, final_base_name_for_report, rename_occurred = finalization_result

            # --- Step 5: Success Reporting ---
            process_end_time = time.time()
            duration = process_end_time - process_start_time
            self.logger.info(
                f"[SUCCESS] Finished processing '{base_filename}.json' -> '{final_base_name_for_report}.json' in {duration:.2f}s "
                f"(PDF Source: {pdf_source_description}, Renamed: {rename_occurred}, LLM CaseInfo Skipped by Classifier: {skip_case_info_llm})"
            )
            return {'filename': f"{final_base_name_for_report}.json",
                    'status': f"Processed{' (Renamed)' if rename_occurred else ''}"}

        except Exception as e:
            self.logger.critical(f"[FAIL] UNHANDLED error during processing of {base_filename}.json: {e}",
                                 exc_info=True)
            if working_data:  # working_data might be None if error was very early
                await self._update_error_status_and_save(working_data, current_json_path,
                                                         f"Unhandled Processing Error: {e}")
            return None
        finally:
            await self._perform_cleanup(working_data, original_zip_path)

    # --- Helper Methods for Single File Processing Steps ---

    async def _load_and_validate_initial_data(self, json_path: str, force_reprocess: bool) -> Optional[
        Union[Dict, Any]]:
        """
        Loads JSON, checks validity (now including 'added_on'),
        and determines if processing should proceed.
        """
        base_filename = os.path.splitext(os.path.basename(json_path))[0]
        self.logger.debug(f"Step 0: Load/Validate for {base_filename}.json (Force={force_reprocess})")
        try:
            data = await self.file_handler.load_json_async(json_path)
            if data is None:
                self.logger.error(f"[FAIL] Failed to load initial JSON: {base_filename}.json")
                return None
            working_data = copy.deepcopy(data)
            working_data['_source_json_path'] = json_path  # Track original path

            # Call the updated validator. It now checks for 'added_on'.
            is_complete, missing_fields = self.validator.is_data_complete(working_data)
            has_error = 'processing_error' in working_data

            if is_complete and not has_error and not force_reprocess:
                self.logger.info(
                    f"[SKIP] Skipping already complete file {base_filename}.json (Force={force_reprocess})")
                # Ensure associated zip is removed if skipping complete file
                zip_path = json_path.replace('.json', '.zip')
                if await asyncio.to_thread(os.path.exists, zip_path):
                    await asyncio.to_thread(try_remove, zip_path, self.logger,
                                            f"redundant zip for completed file {base_filename}")
                # Return skip status and the data (needed for potential filename generation later if used)
                return {'status': 'skip', 'data': working_data}

            # If not skipping, proceed with processing checks
            if has_error:
                self.logger.info(f"[REPROCESSING] File {base_filename}.json has prior errors. Clearing error status.")
                self._clear_processing_errors(working_data)
            elif force_reprocess and not has_error:
                self.logger.info(f"[FORCE REPROCESS] Forcing reprocessing for {base_filename}.json.")
                self._clear_processing_errors(working_data)  # Clear error just in case
            elif not is_complete:
                # Log why it's incomplete if not already errored or forced
                self.logger.info(
                    f"[PROCESSING] File {base_filename}.json is incomplete (Missing/Invalid: {missing_fields}). Proceeding.")

            return working_data  # Return data for processing steps

        except Exception as load_err:
            self.logger.error(f"[FAIL] Error during initial load/validation for {base_filename}.json: {load_err}",
                              exc_info=True)
            return None

    # Located inside the DataTransformer class in transformer.py

    async def _ensure_pdf_available(self, working_data: Dict, base_filename: str, current_dir: str,
                                    target_pdf_path: str, zip_path: str) -> Optional[Tuple[str, str]]:
        """
        Finds PDF: checks existing path, extracts from ZIP, or downloads from S3.
        If no PDF found, checks for transferor information and tries to find s3_link from there.
        Includes robust checks and logging before removing any potentially valid PDF.
        Args:
            working_data: The current data dictionary.
            base_filename: Base name of the original JSON file (no ext).
            current_dir: Directory where files are located.
            target_pdf_path: The *ideal* final path for the PDF based on original JSON name.
            zip_path: Path to the corresponding zip file.

        Returns:
            Tuple (path_to_use, description) or None on critical failure.
        """
        pdf_source_desc = "Not Found"
        pdf_path_to_use = None
        log_prefix = f"[_ensure_pdf_available/{base_filename}]"  # Consistent logging prefix

        self.logger.debug(f"{log_prefix} Starting PDF availability check.")
        self.logger.debug(f"{log_prefix} Ideal target PDF path: {target_pdf_path}")
        self.logger.debug(f"{log_prefix} Zip path: {zip_path}")

        try:
            # --- 1. Check if PDF already exists at the target path ---
            # (Existing logic for checking local PDF remains the same)
            self.logger.debug(f"{log_prefix} Checking for existing PDF at: {target_pdf_path}")
            target_pdf_exists = await asyncio.to_thread(os.path.exists, target_pdf_path)

            if target_pdf_exists:
                self.logger.debug(f"{log_prefix} Path exists: {target_pdf_path}. Checking if it's a valid file...")
                is_file = False
                size = -1  # Default to -1 to indicate check failure
                # --- Robust file/size check with retry ---
                for attempt in range(3):  # Try up to 3 times
                    try:
                        is_file = await asyncio.to_thread(os.path.isfile, target_pdf_path)
                        if is_file:
                            size = await asyncio.to_thread(os.path.getsize, target_pdf_path)
                            if size >= 0:
                                self.logger.debug(
                                    f"{log_prefix} Check attempt {attempt + 1} successful: is_file={is_file}, size={size}")
                                break
                            else:
                                self.logger.warning(
                                    f"{log_prefix} Check attempt {attempt + 1}: is_file=True, but getsize returned negative ({size}). Retrying check...")
                        else:
                            self.logger.warning(
                                f"{log_prefix} Check attempt {attempt + 1}: os.path.isfile returned False. Retrying check...")
                    except OSError as check_err:
                        size = -1
                        self.logger.warning(
                            f"{log_prefix} Check attempt {attempt + 1}: OSError during isfile/getsize check: {check_err}. Retrying check...")
                    if attempt < 2: await asyncio.sleep(0.1 * (attempt + 1))
                # --- End of retry check ---
                # --- Decision based on final check results ---
                if is_file and size > 0:
                    self.logger.info(
                        f"{log_prefix} Found existing VALID PDF (is_file={is_file}, size={size}) after checks. Using this file.")
                    pdf_path_to_use = target_pdf_path
                    pdf_source_desc = "Existing PDF"
                else:
                    self.logger.error(
                        f"{log_prefix} CRITICAL: Existing path {target_pdf_path} deemed INVALID after checks (is_file={is_file}, size={size}).")
                    # --- !! PDF REMOVAL DISABLED FOR SAFETY !! ---
                    # await asyncio.to_thread(try_remove, target_pdf_path, self.logger, f"invalid existing PDF '{os.path.basename(target_pdf_path)}'")
                    self.logger.warning(
                        f"{log_prefix} SKIPPING removal of potentially invalid PDF for safety: {target_pdf_path}. Processing will likely fail.")
                    pdf_path_to_use = None
            else:
                self.logger.debug(f"{log_prefix} No existing PDF found at target path.")

            # --- 2. Check for ZIP file and extract if PDF not found/valid yet ---
            # (Existing logic for ZIP extraction remains the same)
            zip_exists = await asyncio.to_thread(os.path.exists, zip_path) if pdf_path_to_use is None else False
            if pdf_path_to_use is None and zip_exists:
                self.logger.info(
                    f"{log_prefix} Attempting direct PDF extraction from zip '{os.path.basename(zip_path)}' to target '{os.path.basename(target_pdf_path)}'")
                extraction_success = await asyncio.to_thread(
                    extract_from_zip, zip_path, target_pdf_path, self.logger
                )
                if extraction_success:
                    if await asyncio.to_thread(os.path.exists, target_pdf_path) and await asyncio.to_thread(
                            os.path.getsize, target_pdf_path) > 0:
                        self.logger.info(f"{log_prefix} Successfully extracted valid PDF from zip.")
                        pdf_path_to_use = target_pdf_path
                        pdf_source_desc = "Extracted from ZIP"
                    else:
                        self.logger.warning(
                            f"{log_prefix} Extraction reported success, but target PDF invalid/missing. Removing target if exists.")
                        if await asyncio.to_thread(os.path.exists, target_pdf_path):
                            self.logger.debug(
                                f"{log_prefix} Preparing to remove file after failed extraction verification: {target_pdf_path}")
                            await asyncio.to_thread(try_remove, target_pdf_path, self.logger,
                                                    f"PDF after failed zip extract verify '{os.path.basename(target_pdf_path)}'")
                else:
                    self.logger.warning(
                        f"{log_prefix} Extraction from zip failed or found no valid PDF. Will try S3 if possible.")
            elif pdf_path_to_use is None and not zip_exists:
                self.logger.debug(f"{log_prefix} No ZIP file found at: {zip_path}")

            # --- 3. Download from S3 if still not found (using direct s3_link in working_data) ---
            # (Existing logic for direct S3 download remains the same)
            if pdf_path_to_use is None and 's3_link' in working_data:
                s3_url = working_data.get('s3_link')
                if s3_url and isinstance(s3_url, str) and s3_url.lower().endswith('.pdf'):
                    self.logger.info(f"{log_prefix} Attempting S3 download for direct s3_link: {s3_url}")
                    if await asyncio.to_thread(os.path.exists, target_pdf_path):
                        self.logger.warning(
                            f"{log_prefix} Target path exists before direct S3 download. Removing: {target_pdf_path}")
                        await asyncio.to_thread(try_remove, target_pdf_path, self.logger,
                                                f"pre-existing file before direct S3 dl '{os.path.basename(target_pdf_path)}'")

                    download_success = await self.file_handler.download_s3_file_async(s3_url, target_pdf_path)
                    if download_success and await asyncio.to_thread(os.path.exists, target_pdf_path):
                        size = await asyncio.to_thread(os.path.getsize, target_pdf_path)
                        if size > 0:
                            pdf_path_to_use = target_pdf_path
                            pdf_source_desc = "Downloaded from S3"
                            self.logger.info(
                                f"{log_prefix} Successfully downloaded valid PDF from direct S3 link (size={size}).")
                        else:
                            self.logger.error(
                                f"{log_prefix} Direct S3 download successful but file is empty (Size=0). Path: {target_pdf_path}")
                            await asyncio.to_thread(try_remove, target_pdf_path, self.logger,
                                                    f"empty direct S3 download '{os.path.basename(target_pdf_path)}'")
                    # ... (rest of direct S3 download error handling) ...
                    elif download_success:  # Download reported success, but file check failed
                        self.logger.error(
                            f"{log_prefix} Direct S3 download reported success but local file verification failed for {target_pdf_path}")
                        if await asyncio.to_thread(os.path.exists, target_pdf_path):
                            await asyncio.to_thread(try_remove, target_pdf_path, self.logger,
                                                    f"direct S3 dl verification failed file '{os.path.basename(target_pdf_path)}'")
                    else:  # Download failed
                        self.logger.error(
                            f"{log_prefix} Failed to download PDF from direct S3 link: {s3_url}. Check logs.")
                        if await asyncio.to_thread(os.path.exists, target_pdf_path):
                            await asyncio.to_thread(try_remove, target_pdf_path, self.logger,
                                                    f"failed direct S3 download artifact '{os.path.basename(target_pdf_path)}'")
                elif s3_url:
                    self.logger.warning(
                        f"{log_prefix} Key 's3_link' value is invalid or not a PDF link ('{s3_url}'). Skipping direct S3 download.")
                else:
                    self.logger.warning(
                        f"{log_prefix} Key 's3_link' exists but value is None or empty. Skipping direct S3 download.")
            elif pdf_path_to_use is None:
                self.logger.debug(f"{log_prefix} 's3_link' key not found in working_data. Skipping direct S3 download.")

            # --- 4. Check for transferor information if still no PDF found ---
            if pdf_path_to_use is None:
                transferor_court_id = working_data.get('transferor_court_id')
                transferor_docket_num = working_data.get('transferor_docket_num')

                valid_transferor_info = (
                        transferor_court_id and transferor_docket_num and
                        str(transferor_court_id).strip() not in (None, "None", "NA", "") and
                        str(transferor_docket_num).strip() not in (None, "None", "NA", "")
                )

                if valid_transferor_info:
                    self.logger.info(
                        f"{log_prefix} No PDF found yet. Trying transferor info lookup: "
                        f"Court={transferor_court_id}, Docket={transferor_docket_num}"
                    )
                    self.logger.debug(f"{log_prefix} Calling pacer_db.query_transfer_info...")
                    # This method returns a single dict or None based on pacer_manager code
                    transferor_result_dict = self.pacer_db.query_transfer_info(transferor_court_id,
                                                                               transferor_docket_num)
                    self.logger.debug(f"{log_prefix} pacer_db.query_transfer_info returned: {transferor_result_dict}")

                    # --- MODIFIED LOGIC: Handle single dictionary response ---
                    if transferor_result_dict and isinstance(transferor_result_dict, dict):
                        self.logger.info(f"{log_prefix} Found transferor result dictionary.")
                        # Directly check this dictionary for the link
                        s3_url = None
                        try:
                            potential_s3_link = transferor_result_dict.get('s3_link')
                            potential_S3Link = transferor_result_dict.get('S3Link')
                            self.logger.debug(
                                f"{log_prefix} Checking transferor dict links: s3_link='{potential_s3_link}', S3Link='{potential_S3Link}'")

                            # Prioritize S3Link if available and valid
                            if potential_S3Link and isinstance(potential_S3Link,
                                                               str) and potential_S3Link.lower().endswith('.pdf'):
                                s3_url = potential_S3Link
                                self.logger.info(f"{log_prefix} Using 'S3Link' from transferor dict: {s3_url}")
                            elif potential_s3_link and isinstance(potential_s3_link,
                                                                  str) and potential_s3_link.lower().endswith('.pdf'):
                                s3_url = potential_s3_link
                                self.logger.info(f"{log_prefix} Using 's3_link' from transferor dict: {s3_url}")
                            else:
                                self.logger.warning(
                                    f"{log_prefix} Transferor dict found, but 'S3Link'/'s3_link' is missing, invalid, or not a PDF.")
                                s3_url = None
                        except Exception as e:
                            self.logger.error(f"{log_prefix} Error extracting s3_link/S3Link from transferor dict: {e}",
                                              exc_info=True)
                            s3_url = None

                        # If a valid URL was extracted, attempt download
                        if s3_url:
                            self.logger.info(
                                f"{log_prefix} Found s3_link via transferor dict: {s3_url}. Will attempt download.")
                            working_data['s3_link'] = s3_url  # Update working data

                            if await asyncio.to_thread(os.path.exists, target_pdf_path):
                                self.logger.warning(
                                    f"{log_prefix} Target path exists before transferor S3 download. Removing: {target_pdf_path}")
                                await asyncio.to_thread(try_remove, target_pdf_path, self.logger,
                                                        f"pre-existing file before transferor S3 dl")

                            self.logger.debug(f"{log_prefix} Attempting download for transferor URL: {s3_url}")
                            download_success = await self.file_handler.download_s3_file_async(s3_url, target_pdf_path)
                            self.logger.debug(f"{log_prefix} Transferor download result: {download_success}")

                            if download_success and await asyncio.to_thread(os.path.exists, target_pdf_path):
                                size = await asyncio.to_thread(os.path.getsize, target_pdf_path)
                                if size > 0:
                                    pdf_path_to_use = target_pdf_path
                                    pdf_source_desc = "Transferor S3 Link"  # Specific description
                                    self.logger.info(
                                        f"{log_prefix} Successfully downloaded PDF from transferor S3 link (size={size}).")
                                else:
                                    self.logger.error(
                                        f"{log_prefix} Transferor S3 download successful but file is empty (Size=0). Path: {target_pdf_path}")
                                    await asyncio.to_thread(try_remove, target_pdf_path, self.logger,
                                                            f"empty transferor S3 download")
                            else:  # Download failed or file not found after download
                                self.logger.error(
                                    f"{log_prefix} Failed to download or verify PDF from transferor S3 link: {s3_url}")
                                if await asyncio.to_thread(os.path.exists, target_pdf_path):
                                    self.logger.debug(
                                        f"{log_prefix} Removing artifact from failed transferor S3 download: {target_pdf_path}")
                                    await asyncio.to_thread(try_remove, target_pdf_path, self.logger,
                                                            f"failed transferor S3 download")
                        # else: # s3_url was None after checking dict
                        # Already logged warning above

                    elif transferor_result_dict is None:
                        self.logger.error(
                            f"{log_prefix} Transferor info query returned None. Check PacerManager logs for query errors.")
                    else:  # transferor_result_dict was not a dict (or maybe empty dict, though query usually returns None if no match)
                        self.logger.warning(f"{log_prefix} Transferor query did not return a valid dictionary result.")
                else:
                    self.logger.debug(
                        f"{log_prefix} No valid transferor info available. Court='{transferor_court_id}', Docket='{transferor_docket_num}'")

            # --- Final check and return ---
            if pdf_path_to_use and await asyncio.to_thread(os.path.exists, pdf_path_to_use):
                size_final = await asyncio.to_thread(os.path.getsize, pdf_path_to_use)
                if size_final > 0:
                    self.logger.info(
                        f"{log_prefix} PDF source confirmed: {pdf_source_desc} at path {pdf_path_to_use} (size={size_final}).")
                    return pdf_path_to_use, pdf_source_desc
                else:
                    self.logger.error(
                        f"{log_prefix} [FAIL] CRITICAL: Final PDF path '{pdf_path_to_use}' exists but is empty (Size=0). Source was '{pdf_source_desc}'.")
                    # Try removing the empty file
                    await asyncio.to_thread(try_remove, pdf_path_to_use, self.logger, f"empty final PDF")
                    return None  # Treat empty file as failure
            else:
                if pdf_path_to_use:
                    self.logger.error(
                        f"{log_prefix} [FAIL] CRITICAL: PDF path '{pdf_path_to_use}' was determined but file not found or invalid at final check.")
                else:
                    self.logger.error(
                        f"{log_prefix} [FAIL] CRITICAL: Step 1 Failed. No valid PDF source could be determined after all attempts.")
                return None  # Return None if no valid path found

        except Exception as step1_err:
            self.logger.error(f"{log_prefix} Unhandled error in _ensure_pdf_available: {step1_err}", exc_info=True)
            return None  # Return None on any unhandled exception

    async def _ensure_md_available(self, pdf_path: str, md_path: str, base_filename: str) -> Optional[str]:
        """Checks for existing MD file or generates it using OCR."""
        force_ocr_overwrite = self.config.get('reprocess_md', False)
        pdf_text_content: Optional[str] = None

        # --- Helper function for synchronous file reading ---
        # Define it here or make it a static/utility method if used elsewhere
        def _sync_read_text_file(path: str) -> Optional[str]:
            """Synchronously reads a text file, returns content or None on error."""
            try:
                # Use utf-8 encoding, common for text/md files
                with open(path, 'r', encoding='utf-8') as f:
                    return f.read()
            except FileNotFoundError:
                # Log specific error in the calling context if needed
                return None
            except Exception as e_read_sync:
                # Log general error in the calling context if needed (cannot access self.logger here)
                print(f"DEBUG: Error reading file {path} synchronously: {e_read_sync}")  # Fallback print
                return None

        # --- End helper function ---

        try:
            md_exists = await asyncio.to_thread(os.path.exists, md_path)

            if md_exists and not force_ocr_overwrite:
                self.logger.debug(f"Found existing MD file: {os.path.basename(md_path)}")
                try:
                    # --- CORRECTED ASYNC READ ---
                    # Use asyncio.to_thread to run the sync read operation
                    self.logger.debug(f"Attempting async read via thread for: {os.path.basename(md_path)}")
                    pdf_text_content = await asyncio.to_thread(_sync_read_text_file, md_path)
                    # --- END CORRECTION ---

                    if pdf_text_content is not None and pdf_text_content.strip():  # Also check if content is not just whitespace
                        self.logger.info(f"Successfully read existing MD content for {base_filename}.")
                    elif pdf_text_content is None:  # Read operation failed
                        self.logger.warning(
                            f"Failed reading existing MD file {os.path.basename(md_path)} using threaded I/O. Will attempt OCR.")
                        force_ocr_overwrite = True  # Force OCR if read failed
                    else:  # File was empty or whitespace only
                        self.logger.warning(
                            f"Existing MD file is empty or whitespace only: {os.path.basename(md_path)}. Will attempt OCR.")
                        force_ocr_overwrite = True  # Force OCR if file is empty
                except Exception as read_err:
                    # This catch block might be less likely to hit now with _sync_read_text_file handling its own errors
                    # but keep for safety around the await call itself.
                    self.logger.warning(
                        f"Unexpected error during async read attempt for {os.path.basename(md_path)}: {read_err}. Will attempt OCR.",
                        exc_info=True)
                    force_ocr_overwrite = True  # Force OCR if async operation failed

            # Perform OCR if MD doesn't exist, is empty/unreadable, or overwrite is forced
            if pdf_text_content is None or force_ocr_overwrite:
                if force_ocr_overwrite and pdf_text_content is not None:  # Log only if forcing OVER an existing read
                    self.logger.info(
                        f"Forcing OCR regeneration for {base_filename} despite successful initial MD read.")
                elif pdf_text_content is None and not force_ocr_overwrite:
                    self.logger.info(f"MD file not found or read failed for {base_filename}. Running OCR.")

                if self.pdf_processor and await asyncio.to_thread(os.path.exists, pdf_path):
                    try:
                        # --- CORRECTED CALL (v2) ---
                        # Run the synchronous parse_pdf in a separate thread
                        self.logger.debug(f"Running MistralOCR.parse_pdf for {base_filename} via asyncio.to_thread")
                        ocr_result = await asyncio.to_thread(self.pdf_processor.parse_pdf, pdf_path)
                        self.logger.debug(f"asyncio.to_thread call for parse_pdf completed for {base_filename}")
                        # --- END CORRECTION ---

                        if ocr_result and isinstance(ocr_result, str) and ocr_result.strip():
                            pdf_text_content = ocr_result
                            self.logger.info(f"OCR successful. MD content generated for {base_filename}.")
                            # Check if the file *was* saved by MistralOCR or needs manual save
                            # (MistralOCR.parse_pdf doesn't save, so we always need to check/save here)
                            md_exists_after_ocr = await asyncio.to_thread(os.path.exists, md_path)
                            if not md_exists_after_ocr:
                                self.logger.warning(
                                    f"OCR processor method parse_pdf does not save file. Saving manually to: {md_path}")
                                # Use asyncio.to_thread for saving too
                                try:
                                    def _sync_save_text(path, content):
                                        # Added check for empty content before writing
                                        if content and content.strip():
                                            # Ensure directory exists before writing
                                            os.makedirs(os.path.dirname(path), exist_ok=True)
                                            with open(path, 'w', encoding='utf-8') as f:
                                                f.write(content)
                                        else:
                                            # Optionally create an empty file or log differently
                                            # Cannot access self.logger directly here
                                            print(
                                                f"DEBUG: Skipping save for {os.path.basename(path)} due to empty/whitespace content.")
                                            # To create empty file: open(path, 'w').close()

                                    await asyncio.to_thread(_sync_save_text, md_path, pdf_text_content)
                                except Exception as save_err:
                                    # Log error using the instance logger
                                    self.logger.error(
                                        f"Failed to manually save MD file {md_path} after OCR: {save_err}",
                                        exc_info=True)
                            else:
                                # If MD *does* exist, maybe another process created it, or overwrite logic needs review
                                self.logger.debug(
                                    f"MD file {md_path} already exists after OCR call completed. Content used from OCR result.")


                        else:
                            self.logger.error(f"OCR processing failed or returned empty content for {base_filename}.")
                            pdf_text_content = None  # Ensure it's None if OCR fails/is empty
                    except Exception as ocr_err:
                        # Log the specific attribute error if it happens again, or other errors
                        self.logger.error(f"Error during OCR processing call for {base_filename}: {ocr_err}",
                                          exc_info=True)
                        pdf_text_content = None  # Ensure it's None on exception
                elif not self.pdf_processor:
                    self.logger.warning("PDF Processor (MistralOCR) not available. Cannot generate MD file.")
                    pdf_text_content = None  # Ensure it's None if no processor
                else:  # PDF Processor exists, but PDF path doesn't
                    self.logger.error(f"Cannot run OCR: PDF file not found at {pdf_path}")
                    pdf_text_content = None  # Ensure it's None if PDF missing

            # Ensure pdf_text_content is either a non-empty string or None
            if isinstance(pdf_text_content, str) and not pdf_text_content.strip():
                self.logger.debug(f"MD content for {base_filename} is whitespace only, treating as None.")
                pdf_text_content = None

            return pdf_text_content

        except Exception as step2_err:
            self.logger.error(f"Unhandled error in _ensure_md_available for {base_filename}.json: {step2_err}",
                              exc_info=True)
            return None  # Return None on any unhandled error

    # --- Located inside PostProcessorV2 class in transformer.py ---

    async def _finalize_and_rename_files(self, final_data: Dict, base_filename: str, current_json_path: str,
                                         current_pdf_path: str, current_md_path: str) -> Optional[
        Tuple[str, str, bool]]:
        """
        Generates final filename, constructs final s3_link, renames/archives files,
        sets added_on date, and saves final JSON.
        Returns (final_json_path, final_base_name, rename_occurred) or None on failure.
        """
        self.logger.info(f"Step 4: Starting final rename/archive/save/link for {base_filename}.json")  # Added /link
        current_dir = os.path.dirname(current_json_path)
        final_new_base_filename: Optional[str] = None
        final_json_path: str = current_json_path
        rename_occurred = False

        # --- Synchronous Helper for Renaming (remains the same) ---
        def _sync_perform_rename_ops(
                log: logging.Logger,
                curr_json: str,
                curr_pdf: str,
                curr_md: str,
                new_base: str,
                curr_dir: str
        ) -> bool:
            """Performs synchronous renames and backup. Returns True on success."""
            success = True
            new_pdf = os.path.join(curr_dir, f"{new_base}.pdf")
            new_md = os.path.join(curr_dir, f"{new_base}.md")
            backup_json = curr_json + ".bak"  # Consistent with log output

            # 1. Rename PDF if it exists
            try:
                if os.path.exists(curr_pdf) and os.path.isfile(curr_pdf):
                    log.debug(f"Renaming PDF: {os.path.basename(curr_pdf)} -> {os.path.basename(new_pdf)}")
                    os.rename(curr_pdf, new_pdf)
                elif os.path.exists(curr_pdf):
                    log.warning(f"Source PDF path exists but is not a file: {curr_pdf}")
            except OSError as e:
                log.error(f"Failed to rename PDF {os.path.basename(curr_pdf)} to {os.path.basename(new_pdf)}: {e}")
                success = False

            # 2. Rename MD if it exists
            try:
                if os.path.exists(curr_md) and os.path.isfile(curr_md):
                    log.debug(f"Renaming MD: {os.path.basename(curr_md)} -> {os.path.basename(new_md)}")
                    os.rename(curr_md, new_md)
                elif os.path.exists(curr_md):
                    log.warning(f"Source MD path exists but is not a file: {curr_md}")
            except OSError as e:
                log.error(f"Failed to rename MD {os.path.basename(curr_md)} to {os.path.basename(new_md)}: {e}")
                success = False

            # 3. Backup original JSON (Rename current JSON to .bak)
            try:
                if os.path.exists(curr_json) and os.path.isfile(curr_json):
                    if os.path.exists(backup_json):
                        log.debug(f"Removing existing backup: {os.path.basename(backup_json)}")
                        os.remove(backup_json)
                    log.debug(
                        f"Creating backup by renaming: {os.path.basename(curr_json)} -> {os.path.basename(backup_json)}")
                    os.rename(curr_json, backup_json)
                elif os.path.exists(curr_json):
                    log.warning(f"Cannot backup original JSON: Path exists but is not a file: {curr_json}")
                    success = False
                else:
                    log.warning(f"Cannot backup original JSON: File not found: {curr_json}")
                    success = False

            except OSError as e:
                log.error(f"Failed to create backup for {os.path.basename(curr_json)}: {e}")
                success = False

            return success

        # --- End Synchronous Helper ---

        try:
            # --- Generate Final Filename ---
            try:
                final_new_base_filename = self.file_handler.create_filename(final_data)
                self.logger.info(f"Generated final filename base: '{final_new_base_filename}'")
            except (ValueError, KeyError, TypeError) as fn_err:
                self.logger.error(f"CRITICAL: Failed to generate final filename for {base_filename}.json: {fn_err}",
                                  exc_info=False)
                await self._update_error_status_and_save(final_data, current_json_path,
                                                         f"Filename Generation Error: {fn_err}")
                return None

            # --- Determine if Rename is Needed ---
            rename_needed = final_new_base_filename != base_filename
            self.logger.debug(
                f"Rename check: Current='{base_filename}', New='{final_new_base_filename}', Needed={rename_needed}")

            # +++ START: Generate Final S3 Link +++
            final_s3_link = None
            try:
                # Use the FileHandler method to get the base S3 key path
                s3_key_base = self.file_handler.get_s3_key_base(final_data)  # Uses create_filename internally
                if s3_key_base:
                    # Construct the full CDN URL for the PDF
                    final_s3_link = f"https://cdn.lexgenius.ai/{s3_key_base}.pdf"
                    self.logger.info(f"Constructed final s3_link: {final_s3_link}")
                    # Update the data dictionary with the generated link
                    final_data['s3_link'] = final_s3_link
                else:
                    self.logger.error(
                        f"Failed to generate S3 key base for {final_new_base_filename}, s3_link cannot be set.")
                    final_data['s3_link'] = None  # Ensure it's None if generation fails
            except Exception as s3_link_err:
                self.logger.error(f"Error generating s3_link for {final_new_base_filename}: {s3_link_err}",
                                  exc_info=True)
                final_data['s3_link'] = None  # Ensure it's None on error
            # +++ END: Generate Final S3 Link +++

            # --- Prepare final data for saving ---
            # Add 'new_filename' key for tracking/upload purposes
            final_data['new_filename'] = final_new_base_filename

            # Set added_on date (logic remains the same)
            iso_date = self.config.get('iso_date')
            self.logger.debug(
                f"Attempting to set added_on for {base_filename}. Retrieved iso_date from config: '{iso_date}' (Type: {type(iso_date).__name__})")
            if not iso_date or not isinstance(iso_date, str) or len(iso_date) != 8 or not iso_date.isdigit():
                self.logger.error(
                    f"CRITICAL: Invalid or missing iso_date ('{iso_date}') in config for {base_filename}.json. Cannot set valid added_on date. Using fallback.")
                iso_date = "00000000"  # Use a clear invalid date as fallback
            final_data['added_on'] = iso_date
            self.logger.debug(f"Set final_data['added_on'] = {final_data.get('added_on')} for {base_filename}.json")

            # Clear any previous processing errors *before* the final save if we reached this point successfully
            if 'processing_error' in final_data:
                self.logger.info(f"Clearing previous processing error for {base_filename}.json before final save.")
                self._clear_processing_errors(final_data)  # Uses processor method

            # --- Perform Rename and Save OR Just Re-Save ---
            if rename_needed:
                rename_occurred = True
                new_json_path = os.path.join(current_dir, f"{final_new_base_filename}.json")
                self.logger.info(f"Renaming files from '{base_filename}' to '{final_new_base_filename}'")

                rename_success = await asyncio.to_thread(
                    _sync_perform_rename_ops,
                    self.logger, current_json_path, current_pdf_path,
                    current_md_path, final_new_base_filename, current_dir
                )

                if not rename_success:
                    self.logger.error(
                        f"Failed during rename/backup operations for {base_filename} -> {final_new_base_filename}.")
                    # Attempt to save original json with error BEFORE failing the step
                    await self._update_error_status_and_save(final_data, current_json_path,
                                                             f"Rename/Backup Error for target {final_new_base_filename}")
                    return None

                final_json_path = new_json_path
                self.logger.info(f"Rename/Backup ops successful. Final base: {final_new_base_filename}")

                # Final Save to the *new* JSON path (now includes s3_link and added_on)
                self.logger.debug(
                    f"Saving final data (s3_link='{final_data.get('s3_link')}', added_on='{final_data.get('added_on')}') to NEW path: {os.path.basename(final_json_path)}")

                # Check if attorney data exists and log it
                if 'attorney' in final_data and isinstance(final_data['attorney'], list):
                    self.logger.info(
                        f"Final data contains {len(final_data['attorney'])} attorneys before saving to new path")
                else:
                    self.logger.debug(f"No attorney array found in final data before saving to new path")

                # Check if attorneys_gpt data exists and log it
                if 'attorneys_gpt' in final_data and isinstance(final_data['attorneys_gpt'], list):
                    self.logger.info(
                        f"Final data contains {len(final_data['attorneys_gpt'])} attorneys_gpt before saving to new path")
                else:
                    self.logger.debug(f"No attorneys_gpt array found in final data before saving to new path")

                save_final_ok = await self.file_handler.save_json_async(final_json_path, final_data)
                if not save_final_ok:
                    self.logger.critical(
                        f"CRITICAL: Failed to save final renamed JSON '{os.path.basename(final_json_path)}'. Original is likely .bak.")
                    return None  # Critical failure

            else:  # No rename needed
                rename_occurred = False
                final_json_path = current_json_path
                self.logger.info(f"Step 4: Filename '{base_filename}' is correct. No rename needed.")

                # Re-save to original path with updated data (including s3_link, added_on, potentially cleared error)
                self.logger.debug(
                    f"Re-saving final data (s3_link='{final_data.get('s3_link')}', added_on='{final_data.get('added_on')}') to original path: {os.path.basename(current_json_path)}")

                # Check if attorney data exists and log it
                if 'attorney' in final_data and isinstance(final_data['attorney'], list):
                    self.logger.info(f"Final data contains {len(final_data['attorney'])} attorneys before saving")
                else:
                    self.logger.debug(f"No attorney array found in final data before saving")

                # Check if attorneys_gpt data exists and log it
                if 'attorneys_gpt' in final_data and isinstance(final_data['attorneys_gpt'], list):
                    self.logger.info(
                        f"Final data contains {len(final_data['attorneys_gpt'])} attorneys_gpt before saving")
                else:
                    self.logger.debug(f"No attorneys_gpt array found in final data before saving")

                save_no_rename_ok = await self.file_handler.save_json_async(current_json_path, final_data)
                if not save_no_rename_ok:
                    self.logger.error(
                        f"Failed to re-save JSON {os.path.basename(current_json_path)} after no-rename check.")
                    # Attempt to save with error status
                    await self._update_error_status_and_save(final_data, current_json_path, "Failed final re-save")
                    return None

            # Return success state
            self.logger.info(
                f"Step 4: Final save successful for target base '{final_new_base_filename}'. Final JSON path: {final_json_path}")
            return final_json_path, final_new_base_filename, rename_occurred

        except Exception as step4_err:
            self.logger.error(
                f"[FAIL] Unhandled error during Step 4 (_finalize_and_rename_files) for {base_filename}.json: {step4_err}",
                exc_info=True)
            # Attempt to save with error status to the *original* path if possible
            target_save_path = current_json_path  # Default to original
            try:
                if not os.path.exists(current_json_path) and os.path.exists(current_json_path + ".bak"):
                    target_save_path = current_json_path + ".bak"  # Try saving to backup if original gone
            except Exception:
                pass  # Ignore fs errors here

            if final_data and os.path.exists(os.path.dirname(target_save_path)):
                await self._update_error_status_and_save(final_data, target_save_path,
                                                         f"Step 4 Unhandled Error: {step4_err}")
            else:
                self.logger.error(
                    f"Could not save error status for Step 4 failure - data or target path invalid: {target_save_path}")
            return None

    async def _enrich_data(self, working_data: Dict, pdf_text_content: Optional[str],
                           current_json_path: str, skip_case_info_llm: bool) -> bool:
        """
        Runs all data enrichment steps (LLM, HTML, Transfers, MDL, etc.).
        Accepts a flag to conditionally skip the LLM case info extraction step.
        Returns True on success, False on critical failure.
        """
        base_filename = os.path.splitext(os.path.basename(current_json_path))[0]
        self.logger.info(
            f"Step 3: Starting enrichment for {base_filename}.json (Skip LLM Case Info Fields={skip_case_info_llm})")
        try:
            # --- LLM Extraction ---
            if pdf_text_content and self.llm_client and self.docket_processor:
                self.logger.info(f"Running LLM extraction via DocketProcessor for {base_filename}...")
                try:
                    await self.docket_processor.run_llm_extraction(
                        working_data,
                        pdf_text_content
                    )
                    self.logger.info(f"LLM extraction completed for {base_filename}.")

                    # If classifier determined LLM should be skipped for case info fields,
                    # nullify them now so MDLProcessor can fill them from lookup.
                    if skip_case_info_llm:
                        self.logger.debug(
                            f"Classifier identified MDL for {base_filename}. Nullifying LLM-extracted title/summary/allegations to prefer MDL lookup.")
                        working_data['title'] = None
                        working_data['summary'] = None
                        working_data['allegations'] = None
                        # mdl_cat and mdl_num will be handled/confirmed by MDLProcessor.
                        # If 'mdl_num' was set by classifier, it's already in working_data.
                        # If 'mdl_cat' was in input, MDLProcessor will decide to keep or override.

                except Exception as llm_err:
                    self.logger.error(f"LLM extraction error for {base_filename}: {llm_err}", exc_info=True)
                    if hasattr(self.docket_processor, '_update_error_status'):
                        self.docket_processor._update_error_status(working_data, current_json_path,
                                                                   f"LLM extraction err: {llm_err}")
                    else:
                        working_data['processing_error'] = f"LLM extraction err: {llm_err}"
                        working_data['last_error_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            else:
                self.logger.debug(
                    f"Skipping LLM extraction for {base_filename} (Criteria not met: Text={bool(pdf_text_content)}, Client={bool(self.llm_client)}, Processor={bool(self.docket_processor)}).")

            # --- Other Enrichment Steps ---
            self.logger.debug(f"Running other enrichment steps for {base_filename}.")
            try:
                if self.docket_processor:
                    self.docket_processor.process_s3_html(working_data, current_json_path)
                    self.docket_processor.process_filing_date(working_data)
                    # clean_and_flatten might run before or after MDLProcessor, depending on desired effect.
                    # If MDLProcessor adds case_info, run clean_and_flatten after.
                    # For now, keeping it here.
                    # self.docket_processor.clean_and_flatten(working_data) # Moved after MDL processing
                if self.transfer_handler:
                    self.transfer_handler.process_transfers(working_data)

                # MDL Processor: This is where title, mdl_cat, allegations, summary from lookup are primarily set.
                if self.mdl_processor:
                    # Pass the litigation_name_from_classifier if needed by MDLProcessor as a fallback.
                    # Conceptual: self.mdl_processor.add_mdl_info(working_data, classified_lit_name=litigation_name_from_classifier_for_fallback_title)
                    self.mdl_processor.add_mdl_info(working_data)  # Ensure this method sets title, and correct mdl_cat.
                    self.mdl_processor.calculate_afff_num_plaintiffs(working_data)  # Specific to AFFF

                if self.law_firm_processor:  # Usually after attorney extraction (LLM/HTML)
                    self.law_firm_processor.process_law_firms(working_data)

                if self.docket_processor:  # Run clean_and_flatten after all major data additions
                    self.docket_processor.clean_and_flatten(working_data)

                self._clean_duplicate_fields(working_data)

            except Exception as enrich_err:
                self.logger.error(f"Critical error during non-LLM enrichment for {base_filename}: {enrich_err}",
                                  exc_info=True)
                await self._update_error_status_and_save(working_data, current_json_path,
                                                         f"Enrichment Error: {enrich_err}")
                return False

            # --- Intermediate Save (after enrichment) ---
            internal_keys = ['_source_json_path', '_original_zip_filename',
                             '_original_extracted_pdf_name', '_temp_pdf_path', '_temp_md_path',
                             '_s3_temp_download_pdf_path', 'filename']
            for key in internal_keys: working_data.pop(key, None)

            self.logger.debug(f"Saving enriched data back to current JSON: {os.path.basename(current_json_path)}")
            save_ok = await self.file_handler.save_json_async(current_json_path, working_data)
            if not save_ok:
                self.logger.critical(f"CRITICAL: Failed intermediate save of enriched data for {base_filename}.json.")
                if self.docket_processor and hasattr(self.docket_processor, '_update_error_status'):  # Check attribute
                    self.docket_processor._update_error_status(working_data, current_json_path,
                                                               "Failed intermediate save Step 3")
                return False
            else:
                self.logger.info(f"Step 3: Enrichment and intermediate save successful for {base_filename}.")
                return True

        except Exception as step3_err:
            self.logger.error(
                f"[FAIL] Unhandled error during Step 3 (_enrich_data) for {base_filename}.json: {step3_err}",
                exc_info=True)
            await self._update_error_status_and_save(working_data, current_json_path,
                                                     f"Step 3 Unhandled Error: {step3_err}")
            return False

    async def _perform_cleanup(self, data_for_cleanup: Optional[Dict], original_zip_path: str):
        """
        Safely removes only the original source ZIP file, if it still exists.
        Does NOT remove PDF or MD files based on paths stored in data_for_cleanup.
        """
        base_filename = "Unknown"
        source_json = "Unknown"
        if data_for_cleanup:
            source_json = data_for_cleanup.get('_source_json_path', 'Unknown')
            if source_json != 'Unknown':
                base_filename = os.path.splitext(os.path.basename(source_json))[0]

        self.logger.debug(f"Entering SAFE cleanup for {base_filename}.json (Source: {source_json})")

        # Only target the specific original zip file path provided
        if original_zip_path and isinstance(original_zip_path, str):
            if await asyncio.to_thread(os.path.exists, original_zip_path):
                self.logger.info(f"Cleanup: Removing lingering source zip file: {os.path.basename(original_zip_path)}")
                await asyncio.to_thread(try_remove, original_zip_path, self.logger,
                                        f"lingering source zip file for {base_filename}")
            else:
                self.logger.debug(f"Cleanup: Source zip file already removed or never existed: {original_zip_path}")
        else:
            self.logger.debug(f"SAFE cleanup finished for {base_filename}.json")

    # --- Utility Methods (Error Handling Wrappers) ---

    async def _update_error_status_and_save(self, data: Optional[Dict], json_path: str, error_message: str):
        """Updates the error status in the data and attempts to save the JSON file."""
        if not data:
            self.logger.error(f"Cannot save error status for {os.path.basename(json_path)} - data object is None.")
            return
        self.docket_processor._update_error_status(data, json_path, error_message)
        save_success = await self.file_handler.save_json_async(json_path, data)
        if not save_success:
            self.logger.error(f"Failed to save JSON with error status for: {os.path.basename(json_path)}")

    def _update_error_status(self, data: Optional[Dict], json_path: str, error_message: str):
        """Updates the processing_error field in the data dictionary."""
        # Kept for potential direct use, but _update_error_status_and_save is preferred
        self.docket_processor._update_error_status(data, json_path, error_message)

    def _clear_processing_errors(self, data: Dict):
        """Removes the processing_error field from the data dictionary."""
        self.docket_processor._clear_processing_errors(data)

    # --- Sync Wrapper and Logging ---
    def start_sync(self, **kwargs):
        # --- This method remains unchanged ---
        self.logger.warning("Running start_sync() - this uses asyncio.run() to execute the async workflow.")
        sync_start_time = time.time()
        try:
            kwargs['upload'] = False  # Ensure upload=False is passed
            result = asyncio.run(self.start(**kwargs))
            elapsed = time.time() - sync_start_time
            self.logger.info(f"Sync workflow finished in {self._format_elapsed_time(elapsed)}")
            return result
        except Exception as e:
            elapsed = time.time() - sync_start_time
            self.logger.error(f"Sync workflow failed after {self._format_elapsed_time(elapsed)}", exc_info=True)
            raise

    def log_docket_summary(self):
        # --- This method remains unchanged ---
        self.logger.info("--- Docket Processing Summary (from this run) ---")
        if self.docket_summary is None: self.logger.info("Summary not generated."); return []
        if not self.docket_summary: self.logger.info("No successful dockets summarized."); return []
        processed_count = len(self.docket_summary)
        attempted_count = self.initial_file_count  # Use the count from the start of the run
        failed_skipped_count = attempted_count - processed_count
        self.logger.info(f"Attempted to process: {attempted_count} files")
        self.logger.info(f"Successfully processed/finalized: {processed_count} files")
        self.logger.info(
            f"Skipped or Failed: {failed_skipped_count} files")  # Includes initial skips and processing failures
        # Log detailed status counts
        status_counts = {}
        for item in self.docket_summary:
            status = item.get('status', 'Unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        if status_counts: self.logger.info(f"Success Status Breakdown: {status_counts}")
        self.logger.info("--- End Summary ---")
        return self.docket_summary


# --- Main execution block ---
if __name__ == "__main__":
    # --- Logging Setup ---
    log_format = '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s'  # Added more detail
    logging.basicConfig(level=logging.INFO, format=log_format, stream=sys.stdout)  # Default to stdout
    main_logger = logging.getLogger("PostProcessorMain")

    # Rich console setup (if available)
    if RICH_AVAILABLE:
        try:
            from rich.logging import RichHandler

            main_logger.info("Rich console available, configuring RichHandler.")
            root_logger = logging.getLogger()
            # Remove existing console handlers
            for handler in root_logger.handlers[:]:
                is_console_handler = isinstance(handler, logging.StreamHandler) and \
                                     not isinstance(handler, logging.FileHandler)
                if is_console_handler:
                    root_logger.removeHandler(handler)
            # Add RichHandler
            rich_handler = RichHandler(rich_tracebacks=True, show_path=False, console=console,
                                       log_time_format="[%X]")  # Use shared console
            rich_handler.setFormatter(logging.Formatter('%(message)s', datefmt="[%X]"))
            root_logger.addHandler(rich_handler)
            # root_logger.propagate = False # Avoid double logging if root handlers exist
        except Exception as rich_err:
            main_logger.error(f"Failed to configure Rich logging: {rich_err}. Falling back to basic.", exc_info=True)
            # Ensure basicConfig is reapplied if Rich setup fails midway
            logging.basicConfig(level=logging.INFO, format=log_format, stream=sys.stdout, force=True)
    else:
        main_logger.warning("Rich console not available. Using standard logging.")

    try:
        # --- Config Loading ---
        run_date_str = datetime.now().strftime('%m/%d/%y')
        main_logger.info(f"Loading configuration using date: {run_date_str}")
        # Ensure config loading works when run as script (adjust path if needed)
        # Assuming config.py is in the same directory or PYTHONPATH is set
        try:
            config = load_config(run_date_str)
        except NameError:
            main_logger.error("load_config not found. Ensure config.py is importable.")
            sys.exit(1)
        except Exception as cfg_err:
            main_logger.error(f"Error loading configuration: {cfg_err}", exc_info=True)
            sys.exit(1)

        if not config or not config.get('iso_date'):
            raise ValueError("Config loading failed or 'iso_date' missing.")
        if not config.get('bucket_name'):
            config['bucket_name'] = config.get('aws_s3', {}).get('bucket_name')
            if not config['bucket_name']: main_logger.warning("AWS S3 bucket name is missing in the configuration.")

        # --- Adjust Log Level ---
        log_level_str = config.get('logging', {}).get('level', 'INFO').upper()
        log_level = getattr(logging, log_level_str, logging.INFO)
        logging.getLogger().setLevel(log_level)  # Set level on root logger
        main_logger.info(f"Log level set to: {log_level_str}")

        # --- Initialize and Run Processor ---
        processor = DataTransformer(config, main_logger)


        async def run_async_main():
            main_logger.info("Starting async workflow via run_async_main...")
            # --- Example parameters ---
            # Set True to force reprocessing of files even if marked complete (but not errored)
            force_reprocess_all_flag = False
            # Set True to specifically reprocess files previously marked with errors
            process_error_files = True
            # Provide a list of specific JSON full paths to reprocess
            specific_files_to_reprocess = []  # e.g., ['/path/to/your/download/dir/file1.json']

            # Determine the reprocess argument based on flags
            reprocess_arg: Union[bool, List[str]] = False  # Default: process normally
            if force_reprocess_all_flag:
                reprocess_arg = True
                main_logger.info("Setting reprocess_files=True (Force Reprocess All)")
            elif specific_files_to_reprocess:
                reprocess_arg = specific_files_to_reprocess
                main_logger.info(
                    f"Setting reprocess_files to list of {len(specific_files_to_reprocess)} specific files.")
            # process_error_files is handled by start_from_incomplete flag

            skip_list = None  # e.g., ['/path/to/download/dir/skip_this_one.json']
            worker_count = config.get('num_workers', 8)

            results = await processor.start(
                upload=False,  # Ignored by V2, kept for interface consistency if needed elsewhere
                post_process=True,  # Controls whether processing *steps* run (always True here)
                reprocess_files=reprocess_arg,  # Pass the determined reprocessing scope
                skip_files=skip_list,
                start_from_incomplete=process_error_files,  # Use flag here
                num_workers=worker_count
            )
            main_logger.info(
                f"Async workflow finished. Processed docket summary count: {len(results) if results else 0}")


        asyncio.run(run_async_main())

    except Exception as main_err:
        main_logger.critical(f"Main execution failed: {main_err}", exc_info=True)
        # Ensure Rich traceback is printed if available
        if RICH_AVAILABLE and console:
            console.print_exception(show_locals=True)
        sys.exit(1)
    finally:
        main_logger.info("Main script finished.")

# TODO: Create attorney -> law_firm lookup table.
# TODO: UPDATE capitalize and clean law firm names to use capitalization lookup table:
# TODO: JOHNSON, KENDALL & JOHNSON -> <EMAIL>
