#!/usr/bin/env python3
import contextlib
# --- ADDED IMPORT ---
import decimal
import json
import logging
import os
import random
import re
import time
from datetime import datetime, timedelta
# --- END ADDED IMPORT ---
from decimal import Decimal, InvalidOperation  # Keep this existing import as well
from typing import Any, Dict, List, Tuple, Union, Optional, Generator

import boto3
import pandas as pd
from boto3.dynamodb.conditions import Key
from botocore.config import Config
from botocore.exceptions import ClientError
from rich.logging import RichHandler

# --- ADDED TimeRemainingColumn ---

try:
    # Keep existing rich imports
    from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeRemainingColumn, TimeElapsedColumn
except ImportError:
    # Define dummy classes if rich is not installed, so the code doesn't crash
    class Progress:
        def __init__(self, *args, **kwargs): pass

        def add_task(self, *args, **kwargs): return None

        def update(self, *args, **kwargs): pass

        def __enter__(self): return self

        def __exit__(self, *args): pass


    SpinnerColumn = BarColumn = TextColumn = TimeRemainingColumn = TimeElapsedColumn = lambda *args, **kwargs: None
    print("Warning: 'rich' library not found. Progress bars will be disabled.")
# Assuming local_dynamo_mixin.py and config.py exist in the same directory level
try:
    # Adjust these imports based on your actual project structure
    from .config import PROJECT_ROOT
    from .docker_dynamodb_manager import DockerDynamoDBManager
except ImportError:
    # Handle potential path issues if run directly or structured differently
    print("Warning: Could not import local modules relatively. Assuming structure for direct run.")
    try:
        from config import PROJECT_ROOT
        from docker_dynamodb_manager import DockerDynamoDBManager
    except ImportError as e:
        print(f"Error importing config/docker_dynamodb_manager: {e}. Ensure PYTHONPATH or structure.")
        PROJECT_ROOT = '.'  # Fallback


        # Define a dummy class if the import fails, to allow the script to proceed partially
        class DockerDynamoDBManager:
            def __init__(self, port): pass

            def start_dynamodb(self): pass

            def get_status(self): return {'running': False, 'endpoint_url': None}

            def stop_dynamodb(self): pass


def json_decimal_serializer(obj):
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, Decimal):
        # Check if it's an integer
        if obj % 1 == 0:
            return int(obj)
        else:
            return float(obj)
    # Add handling for datetime if needed (though often better to store as ISO strings)
    # elif isinstance(obj, (datetime.date, datetime.datetime)): # Use fully qualified names
    #      return obj.isoformat()
    try:
        # Attempt a generic string conversion as a fallback for simple types
        return str(obj)
    except Exception:
        raise TypeError(
            f"Object of type {obj.__class__.__name__} is not JSON serializable and cannot be easily converted to string")


# --- ADDED HELPER FUNCTION ---
def convert_decimals(obj):
    """Recursively convert Decimal instances in dicts/lists to int/float."""
    if isinstance(obj, list):
        return [convert_decimals(i) for i in obj]
    elif isinstance(obj, dict):
        return {k: convert_decimals(v) for k, v in obj.items()}
    elif isinstance(obj, decimal.Decimal):  # Check against imported decimal module
        if obj % 1 == 0:
            return int(obj)
        else:
            return float(obj)
    else:
        return obj


# Configure root logger to only show messages without file/line info
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[
        RichHandler(
            rich_tracebacks=False,
            show_path=False,
            show_time=False,
            show_level=False
        )
    ]
)

# Set higher log level for noisy libraries
logging.getLogger('boto3').setLevel(logging.WARNING)
logging.getLogger('botocore').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)


class DynamoDbBaseManager:
    """Base manager for DynamoDB operations with support for both AWS and local environments."""

    key_config = {
        'LawFirms': {'keys': ['ID', 'Name'], 'key_attributes': ['ID', 'Name']},
        'Pacer': {
            'keys': ['FilingDate', 'DocketNum'],
            'key_attributes': ['FilingDate', 'DocketNum'],
            'gsis': [
                {'name': 'AddedOn-index', 'hash_key': 'AddedOn', 'range_key': None},
                {'name': 'CourtId-DocketNum-index', 'hash_key': 'CourtId', 'range_key': 'DocketNum'},
                {'name': 'MdlNum-FilingDate-index', 'hash_key': 'MdlNum', 'range_key': 'FilingDate'},
                {'name': 'TransfereeCourtId-TransfereeDocketNum-index', 'hash_key': 'TransfereeCourtId',
                 'range_key': 'TransfereeDocketNum'},
                {'name': 'TransferorCourtId-TransferorDocketNum-index', 'hash_key': 'TransferorCourtId',
                 'range_key': 'TransferorDocketNum'},
                {'name': 'LawFirm-FilingDate-index', 'hash_key': 'LawFirm', 'range_key': 'FilingDate'},
                # Assuming LawFirm indexed
                {'name': 'CourtId-FilingDate-index', 'hash_key': 'CourtId', 'range_key': 'FilingDate'},
                # Added for get_dockets_for_bubble
            ]
        },
        'PacerDockets': {
            'keys': ['CourtId', 'DocketNum'],  # Primary Key: CourtId (HASH), DocketNum (RANGE)
            'key_attributes': ['CourtId', 'DocketNum'],  # Assumed S, S
            'gsis': [
                {'name': 'AddedOn-index', 'hash_key': 'AddedOn', 'range_key': None},  # S
                {'name': 'CourtId-FilingDate-index', 'hash_key': 'CourtId', 'range_key': 'FilingDate'},  # S, S
                {'name': 'MdlNum-CourtId-index', 'hash_key': 'MdlNum', 'range_key': 'CourtId'},  # S, S
                {'name': 'MdlNum-DocketNum-index', 'hash_key': 'MdlNum', 'range_key': 'DocketNum'},  # S, S
                {'name': 'MdlNum-FilingDate-index', 'hash_key': 'MdlNum', 'range_key': 'FilingDate'},  # S, S
                {'name': 'TransferorCourtId-TransferorDocketNum-index', 'hash_key': 'TransferorCourtId',
                 'range_key': 'TransferorDocketNum'},  # S, S
                {'name': 'TransfereeCourtId-TransfereeDocketNum-index', 'hash_key': 'TransfereeCourtId',
                 'range_key': 'TransfereeDocketNum'},  # S, S
                {'name': 'FilingDate-index', 'hash_key': 'FilingDate', 'range_key': None}  # S - New GSI
            ]
        },
        'FBAdArchive': {
            'keys': ['AdArchiveID', 'StartDate'],
            'key_attributes': ['AdArchiveID', 'StartDate'],
            'gsis': [
                {'name': 'LastUpdated-index', 'hash_key': 'LastUpdated', 'range_key': None},
                {'name': 'StartDate-index', 'hash_key': 'StartDate', 'range_key': None},
                {'name': 'PageID-index', 'hash_key': 'PageID', 'range_key': None}
            ]
        },
        'FBAdArchive_Standardized': {
            'keys': ['AdArchiveID', 'StartDate'],
            'key_attributes': ['AdArchiveID', 'StartDate'],
            'gsis': [
                {'name': 'LastUpdated-index', 'hash_key': 'LastUpdated', 'range_key': None},
                {'name': 'StartDate-index', 'hash_key': 'StartDate', 'range_key': None},
                {'name': 'PageID-index', 'hash_key': 'PageID', 'range_key': None}
            ]
        },
        'FBImageHash': {
            'keys': ['ImageHash', 'AdArchiveID'],
            'key_attributes': ['ImageHash', 'AdArchiveID'],
            'gsis': [
                {'name': 'AdArchiveID-index', 'hash_key': 'AdArchiveID', 'range_key': None}
            ]
        },
        'DistrictCourts': {'keys': ['CourtId', 'MdlNum'], 'key_attributes': ['CourtId', 'MdlNum']},
        'LawFirmMap': {'keys': ['LawFirmNorm', 'Name'], 'key_attributes': ['LawFirmNorm', 'Name']},
        'LitigationMap': {'keys': ['MdlNum', 'LitigationName'], 'key_attributes': ['MdlNum', 'LitigationName']},
        'DocketActivity': {'keys': ['MdlNum', 'FilingDate'], 'key_attributes': ['MdlNum', 'FilingDate'],
                           'gsis': [{'name': 'MdlNum-DocNum-index', 'hash_key': 'MdlNum', 'range_key': 'DocNum'}]},
        'JPMLStats': {'keys': ['MdlNum', 'ReportDate'], 'key_attributes': ['MdlNum', 'ReportDate']},
        'JPMLData': {'keys': ['MdlNum', 'ReportDate'], 'key_attributes': ['MdlNum', 'ReportDate']},
    }
    attribute_config = {
        'Pacer': {
            'include': [
                'Office', 'DocketLink', 'Defendant', 'S3Link', 'Plaintiff', 'DocketNum', 'Jury', 'AddedDate',
                'OriginalFilename', 'Mdl', 'AddedOn', 'Cause', 'CaseFlags', 'Presider', 'NewFilename', 'Claims',
                'MdlNum', 'Title', 'Versus', 'LawFirm', 'Jurisdiction', 'Nos', 'FilingDate', 'CourtId',
                'JuryDemand', 'Allegations', 'CourtName', 'LawFirm2', 'PendingCto', 'Flags', 'AttorneysGpt',
                'AssignedTo', 'Referral', 'Attorney', 'TransferorDocketNum', 'IsRemoved', 'S3Html',
                'IsSameFilingDate', 'CaseInOtherCourt', 'TransferorCourtName', 'TransferredIn',
                'TransferredFrom', 'Division', 'Judge', 'LeadCase', 'TransfereeCourtId', 'TransfereeDocketNum',
                'TransferorCourtId', 'IsTransferred', 'UniqueAttorneys', 'DateFiled', 'NormalizedFilename',
                'IsRemoval', 'MatchedLawFirms', 'RemovalDate', 'InitialFilingDate', 'LastErrorDate',
                'ProcessingError', 'TransferorCourtDocketNum', 'MatchedLawFirmsList', 'LawFirmsGptList',
                'AttorneyEmailsGpt', 'MergedLawFirms', 'MergedLawFirmsList', 'LawFirmStats', 'AttorneyEmails',
                'LawFirmsGpt', 'ReferredTo', 'Demand', 'Plaintiffs', 'Defendants', 'OldFilename', 'CtoPending',
                'MatchedLawFirm', 'MatchStatistics', 'HtmlUrl', 'CtoNum', 'CroFiled', 'CtoFinal', 'CroFinal',
                'CtoFiled', 'LitigationName', 'MdlNumber', 'Url', 'BaseFilename', 'HtmlLink', 'S3Url',
                'AttorneyGpt', 'NumPlaintiffs'
            ]
        },
        'PacerDockets': {  # Added configuration for PacerDockets
            'include': [  # Assuming PacerDockets can contain a similar set of fields as Pacer items
                'CourtId', 'DocketNum', 'AddedOn', 'FilingDate', 'MdlNum', 'TransferorCourtId',
                'TransferorDocketNum', 'TransfereeCourtId', 'TransfereeDocketNum',  # Core keys/GSI attributes
                'Office', 'DocketLink', 'Defendant', 'S3Link', 'Plaintiff', 'Jury', 'AddedDate',
                'OriginalFilename', 'Mdl', 'Cause', 'CaseFlags', 'Presider', 'NewFilename', 'Claims',
                'Title', 'Versus', 'LawFirm', 'Jurisdiction', 'Nos',
                'JuryDemand', 'Allegations', 'CourtName', 'LawFirm2', 'PendingCto', 'Flags', 'AttorneysGpt',
                'AssignedTo', 'Referral', 'Attorney', 'IsRemoved', 'S3Html',
                'IsSameFilingDate', 'CaseInOtherCourt', 'TransferorCourtName', 'TransferredIn',
                'TransferredFrom', 'Division', 'Judge', 'LeadCase', 'IsTransferred', 'UniqueAttorneys', 'DateFiled',
                'NormalizedFilename', 'MatchedLawFirms', 'RemovalDate', 'InitialFilingDate', 'LastErrorDate',
                'ProcessingError', 'TransferorCourtDocketNum', 'MatchedLawFirmsList', 'LawFirmsGptList',
                'AttorneyEmailsGpt', 'MergedLawFirms', 'MergedLawFirmsList', 'LawFirmStats', 'AttorneyEmails',
                'LawFirmsGpt', 'ReferredTo', 'Demand', 'Plaintiffs', 'Defendants', 'OldFilename', 'CtoPending',
                'MatchedLawFirm', 'MatchStatistics', 'HtmlUrl', 'CtoNum', 'CroFiled', 'CtoFinal', 'CroFinal',
                'CtoFiled', 'LitigationName', 'MdlNumber', 'Url', 'BaseFilename', 'HtmlLink', 'S3Url',
                'AttorneyGpt', 'NumPlaintiffs'
            ]
        },
        'FBAdArchive': {
            'include': [
                'AdArchiveID', 'StartDate', 'EndDate', 'Title', 'Body', 'LinkDescription',
                'LawFirm', 'PageID', 'PageName', 'IsActive', 'LastUpdated', 'Summary',
                'LinkUrl', 'AdCreativeId', 'CtaText', 'PublisherPlatform'
            ]
        },
        'FBAdArchive_Standardized': {
            'include': [
                'AdArchiveID', 'StartDate', 'EndDate', 'Title', 'Body', 'LinkDescription',
                'LawFirm', 'PageID', 'PageName', 'IsActive', 'LastUpdated', 'Summary',
                'LinkUrl', 'AdCreativeId', 'CtaText', 'PublisherPlatform',
            ]
        },
        'FBImageHash': {
            'include': [
                'ImageHash', 'AdArchiveID', 'S3Key', 'ImageUrl', 'ProcessedDate',
                'Width', 'Height', 'FileSize', 'MimeType', 'ColorProfile'
            ]
        }
        # Add attribute configs for other tables if needed
    }

    def __init__(self, config: Dict[str, Any], table_name: str, use_local: bool = False,
                 local_port: int = 8000) -> None:
        """Initialize the DynamoDB manager."""
        self.config = config
        self.aws_access_key = config.get('aws_access_key')
        self.aws_secret_key = config.get('aws_secret_key')
        self.aws_region = config.get('region_name', 'us-west-2')
        self.date = config.get('iso_date')
        self.table_name = table_name
        self.is_local = use_local
        self.local_port = local_port
        self.retry_config = Config(retries={'max_attempts': 10, 'mode': 'adaptive'})
        # Create a logger without file/line info in output
        self.logger = logging.getLogger(f"{self.__class__.__name__}.{table_name}")
        # Ensure the logger doesn't propagate to avoid duplicate output
        self.logger.propagate = False
        # Add a null handler to prevent "No handlers could be found" warnings
        if not self.logger.handlers:
            self.logger.addHandler(logging.NullHandler())
        self.console = None
        for handler in logging.root.handlers:
            if isinstance(handler, RichHandler) and hasattr(handler, 'console'):
                self.console = handler.console
                break

        if not self.table_name:
            raise ValueError("table_name must be provided")

        self.current_attributes = set(self.attribute_config.get(self.table_name, {}).get('include', []))
        self.exclude_attributes = set()  # Not currently used, but kept for structure

        self.dynamodb = None
        self.table = None
        self.client = None
        self._local_dynamodb_manager: Optional[DockerDynamoDBManager] = None

        if self.is_local:
            self.logger.info(f"Initializing LOCAL mode for table '{self.table_name}' port {self.local_port}")
            self._setup_local_dynamodb()
            self._ensure_local_table_exists()
        else:
            self.logger.info(f"Initializing AWS mode for table '{self.table_name}' region {self.aws_region}")
            if not self.aws_access_key or not self.aws_secret_key:
                self.logger.warning("AWS credentials missing. Using default chain.")
            self._setup_aws_dynamodb()
            try:
                self.table = self.dynamodb.Table(self.table_name)
                self.table.load()
                self.logger.info(f"Connected to AWS table '{self.table_name}'")
            except ClientError as e:
                code = e.response['Error']['Code']
                if code == 'ResourceNotFoundException':
                    self.logger.error(f"AWS table '{self.table_name}' not found in {self.aws_region}.")
                else:
                    self.logger.error(f"Error connecting to AWS table '{self.table_name}': {e}", exc_info=True)
                raise ValueError(f"Could not connect/find AWS table '{self.table_name}'.") from e

        if self.table is None: raise ValueError(f"DynamoDB table '{self.table_name}' not initialized.")

        if self.table_name in self.key_config:
            key_attrs = self.key_config[self.table_name].get('key_attributes', [])
            self.pk_name = key_attrs[0] if key_attrs else None
            self.sk_name = key_attrs[1] if len(key_attrs) > 1 else None
            self.logger.debug(f"PK names init: pk={self.pk_name}, sk={self.sk_name}")
        else:
            self.logger.warning(f"No key config for '{self.table_name}'. PK names not set.")
            self.pk_name = None
            self.sk_name = None

    def _setup_aws_dynamodb(self) -> None:
        """Set up AWS DynamoDB connection."""
        self.logger.debug(f"Setting up boto3 DynamoDB resource for AWS region {self.aws_region}")
        try:
            session_kwargs = {'region_name': self.aws_region}
            if self.aws_access_key and self.aws_secret_key:
                session_kwargs['aws_access_key_id'] = self.aws_access_key
                session_kwargs['aws_secret_access_key'] = self.aws_secret_key
            session = boto3.Session(**session_kwargs)
            self.dynamodb = session.resource('dynamodb', config=self.retry_config)
            self.client = self.dynamodb.meta.client
            self.logger.debug("AWS DynamoDB resource and client created.")
        except Exception as e:
            self.logger.error(f"Failed create AWS DynamoDB resource/client: {e}", exc_info=True)
            raise

    def _setup_local_dynamodb(self) -> None:
        """Initializes and starts the local DynamoDB container and gets the boto3 resource."""
        self.logger.debug(f"Setting up Local DynamoDB via DockerDynamoDBManager on port {self.local_port}")
        try:
            self._local_dynamodb_manager = DockerDynamoDBManager(port=self.local_port)
            self._local_dynamodb_manager.start_dynamodb()
            status = self._local_dynamodb_manager.get_status()
            endpoint_url = status['endpoint_url']
            if not status['running']: raise ConnectionError(f"Local DynamoDB failed start port {self.local_port}")
            self.logger.info(f"Local DynamoDB endpoint: {endpoint_url}")
            self.dynamodb = boto3.resource('dynamodb', endpoint_url=endpoint_url, region_name='us-east-1',
                                           aws_access_key_id='dummy', aws_secret_access_key='dummy',
                                           config=self.retry_config)
            self.client = self.dynamodb.meta.client
            self.logger.debug("Local DynamoDB resource and client created.")
        except Exception as e:
            self.logger.error(f"Failed setup/connect Local DynamoDB: {e}", exc_info=True)
            raise

    def _ensure_local_table_exists(self) -> None:
        """Checks if the local table exists, creates it if not."""
        if not self.is_local or not self.dynamodb: self.logger.error(
            "Cannot ensure local table: Not local or resource not init."); return
        try:
            self.table = self.dynamodb.Table(self.table_name)
            self.table.load()
            self.logger.info(f"Connected to existing local table '{self.table_name}'.")
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                self.logger.warning(f"Local table '{self.table_name}' not found. Creating.")
                self._create_local_table()
            else:
                self.logger.error(f"Error accessing local table '{self.table_name}': {e}",
                                  exc_info=True)
                raise ValueError(
                    f"Could not access local table '{self.table_name}'.") from e
        except Exception as e:
            self.logger.error(f"Unexpected error checking local table '{self.table_name}': {e}", exc_info=True)
            raise

    def _create_local_table(self) -> None:
        """Creates the local DynamoDB table based on key_config and GSI info."""
        if self.table_name not in self.key_config: raise ValueError(
            f"Cannot create '{self.table_name}': No key config.")
        if not self.dynamodb: raise RuntimeError("DynamoDB resource not available for create.")

        key_info = self.key_config[self.table_name]
        key_schema = [{'AttributeName': name, 'KeyType': ('HASH' if i == 0 else 'RANGE')} for i, name in
                      enumerate(key_info['key_attributes'])]
        attr_defs_map = {name: 'S' for name in key_info['key_attributes']}  # Assume String keys

        gsis = []
        if 'gsis' in key_info:
            for gsi_def in key_info['gsis']:
                gsi_name = gsi_def['name']
                gsi_hash = gsi_def['hash_key']
                gsi_range = gsi_def.get('range_key')
                gsi_key_schema = [{'AttributeName': gsi_hash, 'KeyType': 'HASH'}]
                attr_defs_map[gsi_hash] = 'S'  # Assume String
                if gsi_range: gsi_key_schema.append({'AttributeName': gsi_range, 'KeyType': 'RANGE'}); attr_defs_map[
                    gsi_range] = 'S'  # Assume String
                gsis.append(
                    {'IndexName': gsi_name, 'KeySchema': gsi_key_schema, 'Projection': {'ProjectionType': 'ALL'},
                     'ProvisionedThroughput': {'ReadCapacityUnits': 10, 'WriteCapacityUnits': 10}})

        attr_defs = [{'AttributeName': name, 'AttributeType': dtype} for name, dtype in attr_defs_map.items()]
        create_params = {'TableName': self.table_name, 'KeySchema': key_schema, 'AttributeDefinitions': attr_defs,
                         'ProvisionedThroughput': {'ReadCapacityUnits': 10, 'WriteCapacityUnits': 10}}
        if gsis: create_params['GlobalSecondaryIndexes'] = gsis

        try:
            self.logger.info(f"Creating local table '{self.table_name}' with params: {create_params}")
            self.table = self.dynamodb.create_table(**create_params)
            self.logger.info(f"Waiting for local table '{self.table_name}' active...")
            self.table.wait_until_exists()
            self.logger.info(f"Local table '{self.table_name}' created/active.")
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceInUseException':
                self.logger.warning(
                    f"Local table '{self.table_name}' already exists. Connecting.")
                self.table = self.dynamodb.Table(self.table_name)
                self.table.load()
            else:
                self.logger.error(f"Failed create local table '{self.table_name}': {e}",
                                  exc_info=True)
                raise ValueError(
                    f"Could not create local table '{self.table_name}'.") from e
        except Exception as e:
            self.logger.error(f"Unexpected error creating local table '{self.table_name}': {e}", exc_info=True)
            raise

    @staticmethod
    def convert_decimal(value: Any) -> Union[int, float, Any]:
        """ Converts Decimal objects to int or float. Passes other types through. """
        if isinstance(value, Decimal):
            if value % 1 == 0:
                return int(value)
            else:
                return float(value)
        return value

    @staticmethod
    def process_records(records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """ Processes a list of records, converting Decimal types within each record. """
        if not records: return []
        return [convert_decimals(record) for record in records]  # Use module-level function

    @staticmethod
    def process_record(record: Dict[str, Any]) -> Dict[str, Any]:
        """ Processes a single record, converting Decimal types within it. """
        if not record: return {}
        return convert_decimals(record)  # Use module-level function

    @staticmethod
    def snake_or_camel_to_pascal_case(dictionary: Dict[str, Any]) -> Dict[str, Any]:
        """
        Converts keys in a dictionary from snake_case or camelCase to standard PascalCase,
        with a specific override for variants of 'ad_archive_id' to become 'AdArchiveID'.
        Handles existing PascalCase keys correctly.
        """
        pascal_dict = {}
        if not isinstance(dictionary, dict):
            return dictionary  # Return as-is if not a dictionary

        for key, value in dictionary.items():
            if not isinstance(key, str) or not key:
                pascal_dict[key] = value  # Keep non-string or empty keys as they are
                continue

            original_key_lower = key.lower().replace('_', '')  # Normalize for override check

            # --- Apply specific override FIRST based on normalized key ---
            if original_key_lower == 'adarchiveid':
                final_pascal_key = 'AdArchiveID'
            # --- Add other specific overrides here if needed ---
            # elif original_key_lower == 'someotherkey':
            #     final_pascal_key = 'SomeOtherSpecificCase'
            else:
                # --- General PascalCase Conversion ---
                # If it contains '_', assume snake_case
                if '_' in key:
                    parts = key.split('_')
                    pascal_parts = [part.capitalize() for part in parts if part]
                # Handle camelCase or existing PascalCase
                else:
                    s1 = re.sub(r'(.)([A-Z][a-z]+)', r'\1_\2', key)  # Add _ before Cap if preceded by anything
                    s2 = re.sub(r'([a-z0-9])([A-Z])', r'\1_\2', s1)  # Add _ before Cap if preceded by lower/digit
                    parts = s2.split('_')
                    pascal_parts = [part.capitalize() for part in parts if part]

                final_pascal_key = "".join(pascal_parts)

                # Optional: Handle common initialisms like 'Id' explicitly if needed after general conversion
                # Example: if key ends with 'Id' and should be 'ID'
                # if final_pascal_key.endswith('Id') and not final_pascal_key.endswith('Guid'): # Avoid changing Guid
                #     final_pascal_key = final_pascal_key[:-2] + 'ID'

            pascal_dict[final_pascal_key] = value

        return pascal_dict

    @staticmethod
    def pascal_to_snake_case(pascal_string: str) -> str:
        """ Convert a PascalCase string to snake_case. """
        if not isinstance(pascal_string, str) or not pascal_string: return pascal_string
        s1 = re.sub(r'(?<=[a-z0-9])([A-Z])', r'_\1', pascal_string)
        s2 = re.sub(r'([A-Z]+)([A-Z][a-z])', r'\1_\2', s1)
        return s2.lower()

    def convert_dict_keys_to_snake_case(self, pascal_dict: Dict[str, Any]) -> Dict[str, Any]:
        """ Convert all keys in a dictionary from PascalCase to snake_case. """
        if not isinstance(pascal_dict, dict): return pascal_dict
        return {self.pascal_to_snake_case(k): v for k, v in pascal_dict.items()}

    @staticmethod
    def sanitize_record(record: Dict[str, Any]) -> Dict[str, Union[Decimal, str, bool, int, list, dict, set, bytes]]:
        """ Sanitizes a record's values for DynamoDB compatibility. """
        sanitized_record: Dict[str, Union[Decimal, str, bool, int, list, dict, set, bytes]] = {}
        # Use a distinct logger name for static method clarity
        _logger = logging.getLogger(f"{__name__}.DynamoDbBaseManager.sanitize_record")

        if not isinstance(record, dict):
            _logger.warning(f"Input to sanitize_record not dict: {type(record)}. Returning as-is.")
            return record

        # --- Log Input Record (Full JSON) ---
        if _logger.isEnabledFor(logging.DEBUG):
            try:
                # Log the full input record using json.dumps for readability
                # Use default=str for non-serializable types like Decimal before sanitization
                record_str = json.dumps(record, indent=2, default=str)
                _logger.debug(f"Sanitizing Input Record:\n{record_str}")
            except Exception as log_e:
                _logger.debug(
                    f"Sanitizing Input Record: [Error logging full record: {log_e}] Snippet: {str(record)[:250]}...")

        # --- Iterate and Sanitize ---
        for k, v in record.items():
            # --- Debug Log for Specific Key (e.g., AdArchiveID) ---
            # Check specifically for the primary key being investigated
            # This requires knowing the key name ('AdArchiveID' in this context)
            if k == 'AdArchiveID':
                _logger.debug(f"-- Processing key '{k}': Value='{v}', Type={type(v)}")
                if v is None or v == '':
                    # Log clearly if the primary key value causes it to be skipped
                    _logger.warning(f"-- Key '{k}' WILL BE SKIPPED by sanitizer because value is None or empty string.")

            # --- Original Skipping Logic ---
            if v is None or v == '':
                continue  # Skip adding the key/value if value is None or empty string

            # --- Original Sanitization Logic ---
            if isinstance(v, float):
                # Handle NaN, Infinity
                if pd.isna(v) or v == float('inf') or v == float('-inf'):
                    _logger.debug(f"-- Skipping key '{k}' due to non-finite float value: {v}")
                    continue
                try:
                    # Convert finite floats to Decimal
                    sanitized_record[k] = Decimal(str(v))
                except InvalidOperation:
                    # Fallback to string if conversion fails (rare for standard floats)
                    _logger.warning(f"-- Could not convert float {v} to Decimal for key '{k}'. Using string fallback.")
                    sanitized_record[k] = str(v)
            elif isinstance(v, int):
                # Convert integers to Decimal
                sanitized_record[k] = Decimal(v)
            elif isinstance(v, (str, bool, list, dict, set, bytes, bytearray, Decimal)):
                # Keep compatible types as they are
                sanitized_record[k] = v
            else:
                # Attempt to convert other types to string as a fallback
                try:
                    sanitized_record[k] = str(v)
                    _logger.debug(f"-- Converted value for key '{k}' of type {type(v)} to string.")
                except Exception as e:
                    # Log and skip if conversion to string fails
                    _logger.warning(
                        f"-- Could not convert key '{k}' to string. Type: {type(v)}. Value snippet: {str(v)[:50]}. Error: {e}. Skipping key.")
                    continue  # Skip if cannot be converted

        # --- Log Output Record (Full JSON) ---
        if _logger.isEnabledFor(logging.DEBUG):
            try:
                # Log the full sanitized record using json.dumps
                # Use default=str as fallback for any remaining non-serializable types (shouldn't be needed if sanitize worked)
                sanitized_str = json.dumps(sanitized_record, indent=2, default=str)
                _logger.debug(f"Sanitizer Returning Record:\n{sanitized_str}")
            except Exception as log_e:
                _logger.debug(
                    f"Sanitizer Returning Record: [Error logging full record: {log_e}] Snippet: {str(sanitized_record)[:250]}...")

        return sanitized_record

    # --- END CORRECTED sanitize_record METHOD ---

    def scan_table(self, **kwargs) -> Generator[Dict[str, Any], None, None]:
        """ Scans the table with pagination, handling throttling. Converts Decimals. """
        logger = getattr(self, 'logger', logging.getLogger(__name__))
        scan_kwargs = kwargs.copy()
        table_name_log = getattr(getattr(self, 'table', None), 'name', self.table_name)
        action = f"scan on table {table_name_log} with args: {scan_kwargs}"
        logger.debug(f"Starting {action}")
        total_scanned = 0
        total_yielded = 0
        page = 0
        max_retries = 5
        delay = 1
        if not hasattr(self, 'filter_record'): raise AttributeError(f"'{type(self).__name__}' lacks 'filter_record'")
        try:
            while True:
                page += 1
                attempt = 0
                response = None
                while attempt < max_retries:
                    try:
                        # logger.debug(f"Scanning page {page}, attempt {attempt + 1}...")
                        response = self.table.scan(**scan_kwargs)
                        total_scanned += response.get('ScannedCount', 0)
                        break
                    except ClientError as e:
                        if e.response['Error']['Code'] in ['ProvisionedThroughputExceededException',
                                                           'ThrottlingException']:
                            attempt += 1
                            if attempt >= max_retries: logger.error(
                                f"Max retries throttling page {page}. Aborting."); raise e
                            wait = delay * (2 ** attempt)
                            logger.warning(f"Throttling page {page}, attempt {attempt}. Retry {wait}s...")
                            time.sleep(wait)
                        else:
                            logger.error(f"ClientError scan page {page}: {e}")
                            raise e
                    except Exception as e_inner:
                        logger.error(f"Unexpected error scan attempt {attempt + 1} page {page}: {e_inner}",
                                     exc_info=True)
                        raise e_inner
                page_items = response.get('Items', [])
                if not hasattr(self, 'filter_record'):
                    raise AttributeError(f"'{type(self).__name__}' lacks 'filter_record'")
                for item_raw in page_items:
                    item_id = "ID_ERROR"  # Default ID
                    try:
                        _, key_names = self.get_table_key_info()
                        item_id = "/".join(
                            [str(item_raw.get(k, f"NA_{k}")) for k in key_names]) if key_names else str(
                            item_raw.get(self.pk_name, '?PK'))
                    except Exception as id_err:
                        logger.warning(f"Could not get item ID for scan debug: {id_err}")
                    try:
                        item_sanitized = convert_decimals(item_raw)
                        filtered_item = self.filter_record(item_sanitized)
                        if filtered_item: yield filtered_item; total_yielded += 1
                    except AttributeError as filter_ae:
                        logger.error(f"ATTRIBUTE ERROR item ID '{item_id}': {filter_ae}",
                                     exc_info=True)
                        raise filter_ae
                    except Exception as item_e:
                        logger.error(f"Error processing item ID '{item_id}': {item_e}", exc_info=True)
                        continue
                last_key = response.get('LastEvaluatedKey')
                if not last_key:
                    logger.debug("Scan complete.")
                    break
                else:
                    logger.debug(f"Continuing scan...")
                    scan_kwargs['ExclusiveStartKey'] = last_key
            logger.info(f"Completed {action}. Yielded: {total_yielded:,}, Scanned by DB: {total_scanned:,}")
        except (ClientError, Exception) as e:
            logger.error(f"Error during {action}: {e}", exc_info=True)
            raise

    def count_table_items(self) -> int:
        """Efficiently count all items in the DynamoDB table using COUNT select."""
        self.logger.debug(f"Counting items in table {self.table.name}")
        count = 0
        try:
            scan_kwargs = {'Select': 'COUNT'}
            while True:
                response = self.table.scan(**scan_kwargs)
                count += response['Count']
                if 'LastEvaluatedKey' not in response: break
                scan_kwargs['ExclusiveStartKey'] = response['LastEvaluatedKey']
        except ClientError as e:
            if e.response['Error']['Code'] == 'ProvisionedThroughputExceededException':
                self.logger.warning(f"Throttling count {self.table.name}.")
                return -1
            else:
                self.logger.error(f"Error counting {self.table.name}: {e}", exc_info=True)
                return -1
        except Exception as e:
            self.logger.error(f"Unexpected error counting {self.table.name}: {e}", exc_info=True)
            return -1
        self.logger.info(f"Items counted in {self.table.name}: {count:,}")
        return count

    def filter_record(self, record: Dict[str, Any], no_filter=False) -> Dict[str, Any]:
        """Filter a record based on include/exclude attributes (bypassed if local)."""
        logger = getattr(self, 'logger', logging.getLogger(__name__))
        if self.is_local and not no_filter: return record
        item_id = record.get(self.pk_name, 'UNKNOWN_ID') if self.pk_name else 'UNKNOWN_ID'
        try:
            current_attrs = getattr(self, 'current_attributes', set())
            exclude_attrs = getattr(self,
                                    'exclude_attributes',
                                    set())
        except Exception as attr_e:
            logger.error(f"Error getting filter attrs item '{item_id}': {attr_e}")
            return record
        try:
            if no_filter or (not current_attrs and not exclude_attrs): return record
            if current_attrs:
                return {k: v for k, v in record.items() if k in current_attrs}
            elif exclude_attrs:
                return {k: v for k, v in record.items() if k not in exclude_attrs}
            else:
                return record
        except Exception as filter_e:
            logger.error(f"Error filter_record item ID '{item_id}': {filter_e}", exc_info=True)
            return record

    def get_table_key_info(self) -> Tuple[List[str], List[str]]:
        """Gets primary key attribute names based on configuration."""
        if self.table_name not in self.key_config: raise ValueError(f"No key config for table: {self.table_name}")
        key_info = self.key_config[self.table_name]
        keys = key_info.get('keys', [])
        key_attributes = key_info.get('key_attributes', [])
        if not keys or not key_attributes: self.logger.warning(f"Key config incomplete for {self.table_name}.")
        return keys, key_attributes

    def validate_record(self, record: Dict[str, Any], key_attributes: List[str]) -> bool:
        """Validates if key attributes are present and non-empty in a record."""
        for attr in key_attributes:
            value = record.get(attr)
            if value is None or value == '' or (isinstance(value, float) and pd.isna(value)):
                record_keys = {k: record.get(k) for k in key_attributes}
                self.logger.error(f"Record missing/empty key '{attr}'. Keys: {record_keys}")
                return False
        return True

        # --- File: dynamodb_base_manager.py ---

    def batch_insert_items(
            self,
            records: List[Dict[str, Any]],
            batch_size: int = 25,  # Max batch size for BatchWriteItem is 25
            disable_progress: bool = False,
            # Removed debug_individual_put and overwrite parameters
            **kwargs
    ) -> Tuple[int, int]:
        """
        Efficiently adds multiple new records using manual DynamoDB BatchWriteItem calls
        with explicit retry logic for throttling and unprocessed items.

        Args:
            records (List[Dict[str, Any]]): List of records to insert (in snake_case or camelCase).
            batch_size (int): Number of items per batch request (max 25).
            disable_progress (bool): Disable the progress bar display.
            **kwargs: Accepts 'sleep_time' for compatibility, but it's ignored.

        Returns:
            Tuple[int, int]: (Number of items successfully written, Number of items failed/unprocessed after retries)
        """
        logger = self.logger
        # Log and ignore unexpected kwargs other than sleep_time (for compatibility)
        allowed_kwargs = {'sleep_time'}
        unused_kwargs = {k: v for k, v in kwargs.items() if k not in allowed_kwargs}
        if unused_kwargs:
            logger.debug(f"Ignoring unexpected kwargs: {unused_kwargs}")

        if not records:
            logger.info("No records provided for batch insert.")
            return 0, 0

        if not self.client:
            logger.error("DynamoDB client is not initialized. Cannot perform batch insert.")
            return 0, len(records)

        if not self.pk_name:
            logger.error(f"Primary key name not set for table '{self.table_name}'. Cannot perform batch insert.")
            return 0, len(records)

        batch_size = max(1, min(batch_size, 25))
        total_records_initial = len(records)
        operation_mode = "MANUAL BATCH INSERT (with retries)"
        logger.info(
            f"Starting batch insert. Mode: {operation_mode}. Records: {total_records_initial:,}. "
            f"Table: '{self.table_name}'. Batch Size: {batch_size}"
        )

        # --- Stage 1: Prepare and Validate Records ---
        prepared_items = []
        failed_prep_count = 0
        successful_prep_count = 0
        prep_progress = None
        prep_task = None

        # Use a separate progress bar for preparation if not disabled
        prep_maybe_progress = contextlib.nullcontext()
        if not disable_progress and hasattr(self, 'console') and self.console:
            try:
                # Ensure necessary imports are available
                from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn
                prep_progress = Progress(
                    SpinnerColumn(), "[progress.description]{task.description}", BarColumn(),
                    "[progress.percentage]{task.percentage:>3.0f}%",
                    TextColumn("Prepared: {task.completed}/{task.total}"),
                    TextColumn("Failed Prep: {task.fields[errors]}"),
                    TimeElapsedColumn(),
                    transient=False, console=self.console
                )
                prep_maybe_progress = prep_progress
                prep_task = prep_progress.add_task(f"[cyan]Prep Records", total=total_records_initial, errors=0)
            except ImportError:
                logger.warning("Rich library not found, disabling preparation progress bar.")
            except Exception as e:
                logger.error(f"Error initializing preparation progress bar: {e}", exc_info=True)

        with prep_maybe_progress:
            for i, record in enumerate(records):
                pk_value_log = 'N/A'
                try:
                    record_pascal = self.snake_or_camel_to_pascal_case(record)
                    record_sanitized = self.sanitize_record(record_pascal)
                    pk_value = record_sanitized.get(self.pk_name)
                    pk_value_log = pk_value
                    sk_value = record_sanitized.get(self.sk_name) if self.sk_name else None

                    # Validate keys after sanitization
                    if pk_value is None or pk_value == '':
                        raise ValueError(f"Missing/empty PK '{self.pk_name}'")
                    if self.sk_name and (sk_value is None or sk_value == ''):
                        raise ValueError(f"Missing/empty SK '{self.sk_name}'")

                    # Adjust specific keys if needed (Example kept for compatibility)
                    item_to_write = record_sanitized.copy()
                    if self.table_name == "FBAdArchive" and 'PageId' in item_to_write:
                        page_id_value = item_to_write.pop('PageId')
                        item_to_write['PageID'] = page_id_value

                    # Add the prepared item as a 'PutRequest' structure
                    prepared_items.append({'PutRequest': {'Item': item_to_write}})
                    successful_prep_count += 1

                except (ValueError, KeyError) as val_err:
                    failed_prep_count += 1
                    logger.error(
                        f"PREP {i + 1}/{total_records_initial}: FAILED PREP (skipped): {val_err}. PK={pk_value_log}.")
                except Exception as prep_e:
                    failed_prep_count += 1
                    logger.error(
                        f"PREP {i + 1}/{total_records_initial}: UNEXPECTED PREP ERROR (skipped): {prep_e}.",
                        exc_info=True)

                # Update preparation progress bar
                if prep_task and prep_progress:
                    prep_progress.update(prep_task, advance=1, errors=failed_prep_count)

        logger.info(
            f"Preparation complete. Successfully Prepared: {successful_prep_count:,}, Failed Prep: {failed_prep_count:,}")

        if not prepared_items:
            logger.warning("No items were successfully prepared for writing.")
            return 0, total_records_initial

        # --- Stage 2: Batch Write with Retries ---
        items_to_process = list(prepared_items)  # Items needing to be written
        successfully_written_count = 0
        max_retries = 10  # Max attempts for unprocessed items/throttling
        initial_delay = 1.0  # seconds
        current_attempt = 0

        write_progress = None
        write_task = None
        total_prepared = len(items_to_process)

        # Progress bar for the writing phase
        write_maybe_progress = contextlib.nullcontext()
        if not disable_progress and hasattr(self, 'console') and self.console:
            try:
                # Ensure necessary imports are available
                from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn, \
                    TimeRemainingColumn
                write_progress = Progress(
                    SpinnerColumn(), "[progress.description]{task.description}", BarColumn(),
                    "[progress.percentage]{task.percentage:>3.0f}%",
                    TextColumn("Written: {task.completed}/{task.total}"),
                    TextColumn("Pending/Failed: {task.fields[pending]}"),
                    TimeRemainingColumn(), TimeElapsedColumn(),
                    transient=False, console=self.console
                )
                write_maybe_progress = write_progress
                # Use total_prepared as the total for this progress bar
                write_task = write_progress.add_task(f"[green]Write Batches", total=total_prepared,
                                                     pending=total_prepared)
            except ImportError:
                logger.warning("Rich library not found, disabling write progress bar.")
                disable_progress = True  # Also disable write progress if libs missing
            except Exception as e:
                logger.error(f"Error initializing write progress bar: {e}", exc_info=True)
                disable_progress = True

        with write_maybe_progress:
            while items_to_process and current_attempt < max_retries:
                if current_attempt > 0:
                    # Apply exponential backoff with jitter
                    wait_time = initial_delay * (2 ** current_attempt)
                    wait_time += random.uniform(0, 0.5 * wait_time)  # Add jitter up to 50%
                    wait_time = min(wait_time, 30)  # Cap wait time
                    logger.warning(
                        f"Retry {current_attempt}/{max_retries}. Waiting {wait_time:.2f}s before processing {len(items_to_process)} remaining items...")
                    time.sleep(wait_time)
                    if write_task and write_progress:
                        write_progress.update(write_task, description=f"[yellow]Write Retry {current_attempt}")

                current_attempt += 1
                logger.info(
                    f"Starting write attempt {current_attempt}/{max_retries} for {len(items_to_process)} items.")
                items_in_this_attempt = list(items_to_process)  # Copy list for this attempt
                items_to_process.clear()  # Reset for collecting unprocessed items

                batches = [items_in_this_attempt[i:i + batch_size] for i in
                           range(0, len(items_in_this_attempt), batch_size)]

                for batch_index, batch in enumerate(batches):
                    request_items = {self.table_name: batch}
                    batch_log_id = f"Attempt {current_attempt}, Batch {batch_index + 1}/{len(batches)}"
                    try:
                        # logger.debug(f"{batch_log_id}: Sending {len(batch)} items.")
                        response = self.client.batch_write_item(RequestItems=request_items)

                        unprocessed = response.get('UnprocessedItems', {}).get(self.table_name, [])

                        processed_count = len(batch) - len(unprocessed)
                        successfully_written_count += processed_count
                        # logger.debug(f"{batch_log_id}: Success. Written: {processed_count}, Unprocessed: {len(unprocessed)}")

                        if unprocessed:
                            logger.warning(
                                f"{batch_log_id}: Received {len(unprocessed)} unprocessed items. Adding to retry queue.")
                            items_to_process.extend(unprocessed)  # Add unprocessed items back for the next attempt

                        # Update progress bar based on successfully written items in this batch
                        if write_task and write_progress:
                            pending_count = total_prepared - successfully_written_count
                            write_progress.update(write_task, completed=successfully_written_count,
                                                  pending=pending_count)

                    except ClientError as e:
                        error_code = e.response['Error']['Code']
                        if error_code in ['ProvisionedThroughputExceededException', 'ThrottlingException']:
                            logger.warning(
                                f"{batch_log_id}: Throttled ({error_code}). Adding all {len(batch)} items to retry queue.")
                            items_to_process.extend(batch)  # Retry the whole batch
                            # Optional: Add a small delay immediately after throttling?
                            # time.sleep(0.5 + random.random())
                        elif error_code == 'RequestLimitExceeded':
                            logger.warning(
                                f"{batch_log_id}: RequestLimitExceeded. Adding all {len(batch)} items to retry queue.")
                            items_to_process.extend(batch)  # Retry the whole batch
                        else:
                            # Non-retriable client error for this batch
                            logger.error(
                                f"{batch_log_id}: Non-retriable ClientError ({error_code}) for {len(batch)} items: {e}",
                                exc_info=False)
                            # Add to final failed count - these won't be retried
                            # (Implicitly handled as they remain unprocessed after max retries)
                            # Keep them in items_to_process for now, they'll fail max retries.
                            items_to_process.extend(batch)

                    except Exception as e:
                        # Catch-all for other unexpected errors during batch write
                        logger.error(f"{batch_log_id}: Unexpected error processing {len(batch)} items: {e}",
                                     exc_info=True)
                        # Assume these are non-retriable for safety
                        # (Implicitly handled as they remain unprocessed after max retries)
                        items_to_process.extend(batch)

                # After processing all batches in the attempt, check if any items remain for the next attempt
                if items_to_process:
                    logger.info(
                        f"End of attempt {current_attempt}. {len(items_to_process)} items remain to be processed.")
                    if write_task and write_progress:
                        pending_count = len(items_to_process)
                        write_progress.update(write_task, pending=pending_count)
                else:
                    logger.info(
                        f"Attempt {current_attempt} completed successfully. All {total_prepared} prepared items written.")
                    if write_task and write_progress:
                        write_progress.update(write_task, completed=total_prepared, pending=0,
                                              description="[green]Write Complete")
                    break  # Exit the while loop

            # --- End While Loop (Retries) ---

        # Final calculation of failures
        # Failed prep + items still unprocessed after max retries
        final_failed_count = failed_prep_count + len(items_to_process)
        final_success_count = successfully_written_count

        if final_failed_count > 0:
            logger.warning(
                f"Batch insert finished for '{self.table_name}'. "
                f"Items Successfully Written: {final_success_count:,}. "
                f"Items Failed (Prep or Unprocessed): {final_failed_count:,} "
                f"(out of {total_records_initial:,} initial records)."
            )
            if items_to_process:
                logger.warning(f"{len(items_to_process)} items remained unprocessed after {max_retries} attempts.")
        else:
            logger.info(
                f"Batch insert completed successfully for all {final_success_count:,} "
                f"prepared records in '{self.table_name}' (out of {total_records_initial:,} initial)."
            )

        # Return the count of successfully written items and the count of failed/unprocessed items.
        return final_success_count, final_failed_count

    def update_item(self, key: Dict[str, Any], update_data: Dict[str, Any],
                    consistent_read_verify: bool = False) -> bool:
        """
        Update a record identified by its primary key using UpdateItem.
        Ensures update_data is sanitized.
        Crucially ensures key values match expected string types for string-defined keys.

        Args:
            key (Dict[str, Any]): Dictionary containing the primary key components.
            update_data (Dict[str, Any]): Dictionary of attributes to update.
            consistent_read_verify (bool): If True, perform a consistent read after update.

        Returns:
            bool: True if the update API call was successful, False otherwise.
        """
        logger = getattr(self, 'logger', logging.getLogger(__name__))
        table_name_log = getattr(getattr(self, 'table', None), 'name', self.table_name)

        if not isinstance(key, dict) or not key:
            logger.error(f"Invalid key provided for update on table '{table_name_log}': {key}")
            return False

        processed_key = {}
        try:
            # Determine expected key attribute names for this table
            # This assumes key_config has 'key_attributes' correctly defined for self.table_name
            # and that all key attributes for FBAdArchive are indeed strings.
            _, configured_key_names = self.get_table_key_info()

            for k_attr_name, v_key_val in key.items():
                if k_attr_name not in configured_key_names:
                    logger.warning(
                        f"Key attribute '{k_attr_name}' in provided key is not in configured key_attributes for table '{table_name_log}'. It will be passed as is.")

                if v_key_val is None:
                    logger.error(
                        f"Key attribute '{k_attr_name}' cannot be None for update on '{table_name_log}'. Key: {key}")
                    return False

                # FOR FBAdArchive, AdArchiveID and StartDate are ALWAYS STRINGS.
                # Force conversion to string for all key components.
                processed_key[k_attr_name] = str(v_key_val).strip()
                if not processed_key[k_attr_name]:  # Check if empty after strip
                    logger.error(
                        f"Key attribute '{k_attr_name}' became empty string after strip for update on '{table_name_log}'. Key: {key}")
                    return False

        except Exception as key_e:
            logger.error(f"Error processing key for update on '{table_name_log}': {key_e}. Original Key: {key}",
                         exc_info=True)
            return False

        log_key_str = json.dumps(processed_key, default=str)  # For logging

        if not isinstance(update_data, dict):  # update_data must be a dict
            logger.error(
                f"Invalid update_data (not a dict) for key {log_key_str} on '{table_name_log}'. Type: {type(update_data)}")
            return False
        if not update_data:  # No actual data to update
            logger.debug(f"No update data provided for key {log_key_str} on '{table_name_log}'. Skipping update call.")
            return True

        try:
            update_data_pascal = self.snake_or_camel_to_pascal_case(update_data)
            sanitized_update_data = self.sanitize_record(update_data_pascal)

            if self.pk_name in sanitized_update_data: del sanitized_update_data[self.pk_name]
            if self.sk_name and self.sk_name in sanitized_update_data: del sanitized_update_data[self.sk_name]

            if not sanitized_update_data:
                logger.info(
                    f"Update data for key {log_key_str} became empty after sanitization/key removal. Skipping update call.")
                return True
        except Exception as sanitize_e:
            logger.error(f"Error sanitizing update_data for key {log_key_str} on '{table_name_log}': {sanitize_e}",
                         exc_info=True)
            return False

        update_parts = []
        expr_vals = {}
        expr_names = {}
        n_ctr = 0
        v_ctr = 0

        for k, v_update_val in sanitized_update_data.items():
            if v_update_val is None or (isinstance(v_update_val,
                                                   str) and not v_update_val.strip()):  # Check for None or empty/whitespace-only string
                # Instead of skipping, we should REMOVE the attribute if the intent is to clear it
                # However, for this specific PageID fix, we are only SETTING.
                # If v_update_val is an empty string from sanitize_record, it means the original was also empty.
                # If the goal is to ensure PageID is set to a non-empty string, the calling code (FBDuplicateScanner.commit)
                # should ensure `changes_dict` only contains non-empty strings for PageID.
                # `sanitize_record` ALREADY skips None or empty string original values.
                # So, if a value makes it here, it should be valid according to sanitize_record.
                logger.debug(
                    f"Value for '{k}' in sanitized_update_data is None or effectively empty for key {log_key_str}. It won't be part of SET.")
                continue

            name_placeholder = f"#n{n_ctr}"
            expr_names[name_placeholder] = k
            n_ctr += 1

            val_placeholder = f":v{v_ctr}"
            # Critical: Use the value directly from sanitized_update_data
            # `sanitize_record` prepares values for DynamoDB (e.g., numbers to Decimal)
            expr_vals[val_placeholder] = v_update_val
            v_ctr += 1

            update_parts.append(f"{name_placeholder} = {val_placeholder}")

        if not update_parts:
            logger.warning(
                f"No valid update expression parts generated for key {log_key_str} on '{table_name_log}'. Data was: {sanitized_update_data}")
            return True

        update_expr = "SET " + ", ".join(update_parts)

        # --- DEBUG PRINT within DynamoDbBaseManager.update_item ---
        logger.info(
            f"[DBM_UPDATE_ITEM_DEBUG] Key for DDB: {processed_key} (Types: PK='{type(processed_key.get(self.pk_name)).__name__}', SK='{type(processed_key.get(self.sk_name)).__name__ if self.sk_name else 'N/A'}')")
        logger.info(f"[DBM_UPDATE_ITEM_DEBUG] UpdateExpression: {update_expr}")
        logger.info(f"[DBM_UPDATE_ITEM_DEBUG] ExpressionAttributeNames: {expr_names}")
        log_vals_for_debug = {k_val: (v_val, type(v_val).__name__) for k_val, v_val in expr_vals.items()}  # Show types
        logger.info(f"[DBM_UPDATE_ITEM_DEBUG] ExpressionAttributeValues (with types): {log_vals_for_debug}")
        # --- END DEBUG PRINT ---

        try:
            upd_kwargs = {
                'Key': processed_key,  # This key MUST have string values for AdArchiveID and StartDate
                'UpdateExpression': update_expr,
                'ExpressionAttributeNames': expr_names,
                'ExpressionAttributeValues': expr_vals,  # These are sanitized values
                'ReturnValues': "UPDATED_NEW"
            }
            response = self.table.update_item(**upd_kwargs)

            # Check if attributes were actually updated
            if response.get('Attributes'):
                logger.info(
                    f"Update API call successful and attributes changed for key {log_key_str} on '{table_name_log}'. Changed: {response['Attributes']}")
            else:
                # This can happen if the values sent were the same as existing ones, or if a condition wasn't met (not used here)
                logger.info(
                    f"Update API call successful for key {log_key_str}, but no attributes reported as changed in response. This might be okay if values were already current.")

            # Consistent read verification can be added back if needed, but let's simplify first.
            # For now, assume API success means the operation likely worked if keys and data were correct.
            return True

        except ClientError as e:
            error_code = e.response['Error']['Code']
            logger.error(
                f"ClientError updating key {log_key_str} on '{table_name_log}' ({error_code}): {e.response['Error']['Message']}",
                exc_info=True)  # Full exc_info
            return False
        except Exception as e:
            logger.error(f"Unexpected error updating key {log_key_str} on '{table_name_log}': {e}", exc_info=True)
            return False

    @staticmethod
    def compare_records(dynamodb_record: Dict[str, Any], local_record: Dict[str, Any],
                        remove_missing_fields: bool = False) -> Tuple[Dict[str, Any], List[str]]:
        """ Compare DynamoDB and local records, return differences. Handles Decimals."""
        update_data = {}
        remove_data = []
        logger = logging.getLogger(__name__)
        db_sanitized = convert_decimals(dynamodb_record)
        local_sanitized = convert_decimals(local_record)
        for key, local_val in local_sanitized.items():
            db_val = db_sanitized.get(key)
            if db_val != local_val: update_data[key] = local_val  # Use sanitized local value for update
        if remove_missing_fields:
            preserved = {'AddedOn', 'UpdatedOn', 'LastModifiedDate'}  # Example metadata
            try:
                _, key_attrs = DynamoDbBaseManager.get_table_key_info(DynamoDbBaseManager)
                preserved.update(key_attrs)
            except Exception:
                logger.warning("Cannot get PKs for compare preservation.")
            for key in db_sanitized:
                if key not in local_sanitized and key not in preserved: remove_data.append(key)
        return update_data, remove_data

    @staticmethod
    def check_s3_file_exists(s3_link: str) -> bool:
        """ Checks if a file exists in S3 given its HTTPS link. """
        logger = logging.getLogger(__name__)
        if not s3_link or not s3_link.startswith("https://"): logger.warning(
            f"Invalid S3 link: {s3_link}"); return False
        try:
            parts = s3_link.replace("https://", "").split("/")
            bucket_host = parts[0]
            if '.s3.' not in bucket_host: logger.error(f"Cannot parse bucket from host: {bucket_host}"); return False
            bucket = bucket_host.split(".")[0]
            key = "/".join(parts[1:])
            if not bucket or not key: logger.error(f"Cannot parse bucket/key from S3 link: {s3_link}"); return False
            s3 = boto3.client('s3')
            s3.head_object(Bucket=bucket, Key=key)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404' or 'Not Found' in e.response['Error']['Message']:
                return False
            else:
                logger.error(f"S3 Error checking {s3_link}: {e}")
                return False
        except Exception as e:
            logger.error(f"Unexpected error checking S3 {s3_link}: {e}", exc_info=True)
            return False

    def get_records_by_added_on(self, iso_date: str, index_name: str = 'AddedOn-index') -> List[Dict[str, Any]]:
        """ Queries records using AddedOn GSI. Converts Decimals."""
        logger = getattr(self, 'logger', logging.getLogger(__name__))
        all_items_raw = []
        query_kwargs = {'IndexName': index_name, 'KeyConditionExpression': Key('AddedOn').eq(iso_date)}
        try:
            logger.info(f"Querying '{index_name}' for AddedOn {iso_date}")
            while True:
                response = self.table.query(**query_kwargs)
                all_items_raw.extend(response.get('Items', []))
                last_key = response.get('LastEvaluatedKey')
                if not last_key: break
                query_kwargs['ExclusiveStartKey'] = last_key
            logger.info(f"Found {len(all_items_raw)} raw items via '{index_name}' for AddedOn {iso_date}")
            processed = [convert_decimals(item) for item in all_items_raw]
            logger.info(f"Returning {len(processed)} processed items for {iso_date}")
            return processed
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                logger.error(f"GSI '{index_name}' not on '{self.table.name}'.")
            else:
                logger.error(f"Failed query AddedOn {iso_date} index {index_name}: {e}", exc_info=True)
            return []
        except Exception as e:
            logger.error(f"Unexpected error query AddedOn {iso_date}: {e}", exc_info=True)
            return []

    def query_items(self, key_conditions: Dict[str, Any], index_name: Optional[str] = None, **kwargs) -> List[
        Dict[str, Any]]:
        """ Query items based on key conditions. Converts Decimals."""
        logger = getattr(self, 'logger', logging.getLogger(__name__))
        query_kwargs = kwargs.copy()
        if not isinstance(key_conditions, dict) or not key_conditions: raise ValueError("key_conditions dict required.")
        key_expr = None
        for k, v in key_conditions.items(): key_expr = Key(k).eq(v) if key_expr is None else key_expr & Key(k).eq(v)
        if key_expr is None: raise ValueError("Cannot construct KeyConditionExpression.")
        query_kwargs['KeyConditionExpression'] = key_expr
        target = f"index '{index_name}'" if index_name else "base table"
        query_kwargs.setdefault('IndexName', index_name)
        logger.info(f"Querying {target} with conditions: {key_conditions} args: {kwargs}")
        all_items_raw = []
        try:
            while True:
                response = self.table.query(**query_kwargs)
                all_items_raw.extend(response.get('Items', []))
                last_key = response.get('LastEvaluatedKey')
                if not last_key: break
                query_kwargs['ExclusiveStartKey'] = last_key
            logger.info(f"Query found {len(all_items_raw)} raw items.")
            processed = [convert_decimals(item) for item in all_items_raw]
            logger.info(f"Returning {len(processed)} processed items.")
            return processed
        except ClientError as e:
            code = e.response['Error']['Code']
            if code == 'ResourceNotFoundException':
                logger.error(f"Table/Index '{index_name or self.table.name}' not found.")
            elif code == 'ValidationException':
                logger.error(f"Validation error query. Cond={key_conditions}, Idx={index_name}. Err: {e}")
            else:
                logger.error(f"ClientError query: {e}", exc_info=True)
            return []
        except Exception as e:
            logger.error(f"Unexpected error query: {e}", exc_info=True)
            return []

    def query_by_date(self, start_date_str: str, end_date_str: Optional[str] = None,
                      date_field: Optional[str] = None, index_name: Optional[str] = None,
                      projection_expression: Optional[str] = None,
                      limit: Optional[int] = None,  # ADDED
                      scan_index_forward: Optional[bool] = None  # ADDED
                      ) -> List[
        Dict[str, Any]]:
        """
        Queries items by date field/range. Converts Decimals.
        Can project specific attributes to optimize read.
        Accepts optional limit and scan_index_forward for the underlying query per day.
        """
        logger = getattr(self, 'logger', logging.getLogger(__name__))

        # Determine date_field and index_name
        effective_date_field = date_field
        effective_index_name = index_name

        if effective_date_field is None or effective_index_name is None:  # Check if either is None for inference
            if self.table_name == 'FBAdArchive':
                inferred_date_field = 'StartDate'
                inferred_index_name = 'StartDate-index'
            elif self.table_name == 'Pacer':
                inferred_date_field = 'FilingDate'
                # Querying Pacer by FilingDate usually means querying the base table if FilingDate is the PK
                # Or a GSI where FilingDate is the PK. Assuming base table here (PK: FilingDate, SK: DocketNum)
                inferred_index_name = None  # No specific GSI needed if FilingDate is PK of base table or a GSI itself
            elif self.table_name == 'PacerDockets':  # Added PacerDockets logic
                inferred_date_field = 'FilingDate'
                inferred_index_name = 'FilingDate-index'  # Use the GSI where FilingDate is the HASH key
            elif self.table_name == 'JPMLData':
                inferred_date_field = 'ReportDate'
                inferred_index_name = None
            elif self.table_name == 'DocketActivity':
                inferred_date_field = 'FilingDate'
                inferred_index_name = None
            else:
                logger.error(f"Cannot automatically determine date_field/index_name for table {self.table_name}.")
                raise ValueError(
                    f"Date field and index name must be specified for table {self.table_name} or inference added.")

            effective_date_field = effective_date_field or inferred_date_field
            # Important: only use inferred_index_name if effective_index_name was initially None.
            # If an explicit index_name='' (empty string) was passed to signify base table, respect that.
            if effective_index_name is None:  # only overwrite if it wasn't explicitly provided
                effective_index_name = inferred_index_name

            logger.debug(
                f"Using query parameters: date_field='{effective_date_field}', index_name='{effective_index_name}'")

        # Validate dates
        try:
            start_date_obj = datetime.strptime(start_date_str, '%Y%m%d').date()
            end_date_obj = start_date_obj if not end_date_str else datetime.strptime(end_date_str, '%Y%m%d').date()
        except ValueError as e:
            logger.error(
                f"Invalid date format: {e}. Dates must be in YYYYMMDD format. Got: '{start_date_str}', '{end_date_str}'")
            return []

        if start_date_obj > end_date_obj:
            logger.error(f"Start date {start_date_str} cannot be after end date {end_date_str}.")
            return []

        all_items_raw = []
        total_retrieved_for_logging = 0  # Use a different name to avoid confusion with len(all_items_raw)
        max_attempts_per_day = 5
        current_date_obj = start_date_obj

        # Progress bar setup
        use_progress = bool(self.console)  # Check if rich console is available
        # Use contextlib.nullcontext() if Progress is not to be used or fails to import
        progress_manager = contextlib.nullcontext()
        if use_progress:
            try:
                # Ensure rich progress components are available
                from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn, \
                    TimeRemainingColumn
                progress_manager = Progress(
                    SpinnerColumn(),
                    "[progress.description]{task.description}",
                    BarColumn(),
                    TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                    TextColumn("Days: {task.completed}/{task.total}"),
                    TimeElapsedColumn(),
                    TimeRemainingColumn(),
                    console=self.console,
                    transient=False  # Keep progress visible after completion
                )
            except ImportError:
                logger.warning("Rich progress components not available. Progress bar disabled for date query.")
                use_progress = False  # Disable if import fails

        with progress_manager as progress:
            task_id = None
            if use_progress:
                total_days = (end_date_obj - start_date_obj).days + 1
                task_id = progress.add_task(f"Querying {self.table.name}", total=total_days)

            while current_date_obj <= end_date_obj:
                formatted_date_key = current_date_obj.strftime('%Y%m%d')
                if task_id and progress:
                    progress.update(task_id, description=f"Querying {effective_date_field}: {formatted_date_key}")

                logger.info(f"Querying table '{self.table.name}' for {effective_date_field} = {formatted_date_key}"
                            f"{(' using index ' + effective_index_name) if effective_index_name else ''}"
                            f"{(' projecting: ' + projection_expression) if projection_expression else ''}")

                current_day_attempts = 0
                last_evaluated_key_for_day = None
                items_this_day = 0

                while current_day_attempts < max_attempts_per_day:
                    try:
                        query_args = {'KeyConditionExpression': Key(effective_date_field).eq(formatted_date_key)}
                        if effective_index_name:
                            query_args['IndexName'] = effective_index_name
                        if last_evaluated_key_for_day:
                            query_args['ExclusiveStartKey'] = last_evaluated_key_for_day
                        if projection_expression:
                            query_args['ProjectionExpression'] = projection_expression

                        # --- ADDED ---
                        if limit is not None:
                            query_args['Limit'] = limit
                        if scan_index_forward is not None:
                            query_args['ScanIndexForward'] = scan_index_forward
                        # --- END ADDED ---

                        response = self.table.query(**query_args)

                        items_on_page = response.get('Items', [])
                        all_items_raw.extend(items_on_page)
                        page_len = len(items_on_page)
                        items_this_day += page_len
                        total_retrieved_for_logging += page_len

                        last_evaluated_key_for_day = response.get('LastEvaluatedKey')
                        if not last_evaluated_key_for_day:
                            logger.debug(
                                f"Finished querying {formatted_date_key}. Fetched {items_this_day} items for this day.")
                            break  # Finished this day

                        # If a limit was applied and reached for the day, and no LastEvaluatedKey, we are done for the day.
                        # If a limit was applied and reached, AND there's a LastEvaluatedKey, it means more items exist for the day
                        # but we respected the per-day limit. The current logic correctly breaks the inner loop if limit is hit for the day.
                        # However, if the top-level `limit` parameter is meant to cap the *total items returned by query_by_date*,
                        # this per-day limit application would be incorrect.
                        # For now, this change makes `limit` apply per day.
                        if limit is not None and items_this_day >= limit:
                            logger.debug(
                                f"Per-day limit of {limit} reached for {formatted_date_key}. Fetched {items_this_day}.")
                            break  # Finished this day due to limit

                        logger.debug(f"Fetched {page_len} items for {formatted_date_key}, more pages exist...")
                        current_day_attempts = 0  # Reset attempts on successful page read with more data

                    except ClientError as e:
                        error_code = e.response['Error']['Code']
                        if error_code in ['ProvisionedThroughputExceededException', 'ThrottlingException']:
                            current_day_attempts += 1
                            if current_day_attempts >= max_attempts_per_day:
                                logger.error(
                                    f"Max retries ({max_attempts_per_day}) due to throttling while querying {formatted_date_key}. Aborting this day.")
                                break  # Break from attempts loop for this day

                            wait_time = (2 ** current_day_attempts) + random.uniform(0, 1)
                            logger.warning(
                                f"Throttled on {formatted_date_key}, attempt {current_day_attempts}/{max_attempts_per_day}. "
                                f"Retrying in {wait_time:.2f}s..."
                            )
                            time.sleep(wait_time)
                        elif error_code == 'ResourceNotFoundException':
                            logger.error(
                                f"DynamoDB resource not found: Table '{self.table.name}' or Index '{effective_index_name}' "
                                f"(for date_field '{effective_date_field}') may not exist. Query for {formatted_date_key} failed."
                            )
                            if task_id and progress: progress.stop()
                            return []  # Critical error, cannot continue
                        else:  # Other ClientErrors
                            logger.error(f"ClientError querying {formatted_date_key} on {effective_date_field}: {e}",
                                         exc_info=True)
                            # Depending on the error, you might break or continue. Here, we break attempts for this day.
                            if task_id and progress: progress.stop()  # Stop progress on unhandled client error for safety
                            return []  # Or handle more gracefully by skipping the day
                    except Exception as e:
                        logger.error(f"Unexpected error querying {formatted_date_key} on {effective_date_field}: {e}",
                                     exc_info=True)
                        if task_id and progress: progress.stop()  # Stop progress on unexpected error
                        return []  # Or handle more gracefully

                if current_day_attempts >= max_attempts_per_day and last_evaluated_key_for_day:
                    logger.error(
                        f"Failed to retrieve all items for {formatted_date_key} after {max_attempts_per_day} attempts due to persistent errors (e.g. throttling).")

                current_date_obj += timedelta(days=1)
                if task_id and progress:
                    progress.update(task_id, advance=1)

            # This is if a total limit across all days was intended for `query_by_date`
            # if limit is not None and len(all_items_raw) >= limit:
            #    logger.info(f"Overall limit of {limit} reached. Truncating results.")
            #    all_items_raw = all_items_raw[:limit]
            #    # Need to break the outer date loop too if overall limit is hit.
            #    # This part is not fully implemented as `limit` currently applies per day.

            if task_id and progress:
                progress.update(task_id, description=f"Query for {self.table.name} complete.")

        logger.info(f"Date range query complete. Total raw items fetched from DB: {total_retrieved_for_logging:,}")

        processed_items = [convert_decimals(item) for item in all_items_raw]

        logger.info(f"Returning {len(processed_items)} processed items.")
        return processed_items

    def add_gsi_to_local_table(self, gsi_definition: Dict[str, Any]) -> bool:
        """ Adds a Global Secondary Index to an existing local DynamoDB table. """
        # ... (Implementation remains the same, relies on self.dynamodb correctly) ...
        if not self.is_local or not self.table: logger.error("Not local or table not initialized."); return False
        gsi_name = gsi_definition['name']
        self.logger.info(f"Attempting add GSI '{gsi_name}' to local '{self.table_name}'...")
        try:
            table_desc = self.table.meta.client.describe_table(TableName=self.table_name)['Table']
            if any(
                    gsi['IndexName'] == gsi_name for gsi in
                    table_desc.get('GlobalSecondaryIndexes', [])): self.logger.info(
                f"GSI '{gsi_name}' already exists."); return True
            gsi_hash_key = gsi_definition['hash_key']
            gsi_range_key = gsi_definition.get('range_key')
            gsi_key_schema = [{'AttributeName': gsi_hash_key, 'KeyType': 'HASH'}]
            new_attr_defs = [{'AttributeName': gsi_hash_key, 'AttributeType': 'S'}]
            if gsi_range_key: gsi_key_schema.append(
                {'AttributeName': gsi_range_key, 'KeyType': 'RANGE'}); new_attr_defs.append(
                {'AttributeName': gsi_range_key, 'AttributeType': 'S'})
            existing_attr_defs = {ad['AttributeName']: ad for ad in table_desc.get('AttributeDefinitions', [])}
            for new_ad in new_attr_defs:
                if new_ad['AttributeName'] not in existing_attr_defs: existing_attr_defs[
                    new_ad['AttributeName']] = new_ad
            final_attribute_definitions = list(existing_attr_defs.values())
            gsi_update_action = {
                'Create': {'IndexName': gsi_name, 'KeySchema': gsi_key_schema, 'Projection': {'ProjectionType': 'ALL'},
                           'ProvisionedThroughput': {'ReadCapacityUnits': 10, 'WriteCapacityUnits': 10}}}
            self.logger.info(f"Initiating UpdateTable to add GSI '{gsi_name}'...")
            self.table.meta.client.update_table(TableName=self.table_name,
                                                AttributeDefinitions=final_attribute_definitions,
                                                GlobalSecondaryIndexUpdates=[gsi_update_action])
            self.logger.info(f"Waiting for GSI '{gsi_name}' on '{self.table_name}' to become active...")
            waiter = self.table.meta.client.get_waiter('table_exists')
            wait_config = {'Delay': 5, 'MaxAttempts': 24}
            start_wait = time.time()
            while True:
                waiter.wait(TableName=self.table_name, WaiterConfig=wait_config)
                current_desc = self.table.meta.client.describe_table(TableName=self.table_name)['Table']
                gsis = current_desc.get('GlobalSecondaryIndexes', [])
                gsi_status = next((g.get('IndexStatus') for g in gsis if g['IndexName'] == gsi_name), None)
                if gsi_status == 'ACTIVE':
                    self.logger.info(f"GSI '{gsi_name}' is now active.")
                    return True
                elif gsi_status == 'CREATING':
                    self.logger.info(f"GSI '{gsi_name}' still creating, waiting...")
                else:
                    self.logger.error(f"GSI '{gsi_name}' state: {gsi_status}. Addition failed.")
                    return False
                if time.time() - start_wait > wait_config['Delay'] * wait_config['MaxAttempts']:
                    self.logger.error(f"Timeout waiting for GSI '{gsi_name}'.")
                    return False
                time.sleep(wait_config['Delay'])
        except ClientError as e:
            self.logger.error(f"ClientError adding GSI '{gsi_name}': {e}", exc_info=True)
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error adding GSI '{gsi_name}': {e}", exc_info=True)
            return False

    def get_primary_key_schema(self) -> List[Dict[str, str]]:
        """Gets the primary key schema from the table description."""
        # ... (Implementation remains the same) ...
        try:
            return self.table.key_schema
        except Exception as e:
            self.logger.error(f"Could not get key schema for {self.table.name}: {e}", exc_info=True)
            if self.table_name in self.key_config: key_info = self.key_config[self.table_name]; return [
                {'AttributeName': name, 'KeyType': ('HASH' if i == 0 else 'RANGE')} for i, name in
                enumerate(key_info['key_attributes'])]
            raise ValueError(f"Cannot determine PK schema for {self.table.name}")

    def get_all_primary_keys(self, batch_size=1000) -> Generator[List[Dict[str, Any]], None, None]:
        """ Scans fetching only primary keys. Converts Decimals. Yields batches."""
        # ... (Implementation uses corrected _scan_segment_keys/scan_table) ...
        key_schema = self.get_primary_key_schema()
        key_names = [k['AttributeName'] for k in key_schema]
        projection_expression = ", ".join(key_names)
        self.logger.info(f"Scanning for PKs ({projection_expression}) from {self.table.name}...")
        total_keys = 0
        use_parallel_scan = not self.is_local
        if use_parallel_scan:
            from concurrent.futures import ThreadPoolExecutor
            num_workers = min(os.cpu_count() * 2, 16)
            self.logger.info(f"Using {num_workers} parallel workers for AWS key scan.")
            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                futures = [executor.submit(self._scan_segment_keys,
                                           {'ProjectionExpression': projection_expression, 'Segment': seg,
                                            'TotalSegments': num_workers}) for seg in range(num_workers)]
                batch = []
                for future in futures:
                    try:
                        # _scan_segment_keys yields sanitized key dicts
                        for key_item in future.result(): batch.append(key_item); total_keys += 1
                        if len(batch) >= batch_size: yield batch; batch = []
                    except Exception as e:
                        self.logger.error(f"Error processing parallel segment: {e}", exc_info=True)
                if batch: yield batch
        else:  # Normal Scan
            batch = []
            # scan_table yields sanitized dicts, we just need the keys
            for item_sanitized in self.scan_table(ProjectionExpression=projection_expression):
                key_item = {k: item_sanitized.get(k) for k in key_names}
                batch.append(key_item)
                total_keys += 1
                if len(batch) >= batch_size: yield batch; batch = []
            if batch: yield batch
        self.logger.info(f"Finished scanning PKs. Found {total_keys:,}.")

    def _scan_segment_keys(self, scan_kwargs: Dict) -> Generator[Dict[str, Any], None, None]:
        """Worker for scanning keys in a segment. Converts Decimals."""
        # ... (Implementation uses convert_decimals) ...
        last_evaluated_key = None
        key_names = [k.strip() for k in scan_kwargs['ProjectionExpression'].split(',')]
        while True:
            if last_evaluated_key: scan_kwargs['ExclusiveStartKey'] = last_evaluated_key
            attempt = 0
            max_retries = 5
            retry_delay = 1
            response = None
            while attempt < max_retries:
                try:
                    response = self.table.scan(**scan_kwargs)
                    break
                except ClientError as e:
                    if e.response['Error']['Code'] == 'ProvisionedThroughputExceededException':
                        attempt += 1
                        if attempt >= max_retries: self.logger.error(
                            f"Segment {scan_kwargs.get('Segment')} max retries."); raise e
                        wait_time = retry_delay * (2 ** attempt)
                        self.logger.warning(f"Segment {scan_kwargs.get('Segment')} throttled, retry {wait_time}s...")
                        time.sleep(wait_time)
                    else:
                        self.logger.error(f"Segment {scan_kwargs.get('Segment')} client error: {e}")
                        raise e
                except Exception as e_inner:
                    self.logger.error(f"Segment {scan_kwargs.get('Segment')} unexpected error: {e_inner}",
                                      exc_info=True)
                    raise e_inner
            for item_raw in response.get('Items', []):
                item_sanitized = convert_decimals(item_raw)
                yield {k: item_sanitized.get(k) for k in key_names}
            last_evaluated_key = response.get('LastEvaluatedKey')
            if not last_evaluated_key: break

    def batch_get_items(self, keys: List[Dict[str, Any]], batch_size: int = 100) -> Generator[
        Dict[str, Any], None, None]:
        """ Fetches items using BatchGetItem. Converts Decimals. Handles unprocessed keys."""
        # ... (Implementation uses convert_decimals) ...
        if not self.dynamodb or not self.table: raise RuntimeError("DynamoDB not ready.")
        if not keys: return
        batch_size = min(max(1, batch_size), 100)
        total_keys = len(keys)
        processed_count = 0
        yielded_count = 0
        keys_to_process = list(keys)
        use_progress = bool(self.console)
        maybe_progress = contextlib.nullcontext()
        if use_progress: maybe_progress = Progress(SpinnerColumn(), "[progress.description]{task.description}",
                                                   BarColumn(), "[progress.percentage]{task.percentage:>3.0f}%",
                                                   TextColumn("Keys Fetched: {task.completed:,}/{task.total:,}"),
                                                   TimeElapsedColumn(), console=self.console)
        with maybe_progress as progress:
            task_id = progress.add_task(f"Batch Get {self.table.name}", total=total_keys) if use_progress else None
            while keys_to_process:
                current_batch_keys = keys_to_process[:batch_size]
                request_items = {self.table.name: {'Keys': current_batch_keys, 'ConsistentRead': False}}
                attempt = 0
                max_retries = 5
                retry_delay = 1
                batch_processed = False
                while attempt < max_retries:
                    try:
                        response = self.dynamodb.batch_get_item(RequestItems=request_items)
                        fetched_items_raw = response.get('Responses', {}).get(self.table.name, [])
                        for item_raw in fetched_items_raw: yield convert_decimals(item_raw); yielded_count += 1
                        unprocessed = response.get('UnprocessedKeys', {}).get(self.table.name, {}).get('Keys', [])
                        if unprocessed:
                            self.logger.warning(f"{len(unprocessed)} keys unprocessed. Retrying.")
                            request_items = {self.table.name: {'Keys': unprocessed, 'ConsistentRead': False}}
                            wait = retry_delay * (2 ** attempt)
                            self.logger.info(f"Waiting {wait}s before retry...")
                            time.sleep(wait)
                            attempt += 1
                        else:
                            processed_count += len(current_batch_keys)
                            if task_id: progress.update(task_id, completed=processed_count)
                            del keys_to_process[:len(current_batch_keys)]
                            batch_processed = True
                            break
                    except ClientError as e:
                        if e.response['Error']['Code'] in ['ProvisionedThroughputExceededException',
                                                           'ThrottlingException']:
                            attempt += 1
                            if attempt >= max_retries:
                                self.logger.error(f"Max retries batch_get throttling.")
                                if task_id:
                                    progress.stop()
                                raise RuntimeError("Batch get failed: persistent throttling.") from e
                            wait = retry_delay * (2 ** attempt)
                            self.logger.warning(f"BatchGet throttled, attempt {attempt}. Retry {wait}s...")
                            time.sleep(wait)
                        else:
                            self.logger.error(f"ClientError batch_get: {e}", exc_info=True)
                            if task_id:
                                del keys_to_process[:len(current_batch_keys)]
                            raise
                    except Exception as e:
                        self.logger.error(f"Unexpected error batch_get: {e}", exc_info=True)
                        if task_id:
                            progress.stop()
                        del keys_to_process[:len(current_batch_keys)]
                        raise
                if not batch_processed and attempt == max_retries:
                    self.logger.error(f"Failed process batch ending {current_batch_keys[-1]} after max retries.")
                    del keys_to_process[:len(current_batch_keys)]


# --- Main block for testing ---
if __name__ == "__main__":
    print("Running DynamoDbBaseManager script directly for testing...")
    try:
        # Attempt to load config, provide defaults if it fails
        try:
            from src.lib.config import load_config
        except ImportError:
            print("Assuming config is in current directory or needs dummy values.")


            def load_config(date_str):
                return {'iso_date': '19700101', 'region_name': 'us-west-2'}  # Dummy config

        # Basic logging setup if not already done
        if not logging.root.handlers:
            logging.basicConfig(level=logging.DEBUG)
        logger = logging.getLogger("DynamoDBManagerTest")
        logger.setLevel(logging.DEBUG)  # Ensure debug messages show

        test_config = load_config('01/01/70')
        print(f"Test Config: {test_config}")

        # --- Test AWS Connection (Requires valid credentials/config) ---
        try:
            print("\n--- Testing PacerManager (AWS) ---")
            db_aws = DynamoDbBaseManager(test_config, 'Pacer',
                                         use_local=False)  # Use Base Class directly for simple test
            print(f"Connected to AWS table: {db_aws.table.name}")
            print(f"Primary Keys: PK={db_aws.pk_name}, SK={db_aws.sk_name}")
            # Example: Count items (may be slow/costly on large tables)
            # item_count = db_aws.count_table_items()
            # print(f"AWS Item Count: {item_count}")
            # Example: Get a single item (replace with actual keys)
            # single_item = db_aws.query_items({'FilingDate': 'YYYYMMDD', 'DocketNum': 'X:YY-cv-ZZZZZ'})
            # print(f"AWS Sample Item: {single_item}")
        except Exception as aws_e:
            print(f"AWS Test Failed: {aws_e}")
            logger.warning("AWS test failed, check credentials and table existence.", exc_info=True)

        # --- Test Local Connection ---
        try:
            print("\n--- Testing PacerManager (Local) ---")
            # Ensure Docker is running with DynamoDB local if testing this
            db_local = DynamoDbBaseManager(test_config, 'Pacer', use_local=True)  # Use Base Class
            print(f"Connected to local table: {db_local.table.name}")
            print(f"Primary Keys: PK={db_local.pk_name}, SK={db_local.sk_name}")
            # Add a test item
            test_record = {'FilingDate': '20240101', 'DocketNum': '1:24-local-00001', 'Title': 'Local Test Case',
                           'SomeNumber': 123.45}
            print(f"Adding test record: {test_record}")
            # Use base class batch method (simpler test than PacerManager's override)
            suc, fail = db_local.batch_insert_items([test_record], disable_progress=True)
            print(f"Local Batch Add Result: Success={suc}, Fail={fail}")
            # Fetch the item back
            fetched_raw = db_local.table.get_item(Key={'FilingDate': '20240101', 'DocketNum': '1:24-local-00001'}).get(
                'Item')
            print(f"Fetched Raw from local: {fetched_raw}")
            fetched_processed = convert_decimals(fetched_raw) if fetched_raw else None
            print(f"Fetched Processed from local: {fetched_processed}")
            # Test query_items
            fetched_query = db_local.query_items(
                key_conditions={'FilingDate': '20240101', 'DocketNum': '1:24-local-00001'})
            print(f"Fetched via query_items: {fetched_query}")

        except Exception as local_e:
            print(f"Local Test Failed: {local_e}")
            logger.warning("Local test failed. Is Docker running with DynamoDB-Local?", exc_info=True)
        finally:
            # Stop the local container if it was started
            if 'db_local' in locals() and db_local._local_dynamodb_manager:
                print("Stopping local DynamoDB container...")
                db_local._local_dynamodb_manager.stop_dynamodb()

    except Exception as main_e:
        print(f"An error occurred during __main__ test setup or execution: {main_e}")
        logging.exception("Error in __main__")

    print("\nDynamoDbBaseManager script finished.")

# TODO: Check which classes calling pacer or fb or other items use overwrite=True/
