# orchestrator.py (Continuing the existing file)

import asyncio
import json
import logging
import os
import re
import shutil
import time
import uuid
from collections import OrderedDict
from datetime import datetime, timedelta, date as DateType
from pathlib import Path
from pprint import pformat
from typing import List, Optional, Tuple, Any, Dict, Set, NamedTuple

import portalocker
from playwright.async_api import (
    <PERSON>rowserContext,
    Page,
    TimeoutError as PlaywrightTimeoutError,
    Error as PlaywrightError, Locator
)

from main import DISTRICT_COURTS_PATH
from .authenticator import PacerAuthenticator  # Added explicit import
from .browser_service import BrowserService
from .case_relevance_engine import CaseRelevanceEngine
from .docket_processor import DocketProcessor
from .file_manager import PacerFileManager
from .navigator import PacerNavigator
from .pacer_utils import parse_date_str, format_date_mmddyy, generate_filing_dates_iso
from .report_handler import ReportHandler
from ..district_courts_manager import DistrictCourtsManager
from ..gpt4_interface import GPT4
from ..pacer_manager import Pacer<PERSON>anager
from ..s3_manager import S3Manager

# --- Constants ---
REMOVAL_KEYWORD = 'removal'


# District courts path and project root will be determined from config
# Relevant defendants path will be constructed based on project root from config
# Data path will be constructed based on project root from config

def get_default_data_path(config):
    """Utility function to get data_path from config or construct it from project_root."""
    project_root = config.get('project_root')
    if not project_root:
        project_root = config.get('directories', {}).get('base_dir')
        if not project_root:
            project_root = os.getcwd()
    return os.path.join(project_root, 'data')


def calculate_default_start_date(end_date: DateType) -> DateType:
    """Calculates the default start date based on the end date's weekday."""
    weekday = end_date.weekday()
    if weekday == 0:  # Monday
        return end_date - timedelta(days=3)  # Previous Friday
    elif weekday == 6:  # Sunday
        return end_date - timedelta(days=2)  # Previous Friday
    elif weekday == 5:  # Saturday
        return end_date - timedelta(days=1)  # Previous Friday
    else:  # Tuesday - Friday
        return end_date - timedelta(days=1)  # Previous day


def _setup_court_file_logger(logger_name: str, log_file_path: Path, level: int = logging.DEBUG) -> logging.Logger:
    """Sets up a file logger that doesn't propagate and has a specific format."""
    court_logger = logging.getLogger(logger_name)
    court_logger.setLevel(level)
    court_logger.propagate = False  # Prevent messages from going to console via root logger

    # Remove existing handlers to prevent duplication if a task retries or logger is re-configured
    for handler in list(court_logger.handlers):  # Iterate over a copy
        court_logger.removeHandler(handler)
        handler.close()

    # Ensure the directory for the log file exists
    log_file_path.parent.mkdir(parents=True, exist_ok=True)

    fh = logging.FileHandler(log_file_path)
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s:%(lineno)d - %(message)s')
    fh.setFormatter(formatter)
    court_logger.addHandler(fh)
    return court_logger


class LoggingContext:
    """Adds temporary context to log messages (basic implementation)."""

    def __init__(self, logger, extra):
        self.logger = logger
        self.extra = extra

    def __enter__(self): pass

    def __exit__(self, exc_type, exc_val, exc_tb): pass


# --- Helper Named Tuples for Clarity ---
class RowElements(NamedTuple):
    docket_link_loc: Locator
    versus_loc: Locator
    date_cell_loc: Locator
    cause: Optional[str] = None
    nos: Optional[str] = None
    case_flags: Optional[str] = None


class ExistenceCheckResult(NamedTuple):
    should_skip: bool
    reason: Optional[str]
    base_filename: Optional[str]


class DateCheckResult(NamedTuple):  # THIS IS THE DEFINITION TO ENSURE IS CORRECT
    is_in_range: bool
    filing_date_obj: Optional[DateType]  # Ensure this field is present
    filing_date_for_processor: Optional[str]


# --- Orchestrator Class ---

class PacerOrchestrator:
    """Orchestrates the PACER scraping process across multiple courts or dockets."""

    # Configuration defaults
    DEFAULT_MAX_WORKERS = 6
    DEFAULT_RETRIES_PER_COURT = 3
    DEFAULT_DELAY_S = 1.5
    DEFAULT_TIMEOUT_S = 30
    HISTORICAL_IRRELEVANT_DAYS_TO_CHECK = 5

    # --- [ __init__, _get_date_range, _setup_directories, process_courts, etc. ] ---
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.PacerOrchestrator")

        # ---- Dependency Initialization ----
        required_keys = ['username_prod', 'password_prod', 'bucket_name', 'openai_api_key']
        for key in required_keys:
            if key not in config or not config[key]:
                self.logger.warning(
                    f"Configuration missing essential key: '{key}'. Using potentially insecure defaults or expect errors.")

        self._load_relevance_dependencies()

        self.browser_service = BrowserService(
            headless=config.get('headless', True),
            timeout_ms=config.get('timeout', self.DEFAULT_TIMEOUT_S) * 1000
        )

        try:
            self.pacer_db = PacerManager(config, use_local=False)
        except Exception as e:
            self.logger.error(f"Failed to initialize PacerManager: {e}", exc_info=True)
            raise RuntimeError("PacerManager initialization failed") from e
        try:
            self.dc_db = DistrictCourtsManager(config, use_local=False)
        except Exception as e:
            self.logger.error(f"Failed to initialize DistrictCourtsManager: {e}", exc_info=True)
            raise RuntimeError("DistrictCourtsManager initialization failed") from e
        try:
            bucket = config.get('bucket_name')
            if not bucket: raise ValueError("S3 bucket_name missing in configuration")
            self.s3_manager = S3Manager(config, bucket)
        except Exception as e:
            self.logger.error(f"Failed to initialize S3Manager: {e}", exc_info=True)
            raise RuntimeError("S3Manager initialization failed") from e
        try:
            if not config.get('openai_api_key'): raise ValueError("OpenAI API key missing in configuration")
            self.gpt_interface = GPT4(config)
        except Exception as e:
            self.logger.error(f"Failed to initialize GPT4 interface: {e}", exc_info=True)
            raise RuntimeError("GPT4 interface initialization failed") from e

        # Get data path from config or construct from project_root
        project_root = config.get('project_root')
        if not project_root:
            project_root = config.get('directories', {}).get('base_dir')
            if not project_root:
                project_root = os.getcwd()
                self.logger.warning(f"Using current directory as project root: {project_root}")

        default_data_path = os.path.join(project_root, 'data')
        self.file_manager = PacerFileManager(base_data_dir=config.get('data_path', default_data_path))

        # ---- State Variables ----
        # REMOVED: self.processed_courts_log: Dict[str, Dict[str, int]] = {}
        self.overall_start_time = time.monotonic()
        self.historically_irrelevant_dockets: Set[Tuple[str, str]] = set()

    @staticmethod
    async def _handle_flnd_client_code_page(navigator: PacerNavigator, court_id: str,
                                            court_logger: logging.Logger) -> bool:
        """
        Handles the specific "Client Code" page for FLND court after ECF login.
        If the page is detected, it fills "007" into the client code field and
        attempts to click the "Submit" button.
        """
        log_prefix = f"[{court_id}] FLNDClientCode:"
        court_logger.info(f"{log_prefix} Checking for FLND client code page. Current URL: {navigator.page.url}")

        client_code_input_selector = "input#logoutForm\\:clientCode"  # Escaped colon
        client_code_submit_button_selector = "button#logoutForm\\:btnChangeClientCode"  # Escaped colon

        try:
            # Check for the input field first as a stronger indicator of the page
            client_code_input_loc_collection = await navigator.locator(client_code_input_selector)

            if await client_code_input_loc_collection.count() == 0:
                court_logger.info(
                    f"{log_prefix} Client code input field ('{client_code_input_selector}') not found. Assuming not on client code page.")
                return True  # Not an error, just not the page we're looking for

            client_code_input_loc = client_code_input_loc_collection.first
            if not await client_code_input_loc.is_visible(timeout=5000):
                court_logger.info(
                    f"{log_prefix} Client code input field found but not visible. Assuming not the active form.")
                return True

            court_logger.info(f"{log_prefix} FLND client code input field is visible. Filling with '007'.")
            await client_code_input_loc.fill("007", timeout=5000)

            # Now find and click the submit button
            submit_button_loc_collection = await navigator.locator(client_code_submit_button_selector)
            if await submit_button_loc_collection.count() == 0:
                court_logger.error(
                    f"{log_prefix} Client code submit button ('{client_code_submit_button_selector}') not found after filling input. This is unexpected.")
                await navigator.save_screenshot("flnd_client_code_submit_btn_missing_after_fill")
                return False

            submit_button_loc = submit_button_loc_collection.first
            if not await submit_button_loc.is_visible(timeout=5000):
                court_logger.error(f"{log_prefix} Client code submit button found but not visible after filling input.")
                await navigator.save_screenshot("flnd_client_code_submit_btn_not_visible_after_fill")
                return False

            court_logger.info(f"{log_prefix} FLND client code submit button is visible. Attempting to click.")

            await submit_button_loc.click(timeout=10000)
            court_logger.info(f"{log_prefix} Clicked FLND client code submit button.")

            await navigator.page.wait_for_timeout(3000)

            # Re-check existence and visibility of the submit button
            if await submit_button_loc_collection.count() > 0:
                # If count > 0, it means an element matching the selector is still in the DOM.
                # Now check if that first (and presumably only) element is visible.
                try:
                    if await submit_button_loc_collection.first.is_visible(timeout=1000):
                        court_logger.warning(
                            f"{log_prefix} FLND client code submit button still visible after click and delay. Proceeding cautiously.")
                    else:
                        court_logger.info(
                            f"{log_prefix} FLND client code submit button is in DOM but no longer visible after click.")
                except PlaywrightTimeoutError:  # is_visible can timeout if element becomes non-visible quickly
                    court_logger.info(
                        f"{log_prefix} FLND client code submit button became non-visible (timeout check).")
            else:
                court_logger.info(
                    f"{log_prefix} FLND client code submit button no longer found in DOM (count is 0) after click.")

            court_logger.info(
                f"{log_prefix} Successfully handled FLND client code page. New URL (if changed): {navigator.page.url}")
            return True

        except PlaywrightTimeoutError as pte:
            court_logger.warning(f"{log_prefix} Timeout interacting with FLND client code page elements: {pte}",
                                 exc_info=False)
            await navigator.save_screenshot("flnd_client_code_timeout_error")
            return True  # Allow continuation, might not be on this page
        except PlaywrightError as pe:
            if "is not a valid selector" in str(pe):
                court_logger.info(
                    f"{log_prefix} Encountered invalid selector error, likely not on the client code page or structure changed: {pe}")
                return True
            court_logger.error(f"{log_prefix} PlaywrightError handling FLND client code page: {pe}", exc_info=True)
            await navigator.save_screenshot("flnd_client_code_playwright_error")
            return False
        except Exception as e:
            court_logger.error(f"{log_prefix} Unexpected error handling FLND client code page: {e}", exc_info=True)
            await navigator.save_screenshot("flnd_client_code_unexpected_error")
            return False

    async def _load_historical_irrelevant_cases(self, current_iso_date_for_run_str: str):
        """
        Loads historically irrelevant cases from JSON files from previous days.
        Populates self.historically_irrelevant_dockets.
        """
        self.logger.info(
            f"Loading historically irrelevant dockets relative to run date: {current_iso_date_for_run_str}")
        self.historically_irrelevant_dockets.clear()  # Clear for current run
        loaded_count = 0

        try:
            current_run_date_obj = datetime.strptime(current_iso_date_for_run_str, '%Y%m%d').date()
        except ValueError:
            self.logger.error(
                f"Invalid current_iso_date_for_run_str: {current_iso_date_for_run_str}. Cannot load historical irrelevant cases.")
            return

        # Use utility function to get data_path with fallback
        default_data_path = get_default_data_path(self.config)
        base_data_path = Path(self.config.get('data_path', default_data_path))

        for i in range(1, self.HISTORICAL_IRRELEVANT_DAYS_TO_CHECK + 1):
            prev_date_obj = current_run_date_obj - timedelta(days=i)
            prev_iso_date_str = prev_date_obj.strftime('%Y%m%d')
            file_path = base_data_path / prev_iso_date_str / "logs" / "irrelevant_cases_all.json"

            if await asyncio.to_thread(file_path.exists):
                try:
                    self.logger.debug(f"Attempting to load historical irrelevant cases from: {file_path}")

                    # Perform both file reading and JSON parsing in a separate thread
                    def _read_and_parse_json(path: Path) -> Any:
                        with open(path, 'r') as f_content:
                            return json.load(f_content)

                    data = await asyncio.to_thread(_read_and_parse_json, file_path)

                    if isinstance(data, list):
                        for item in data:
                            if isinstance(item, dict):
                                court_id = item.get('court_id')
                                docket_num = item.get('docket_num')
                                if isinstance(court_id, str) and isinstance(docket_num,
                                                                            str) and court_id and docket_num:
                                    if (court_id, docket_num) not in self.historically_irrelevant_dockets:
                                        self.historically_irrelevant_dockets.add((court_id, docket_num))
                                        loaded_count += 1
                                else:
                                    self.logger.warning(
                                        f"Invalid item format in {file_path}: {item}. Missing/invalid court_id or docket_num.")
                            else:
                                self.logger.warning(f"Non-dict item found in {file_path}: {item}")
                    else:
                        self.logger.warning(f"Expected a list in {file_path}, but got {type(data)}.")
                except json.JSONDecodeError as e:
                    self.logger.error(f"Error decoding JSON from {file_path}: {e}")
                except Exception as e:
                    self.logger.error(f"Unexpected error loading or processing {file_path}: {e}", exc_info=True)
            else:
                self.logger.debug(f"Historical irrelevant cases file not found: {file_path}")

        self.logger.info(
            f"Loaded {loaded_count} new unique historically irrelevant dockets from the past {self.HISTORICAL_IRRELEVANT_DAYS_TO_CHECK} days. Total: {len(self.historically_irrelevant_dockets)}")

    def _get_date_range(self, start_date_str: Optional[str], end_date_str: Optional[str]) -> Tuple[DateType, DateType]:
        """Determines and validates the start and end dates for the run."""
        try:
            end_date = parse_date_str(end_date_str) if end_date_str else datetime.now().date()
            start_date = parse_date_str(start_date_str) if start_date_str else calculate_default_start_date(end_date)
            if start_date > end_date:
                raise ValueError(f"Start date ({start_date}) cannot be after end date ({end_date}).")
            self.logger.info(
                f"Processing date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            return start_date, end_date
        except ValueError as e:
            self.logger.error(f"Invalid date configuration: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error in _get_date_range: {e}", exc_info=True)
            raise

    def _load_relevance_dependencies(self):
        """Loads relevance configuration and dependencies needed for CaseRelevanceEngine."""
        # Ensure self.relevance_config is initialized even if file loading fails later
        self.relevance_config = {}

        # Get project_root from config
        project_root = self.config.get('project_root')
        if not project_root:
            # Fall back to directories.base_dir
            project_root = self.config.get('directories', {}).get('base_dir')
            if not project_root:
                self.logger.warning("No project_root found in config, using current directory")
                project_root = os.getcwd()

        config_dir = Path(project_root) / 'src' / 'config' / 'pacer'
        relevance_config_path = config_dir / 'relevance_config.json'

        try:
            if relevance_config_path.exists():
                with open(relevance_config_path, 'r') as f:
                    self.relevance_config = json.load(f)
                self.logger.info(f"Successfully loaded relevance configuration from {relevance_config_path}")
            else:
                self.logger.warning(f"Relevance config file not found at: {relevance_config_path}. Using empty config.")
        except Exception as e:
            self.logger.error(f"Error loading relevance config: {e}", exc_info=True)
            self.relevance_config = {}  # Ensure it's an empty dict on error

        # Load relevant defendants
        self.relevant_defendants_lower: List[str] = []  # Initialize attribute

        # Get project_root from config - we already loaded it above
        project_root = self.config.get('project_root')
        if not project_root:
            # Fall back to directories.base_dir
            project_root = self.config.get('directories', {}).get('base_dir')
            if not project_root:
                self.logger.warning("No project_root found in config, using current directory")
                project_root = os.getcwd()

        # Use project_root to construct the path
        actual_relevant_defendants_path = Path(
            project_root) / 'src' / 'config' / 'defendants' / 'relevant_defendants.json'
        self.logger.info(f"Orchestrator trying relevant_defendants_path: {actual_relevant_defendants_path}")

        try:
            if actual_relevant_defendants_path.exists():  # Check the modified path
                with open(actual_relevant_defendants_path, 'r') as f:  # Use modified path
                    data = json.load(f)
                    # Ensure 'defendants' key exists and is a list
                    defendants_data = data.get("defendants", [])
                    if not isinstance(defendants_data, list):
                        self.logger.error(
                            f"Invalid format in relevant defendants file: 'defendants' is not a list or missing.")
                    else:
                        self.relevant_defendants_lower = [str(d).lower() for d in defendants_data if
                                                          isinstance(d, str) and d]
                        self.logger.info(
                            f"Orchestrator loaded {len(self.relevant_defendants_lower)} relevant defendants keywords from {actual_relevant_defendants_path}.")
            else:
                self.logger.warning(f"Relevant defendants file not found at: {actual_relevant_defendants_path}")
        except Exception as e:
            self.logger.error(f"Error loading relevant defendants from {actual_relevant_defendants_path}: {e}",
                              exc_info=True)
            self.relevant_defendants_lower = []  # Ensure it's an empty list on error

        # Compile USA defendant regex (rest of this method is likely fine)
        default_no_match_regex = r'(?!)'
        usa_defendant_regex_str = default_no_match_regex

        try:
            usa_defendant_regex_str = self.relevance_config.get('usa_defendant_regex', default_no_match_regex)
            if not isinstance(usa_defendant_regex_str, str) or not usa_defendant_regex_str:
                self.logger.warning(
                    f"USA defendant regex pattern is missing or invalid in config. Using default 'match nothing' pattern.")
                usa_defendant_regex_str = default_no_match_regex

            self.usa_defendant_regex_compiled = re.compile(usa_defendant_regex_str, re.IGNORECASE)
            self.logger.info(f"Successfully compiled USA defendant regex pattern: '{usa_defendant_regex_str}'")
        except re.error as e:
            self.logger.error(
                f"Failed to compile USA defendant regex '{usa_defendant_regex_str}': {e}. Using 'match nothing' fallback. Relevance checks may be affected.")
            self.usa_defendant_regex_compiled = re.compile(default_no_match_regex)
        except TypeError as e:
            self.logger.error(
                f"TypeError during USA defendant regex compilation (pattern was '{usa_defendant_regex_str}'): {e}. Using 'match nothing' fallback.")
            self.usa_defendant_regex_compiled = re.compile(default_no_match_regex)

    def _setup_directories(self, iso_date: str):
        """Creates essential directories for the run date idempotently."""
        # Use utility function to get data_path with fallback
        default_data_path = get_default_data_path(self.config)
        base_path = Path(self.config.get('data_path', default_data_path))
        date_dir = base_path / iso_date
        subdirs = ['dockets', 'logs', 'html', 'screenshots']
        try:
            for subdir in subdirs:
                (date_dir / subdir).mkdir(parents=True, exist_ok=True)
            (date_dir / 'dockets' / 'temp').mkdir(parents=True, exist_ok=True)
            (date_dir / 'dockets' / '.locks').mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Ensured base directories exist under: {date_dir}")
        except Exception as e:
            self.logger.error(f"Error creating directories under {date_dir}: {e}", exc_info=True)
            raise RuntimeError(f"Failed to setup required directories for date {iso_date}") from e

    async def _perform_ecf_login_sequence(self, navigator: PacerNavigator, court_id: str,
                                          authenticator: PacerAuthenticator, court_logger: logging.Logger) -> bool:
        """
        Performs the full login sequence:
        1. Logs into main PACER.
        2. Navigates to the specified court's ECF.
        3. Handles any court-specific UI elements (e.g., popups).
        4. Logs into the court's ECF system.
        5. Handles FLND-specific client code page.
        Returns True if successful, False otherwise. The navigator will be on the court's ECF main page.
        """
        log_prefix = f"[{court_id}] ECFLoginSeq:"
        try:
            court_logger.info(f"{log_prefix} Starting ECF login sequence.")

            # PacerAuthenticator should ideally use the court_logger if its actions are court-specific.
            # Assuming PacerAuthenticator's constructor was updated to accept and use a logger.
            if not await authenticator.login():  # authenticator.login() will use its own configured logger
                court_logger.error(f"{log_prefix} Main PACER login failed.")
                return False
            court_logger.info(f"{log_prefix} Main PACER login successful.")

            if not await self._navigate_to_court_ecf(navigator, court_id, court_logger):
                court_logger.error(f"{log_prefix} Navigation to court ECF page failed.")
                return False
            court_logger.info(f"{log_prefix} Successfully navigated to court's ECF portal page.")

            await self._handle_court_specific_ui(navigator, court_id, court_logger)
            court_logger.info(f"{log_prefix} Court-specific UI handled (if any).")

            if not await self._login_to_court_ecf(navigator, court_id, court_logger):
                court_logger.error(f"{log_prefix} Court ECF login failed.")
                return False
            court_logger.info(
                f"{log_prefix} Court ECF login successful. Navigator is on court's ECF main page (or client code page for FLND).")

            # FLND-specific client code page handling
            if court_id.lower() == 'flnd':
                court_logger.info(f"{log_prefix} Court is FLND, attempting to handle client code page.")
                if not await self._handle_flnd_client_code_page(navigator, court_id, court_logger):
                    court_logger.error(f"{log_prefix} FLND client code page handling failed.")
                    return False
                court_logger.info(f"{log_prefix} FLND client code page handled successfully.")

            court_logger.info(
                f"{log_prefix} Full ECF login sequence successful. Navigator should be on court's ECF main content page.")
            return True

        except Exception as e:
            court_logger.error(f"{log_prefix} Exception during ECF login sequence: {e}", exc_info=True)
            if navigator.is_ready:
                await navigator.save_screenshot("ecf_login_sequence_exception")
            return False

    async def _fill_docket_num_and_trigger_find(self, main_navigator: PacerNavigator, docket_num: str,
                                                log_prefix: str, court_logger: logging.Logger) -> bool:
        """Fills the docket number, focuses another field, and clicks 'Find This Case'."""
        page = main_navigator.page
        try:
            # 1. Enter Docket Number
            docket_input_selector = "#case_number_text_area_0"
            court_logger.debug(f"{log_prefix} Trying docket input selector: {docket_input_selector}")
            input_loc_collection = await main_navigator.locator(docket_input_selector)
            if await input_loc_collection.count() == 0:
                court_logger.error(f"{log_prefix} Docket input selector '{docket_input_selector}' found no elements.")
                await main_navigator.save_screenshot("query_input_not_found")
                return False
            input_first_loc = input_loc_collection.first

            await input_first_loc.wait_for(state='visible', timeout=7000)
            court_logger.info(f"{log_prefix} Docket input field '{docket_input_selector}' is visible.")
            await input_first_loc.focus(timeout=3000)
            await input_first_loc.fill(docket_num, timeout=10000)
            filled_value = await input_first_loc.input_value(timeout=2000)
            if filled_value != docket_num:
                court_logger.error(
                    f"{log_prefix} Fill completed, but input value ('{filled_value}') does not match expected ('{docket_num}').")
                await main_navigator.save_screenshot("query_fill_critical_value_mismatch")
                return False
            court_logger.info(f"{log_prefix} Successfully filled docket number '{docket_num}'.")
            await input_first_loc.dispatch_event('input', timeout=1000)
            await input_first_loc.dispatch_event('change', timeout=1000)

            # Focus on another field
            filed_date_from_selector = "input[name='Qry_filed_from']"
            court_logger.debug(f"{log_prefix} Attempting to focus on '{filed_date_from_selector}'.")
            date_from_loc_collection = await main_navigator.locator(filed_date_from_selector)
            if await date_from_loc_collection.count() > 0:
                await date_from_loc_collection.first.focus(timeout=3000)
                court_logger.info(f"{log_prefix} Successfully focused on '{filed_date_from_selector}'.")
            else:
                court_logger.warning(
                    f"{log_prefix} Could not find '{filed_date_from_selector}' to focus. Clicking body.")
                await page.locator('body').first.click(timeout=2000)
            await page.wait_for_timeout(self.config.get('docket_blur_reaction_delay_ms', 2000))

            # 2. Click "Find This Case" button
            find_this_case_selector = "input#case_number_find_button_0[value='Find This Case']"
            court_logger.info(f"{log_prefix} Looking for '{find_this_case_selector}' button.")
            find_button_loc_collection = await main_navigator.locator(find_this_case_selector)

            try:  # Check attachment first
                await find_button_loc_collection.first.wait_for(state='attached', timeout=5000)
            except PlaywrightTimeoutError:
                court_logger.error(f"{log_prefix} '{find_this_case_selector}' button NOT ATTACHED.")
                await main_navigator.save_screenshot("find_case_button_not_attached")
                return False
            if await find_button_loc_collection.count() == 0:  # Should not happen if attached worked
                court_logger.error(f"{log_prefix} '{find_this_case_selector}' button NOT FOUND (after attach check).")
                await main_navigator.save_screenshot("find_case_button_not_found_post_attach")
                return False

            find_button_first_loc = find_button_loc_collection.first
            await find_button_first_loc.wait_for(state='visible', timeout=7000)
            court_logger.info(f"{log_prefix} '{find_this_case_selector}' is visible. Clicking.")
            await find_button_first_loc.click(timeout=10000)
            await page.wait_for_timeout(self.config.get('find_case_button_delay_ms', 3000))
            court_logger.info(f"{log_prefix} After 'Find This Case' click. URL: {page.url}")

            error_message_area_selector = "#case_number_message_area_0"
            error_area_loc_collection = await main_navigator.locator(error_message_area_selector)
            if await error_area_loc_collection.count() > 0:
                error_area_loc = error_area_loc_collection.first
                if await error_area_loc.is_visible(timeout=1000):
                    error_text = await error_area_loc.text_content(timeout=1000)
                    if error_text and ("not found" in error_text.lower() or "invalid" in error_text.lower()):
                        court_logger.error(f"{log_prefix} Error message after 'Find This Case': '{error_text.strip()}'")
                        await main_navigator.save_screenshot("find_case_returns_error")
                        return False
                    elif error_text:
                        court_logger.warning(f"{log_prefix} Message after 'Find This Case': '{error_text.strip()}'")
            return True
        except PlaywrightError as e:
            court_logger.error(f"{log_prefix} PlaywrightError during fill/find case: {e}", exc_info=True)
            await main_navigator.save_screenshot("fill_find_case_playwright_error")
            return False

    @staticmethod
    async def _click_run_query_and_navigate_on_query_page(main_navigator: PacerNavigator,
                                                          log_prefix: str, court_logger: logging.Logger) -> bool:
        """Clicks the 'Run Query' button and expects navigation."""
        page = main_navigator.page
        try:
            run_query_button_selector = "input[name='button1'][value='Run Query']"
            court_logger.info(f"{log_prefix} Looking for 'Run Query' button: {run_query_button_selector}")
            run_query_button_loc_collection = await main_navigator.locator(run_query_button_selector)

            try:  # Check attachment first
                await run_query_button_loc_collection.first.wait_for(state='attached', timeout=5000)
            except PlaywrightTimeoutError:
                court_logger.error(f"{log_prefix} 'Run Query' button ('{run_query_button_selector}') NOT ATTACHED.")
                await main_navigator.save_screenshot("run_query_button_not_attached")
                return False
            if await run_query_button_loc_collection.count() == 0:  # Should not happen if attached worked
                court_logger.error(
                    f"{log_prefix} 'Run Query' button ('{run_query_button_selector}') NOT FOUND (after attach check).")
                await main_navigator.save_screenshot("run_query_button_not_found_post_attach")
                return False

            run_query_first_loc = run_query_button_loc_collection.first
            await run_query_first_loc.wait_for(state='visible', timeout=5000)
            court_logger.info(f"{log_prefix} 'Run Query' button is visible. Clicking.")
            current_url_before_click = page.url
            async with page.expect_navigation(wait_until="domcontentloaded", timeout=main_navigator.timeout + 20000):
                await run_query_first_loc.click(timeout=15000)

            if page.url == current_url_before_click and not page.is_closed():
                court_logger.warning(f"{log_prefix} URL did not change after 'Run Query'. Unexpected. URL: {page.url}")
                await main_navigator.save_screenshot("run_query_no_navigation")
                # Check for error messages if navigation failed
                error_message_area_selector_rq = "#case_number_message_area_0"
                error_area_loc_collection_rq = await main_navigator.locator(error_message_area_selector_rq)
                if await error_area_loc_collection_rq.count() > 0:
                    error_area_loc_rq = error_area_loc_collection_rq.first
                    if await error_area_loc_rq.is_visible(timeout=1000):
                        error_text_rq = await error_area_loc_rq.text_content(timeout=1000)
                        if error_text_rq:
                            court_logger.error(
                                f"{log_prefix} Message in error area when Run Query failed to navigate: '{error_text_rq.strip()}'")
                return False
            court_logger.info(f"{log_prefix} Successfully clicked 'Run Query'. Landed on: {page.url}")
            return True
        except PlaywrightTimeoutError as e_timeout_run_query:
            court_logger.error(f"{log_prefix} Timeout related to 'Run Query' button: {e_timeout_run_query}",
                               exc_info=False)
            error_message_area_selector_rq = "#case_number_message_area_0"
            error_area_loc_collection_rq = await main_navigator.locator(error_message_area_selector_rq)
            if await error_area_loc_collection_rq.count() > 0:
                error_area_loc_rq = error_area_loc_collection_rq.first
                if await error_area_loc_rq.is_visible(timeout=1000):
                    error_text_rq = await error_area_loc_rq.text_content(timeout=1000)
                    if error_text_rq:
                        court_logger.error(
                            f"{log_prefix} Message in error area when Run Query timed out/was disabled: '{error_text_rq.strip()}'")
            await main_navigator.save_screenshot("run_query_button_timeout_or_not_enabled")
            return False
        except PlaywrightError as e:
            court_logger.error(f"{log_prefix} PlaywrightError clicking 'Run Query': {e}", exc_info=True)
            await main_navigator.save_screenshot("run_query_click_playwright_error")
            return False

    @staticmethod
    async def _click_docket_report_link_on_intermediate_page(main_navigator: PacerNavigator,
                                                             context: BrowserContext, log_prefix: str,
                                                             court_logger: logging.Logger) -> Optional[
        Page]:
        """Clicks the 'Docket Report' link on the intermediate page, returning the new page."""
        page = main_navigator.page  # This is the intermediate page
        docket_report_link_selectors = [
            "a:text-matches('/Docket Report/i')",
            "a:has-text('Docket Report')",
            "//a[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'docket report')]"
        ]
        court_logger.info(f"{log_prefix} Looking for 'Docket Report' link on page: {page.url}")

        docket_report_first_link_loc: Optional[Locator] = None
        found_dr_link = False

        for sel_idx, dr_sel in enumerate(docket_report_link_selectors):
            try:
                court_logger.debug(f"{log_prefix} Trying Docket Report link selector #{sel_idx + 1}: {dr_sel}")
                dr_loc_collection = await main_navigator.locator(dr_sel)  # Use main_navigator as it's on current page
                if await dr_loc_collection.count() == 0: continue
                temp_loc = dr_loc_collection.first
                await temp_loc.wait_for(state='attached', timeout=5000)
                if await temp_loc.is_visible(timeout=10000):
                    docket_report_first_link_loc = temp_loc
                    found_dr_link = True
                    court_logger.info(f"{log_prefix} Found 'Docket Report' link with selector: {dr_sel}")
                    break
            except PlaywrightError:
                court_logger.debug(f"{log_prefix} Docket Report link selector '{dr_sel}' not found/visible.")

        if not found_dr_link or not docket_report_first_link_loc:
            court_logger.error(f"{log_prefix} 'Docket Report' link not found/visible on intermediate page: {page.url}")
            # Check for common error messages on this page
            no_case_found_loc = page.locator("text=/Case with that number not found in this court|Case not found/i")
            invalid_format_loc = page.locator("text=/Invalid case number format/i")
            if await no_case_found_loc.count() > 0 and await no_case_found_loc.first.is_visible(timeout=1000):
                court_logger.error(f"{log_prefix} 'Case not found' on intermediate page.")
            elif await invalid_format_loc.count() > 0 and await invalid_format_loc.first.is_visible(timeout=1000):
                court_logger.error(f"{log_prefix} 'Invalid case format' on intermediate page.")
            await main_navigator.save_screenshot("docket_report_link_not_visible_intermediate")
            return None

        court_logger.info(f"{log_prefix} Attempting to open 'Docket Report' in new page.")
        href = await docket_report_first_link_loc.get_attribute("href")
        case_page_for_sheet: Optional[Page] = None

        if not href:  # JS driven link, possibly target=_blank
            court_logger.info(f"{log_prefix} 'Docket Report' link has no href. Checking for target=_blank.")
            target_attr = await docket_report_first_link_loc.get_attribute("target")
            if target_attr and target_attr.lower() == "_blank":
                async with context.expect_page(timeout=main_navigator.timeout + 5000) as new_page_info:
                    await docket_report_first_link_loc.click(timeout=10000)
                case_page_for_sheet = await new_page_info.value
                await case_page_for_sheet.wait_for_load_state("domcontentloaded",
                                                              timeout=main_navigator.timeout + 20000)
            else:
                court_logger.error(f"{log_prefix} No href and not target=_blank. Cannot proceed.")
                await main_navigator.save_screenshot("docket_report_link_no_href_no_blank")
                return None
        else:  # Standard href link
            from urllib.parse import urljoin
            absolute_href = urljoin(page.url, href)
            court_logger.info(f"{log_prefix} Resolved 'Docket Report' link URL to: {absolute_href}")
            case_page_for_sheet = await context.new_page()
            await case_page_for_sheet.goto(absolute_href, wait_until="domcontentloaded",
                                           timeout=main_navigator.timeout + 30000)

        if not case_page_for_sheet or case_page_for_sheet.is_closed():
            court_logger.error(f"{log_prefix} Failed to obtain page from 'Docket Report' link.")
            return None

        court_logger.info(f"{log_prefix} Navigated to potential docket sheet entry page: {case_page_for_sheet.url}")
        return case_page_for_sheet

    @staticmethod
    async def _handle_final_run_report_button_on_case_page(case_page: Page, navigator_timeout: int,
                                                           log_prefix: str, screenshot_dir: Path,
                                                           court_logger: logging.Logger) -> bool:
        """Handles the final 'Run Report' button if present on the case page."""
        court_logger.info(f"{log_prefix} On page '{case_page.url}', checking for a final 'Run Report' button.")
        final_run_report_selector = "input[name='button1'][value='Run Report'][type='button']"
        final_run_report_button_loc_collection = case_page.locator(final_run_report_selector)

        if await final_run_report_button_loc_collection.count() > 0:
            final_run_report_button_first_loc = final_run_report_button_loc_collection.first
            try:
                await final_run_report_button_first_loc.wait_for(state='visible', timeout=10000)
                court_logger.info(f"{log_prefix} Final 'Run Report' button found and visible. Attempting click.")
                async with case_page.expect_navigation(wait_until="domcontentloaded",
                                                       timeout=navigator_timeout + 20000):
                    await final_run_report_button_first_loc.click(timeout=15000)
                court_logger.info(f"{log_prefix} Clicked final 'Run Report'. New URL: {case_page.url}")
                await case_page.wait_for_timeout(1000)  # Brief pause
                return True
            except PlaywrightTimeoutError:
                court_logger.warning(
                    f"{log_prefix} Final 'Run Report' button timed out (visibility/click). Page: {case_page.url}")
                # Save screenshot for debugging if this path is taken unexpectedly.
                # Need a temporary navigator for this page to set screenshot dir
                temp_nav = PacerNavigator(case_page)
                temp_nav.set_screenshot_dir(str(screenshot_dir))
                await temp_nav.save_screenshot("final_run_report_button_timeout")
                # Assume it might be okay, proceed to verification. If not, verification will fail.
                return True  # Let verification confirm
            except PlaywrightError as e_final_click:
                court_logger.error(f"{log_prefix} Error clicking final 'Run Report' button: {e_final_click}",
                                   exc_info=True)
                if not case_page.is_closed():
                    temp_nav_err = PacerNavigator(case_page)
                    temp_nav_err.set_screenshot_dir(str(screenshot_dir))
                    await temp_nav_err.save_screenshot("final_run_report_click_error")
                return False  # This is a failure for this step
        else:
            court_logger.info(
                f"{log_prefix} Final 'Run Report' button not found. Assuming current page is docket sheet or error page.")
            return True  # Proceed to verification

    @staticmethod
    async def _verify_docket_sheet_content_on_case_page(case_page: Page, log_prefix: str,
                                                        screenshot_dir: Path, court_logger: logging.Logger) -> bool:
        """Verifies that the current page appears to be a docket sheet."""
        if not case_page or case_page.is_closed():
            court_logger.error(f"{log_prefix} Cannot verify, page is invalid or closed.")
            return False

        main_content_selector = "table[width='99%'][border='1'], center > h3"  # Common docket sheet structures
        try:
            # case_page.locator is not async.
            case_page_main_content_loc_collection = case_page.locator(main_content_selector)
            # await first locator from collection, then wait_for on that specific locator
            await case_page_main_content_loc_collection.first.wait_for(state="visible", timeout=15000)
            court_logger.info(f"{log_prefix} Successfully verified docket sheet content on page: {case_page.url}")
            return True
        except PlaywrightError:  # Catches TimeoutError from wait_for or other Playwright issues
            court_logger.error(
                f"{log_prefix} Failed to verify docket sheet content on page: {case_page.url}. Title: '{await case_page.title()}'")
            temp_nav = PacerNavigator(case_page)  # Create new navigator for this specific page
            temp_nav.set_screenshot_dir(str(screenshot_dir))  # screenshot_dir is passed from caller
            await temp_nav.save_screenshot("docket_sheet_verification_fail")
            return False

    async def _process_single_row_from_report(self,
                                              row_locator_instance: Locator,
                                              row_num: int,
                                              total_rows: int,
                                              court_id: str,
                                              iso_date: str,
                                              start_date_obj: DateType,  # Overall run start date
                                              end_date_obj: DateType,  # Overall run end date
                                              context: BrowserContext,
                                              original_page: Page,
                                              processor_config_for_dockets: Dict[str, Any],
                                              allowed_iso_dates: Set[str],
                                              added_date_iso_for_filemanager: str,
                                              relevance_engine: CaseRelevanceEngine,
                                              report_screenshot_dir: Path,
                                              row_counts: Dict[str, int],
                                              court_logger: logging.Logger
                                              ) -> None:
        log_prefix_row_pre_docket = f"[{court_id}][Row-{row_num}/{total_rows}]"
        court_logger.info(f"--- {log_prefix_row_pre_docket} Starting processing attempt ---")

        case_page: Optional[Page] = None
        cleaned_docket_num_for_log = "PRE_EXTRACT"
        current_row_status_summary = "Init"
        base_filename_for_row = None
        log_prefix_row_post_docket = log_prefix_row_pre_docket

        try:
            row_elements = await self._extract_row_elements(row_locator_instance, row_num, log_prefix_row_pre_docket,
                                                            court_logger)
            if not row_elements:
                court_logger.debug(
                    f"{log_prefix_row_pre_docket} Failed to extract basic elements (possibly header or malformed row). Skipping row.")
                row_counts["failed_extract_elements"] += 1
                current_row_status_summary = "Fail_ExtractElements"
                return

            cleaned_docket_num_for_log = await self._get_cleaned_docket_number(row_elements.docket_link_loc, row_num,
                                                                               log_prefix_row_pre_docket, court_logger)
            if not cleaned_docket_num_for_log:
                court_logger.warning(f"{log_prefix_row_pre_docket} Failed to get cleaned docket number. Skipping row.")
                row_counts["failed_get_docket_num"] += 1
                current_row_status_summary = "Fail_GetDocketNum"
                return

            log_prefix_row_post_docket = f"[{court_id}][{cleaned_docket_num_for_log}]"
            court_logger.info(f"{log_prefix_row_post_docket} Extracted docket num: {cleaned_docket_num_for_log}")

            if "-md-" in cleaned_docket_num_for_log.lower():
                court_logger.info(
                    f"{log_prefix_row_post_docket} SKIPPING: Detected '-md-' in docket number ('{cleaned_docket_num_for_log}'). Marking as MD case.")
                row_counts["skipped_md_case"] += 1
                current_row_status_summary = "Skip_MDCase"
                return

            date_check_result = await self._is_date_in_range(
                row_elements.date_cell_loc, allowed_iso_dates,
                row_num, log_prefix_row_post_docket, cleaned_docket_num_for_log, court_logger
            )
            if not date_check_result.is_in_range:
                court_logger.info(f"{log_prefix_row_post_docket} SKIPPING: Date not in general configured range.")
                try:
                    versus_text_for_skip_log = await row_elements.versus_loc.text_content(
                        timeout=1000) or "Unknown Versus"
                except PlaywrightError:
                    versus_text_for_skip_log = "Unknown Versus (Error)"

                reason_skipped_date = (
                    f"Date '{date_check_result.filing_date_for_processor or 'N/A'}' "
                    f"not in configured range ({start_date_obj.strftime('%Y-%m-%d')} to {end_date_obj.strftime('%Y-%m-%d')})"
                )
                skipped_date_details = {
                    'court_id': court_id,
                    'docket_num': cleaned_docket_num_for_log,
                    'versus': versus_text_for_skip_log.strip(),
                    'filing_date': date_check_result.filing_date_for_processor,
                    'added_date_iso': added_date_iso_for_filemanager,
                    '_reason_irrelevant': reason_skipped_date,
                    '_processing_status': 'Skipped (Date Out of Report Range)'
                }
                await self.file_manager.append_to_log_file(
                    added_date_iso_for_filemanager, "irrelevant_cases_all.json", skipped_date_details
                )
                court_logger.info(
                    f"{log_prefix_row_post_docket} Logged skipped_date case to irrelevant_cases_all.json.")
                row_counts["skipped_date"] += 1
                current_row_status_summary = "Skip_DateRange"
                return

            row_filing_date_obj_for_check = date_check_result.filing_date_obj

            if (court_id, cleaned_docket_num_for_log) in self.historically_irrelevant_dockets:
                court_logger.info(
                    f"{log_prefix_row_post_docket} SKIPPING: Found in historically irrelevant dockets list.")
                row_counts["skipped_historical_irrelevant"] += 1
                current_row_status_summary = "Skip_HistoricalIrrelevant"
                return

            existence_check = await self._check_docket_existence_and_relevance(
                court_id, cleaned_docket_num_for_log, row_elements.versus_loc,
                start_date_obj, end_date_obj,
                row_num, log_prefix_row_post_docket,
                added_date_iso_for_filemanager,
                row_filing_date_obj_for_check,
                court_logger
            )
            if existence_check.should_skip:
                court_logger.info(
                    f"{log_prefix_row_post_docket} SKIPPING based on _check_docket_existence_and_relevance: {existence_check.reason}")
                skip_reason_key = existence_check.reason if existence_check.reason else "skipped_unknown_existence"
                row_counts[skip_reason_key] = row_counts.get(skip_reason_key, 0) + 1
                current_row_status_summary = f"Skip_Exists ({existence_check.reason})"
                return

            base_filename_for_row = existence_check.base_filename
            if not base_filename_for_row:
                court_logger.error(
                    f"{log_prefix_row_post_docket} CRITICAL: base_filename not set after existence check for non-skipped case.")
                try:
                    versus_text_for_fn_fallback = await row_elements.versus_loc.text_content(
                        timeout=1000) or "UnknownVersus"
                    base_filename_for_row = self.file_manager.create_base_filename_static(court_id,
                                                                                          cleaned_docket_num_for_log,
                                                                                          versus_text_for_fn_fallback.strip())
                except PlaywrightError:
                    base_filename_for_row = self.file_manager.create_base_filename_static(court_id,
                                                                                          cleaned_docket_num_for_log,
                                                                                          "UnknownVersusError")

            versus_text_raw = await row_elements.versus_loc.text_content(
                timeout=3000) if await row_elements.versus_loc.count() > 0 else "Unknown Versus"

            initial_case_details = {
                'court_id': court_id,
                'docket_num': cleaned_docket_num_for_log,
                'versus': versus_text_raw.strip(),
                'added_date': end_date_obj.strftime('%Y-%m-%d'),
                'added_date_iso': added_date_iso_for_filemanager,
                'source_page': 'Case Report List',
                'cause_from_report': row_elements.cause, 'nos_from_report': row_elements.nos,
                'cause': row_elements.cause, 'nos': row_elements.nos,
                'case_flags_from_report': row_elements.case_flags,
                'base_filename': base_filename_for_row,
                'filing_date': date_check_result.filing_date_for_processor
            }
            court_logger.debug(
                f"{log_prefix_row_post_docket} Initial details from report row (after all skips): {pformat(initial_case_details)}")

            current_nos = initial_case_details.get('nos')
            if current_nos and str(current_nos).strip() != '367':
                versus_text_for_def_check = initial_case_details.get('versus', '').lower()
                defendant_part_text = ""
                defendant_match = re.search(r'v\.\s*(.+)', versus_text_for_def_check, re.IGNORECASE)
                if defendant_match:
                    defendant_part_text = defendant_match.group(1).strip()

                if defendant_part_text:
                    is_defendant_relevant = any(
                        keyword in defendant_part_text for keyword in self.relevant_defendants_lower
                    )
                    if not is_defendant_relevant:
                        reason_irrelevant_nos_def = (
                            f"NOS not 367 ('{current_nos}') AND defendant "
                            f"('{defendant_part_text[:50]}...') not in relevant_defendants_lower."
                        )
                        court_logger.info(
                            f"{log_prefix_row_post_docket} SKIPPING (NOS/DEFENDANT CHECK): {reason_irrelevant_nos_def}")
                        initial_case_details['_reason_irrelevant'] = reason_irrelevant_nos_def
                        initial_case_details['_processing_status'] = 'Skipped (NOS/Defendant Check)'
                        await self.file_manager.append_to_log_file(
                            added_date_iso_for_filemanager, "irrelevant_cases_all.json", initial_case_details
                        )
                        row_counts["skipped_nos_defendant_check"] += 1
                        current_row_status_summary = "Skip_NOSDefendantCheck"
                        return

            if await asyncio.to_thread(relevance_engine.check_overall_relevance,
                                       initial_case_details):  # Relevance engine uses its own logger
                reason_irrelevant = initial_case_details.get('_reason_irrelevant',
                                                             'Unknown report data irrelevance by CRE')
                court_logger.info(
                    f"{log_prefix_row_post_docket} SKIPPING: Marked irrelevant by CaseRelevanceEngine (report data): {reason_irrelevant}. Saving info.")
                initial_case_details['_processing_status'] = 'Skipped (Report Data Irrelevant via CRE)'
                await self.file_manager.append_to_log_file(
                    added_date_iso_for_filemanager, "irrelevant_cases_all.json", initial_case_details
                )
                row_counts["skipped_report_data_irrelevant"] += 1
                current_row_status_summary = f"Skip_ReportIrrelevant_CRE ({reason_irrelevant})"
                return

            court_logger.info(f"{log_prefix_row_post_docket} Passed all pre-checks. Proceeding to open docket page.")
            row_counts["attempted"] += 1

            docket_page_screenshot_subdir = report_screenshot_dir / f"row_{row_num}_{cleaned_docket_num_for_log.replace(':', '_').replace('/', '_')}"
            docket_page_screenshot_subdir.mkdir(parents=True, exist_ok=True)

            # This call can now raise critical PlaywrightErrors if _prepare_and_open_case_page re-raises them
            case_page = await self._prepare_and_open_case_page(
                row_elements.docket_link_loc, original_page, context,
                row_num, log_prefix_row_post_docket, cleaned_docket_num_for_log,
                docket_page_screenshot_subdir, court_logger
            )

            if not case_page or case_page.is_closed():
                court_logger.error(f"{log_prefix_row_post_docket} Failed to open or prepare case page. Skipping.")
                row_counts["failed"] += 1
                current_row_status_summary = "Fail_PageOpen"
                # If _prepare_and_open_case_page returned None without raising a critical error,
                # this path is taken. If it raised, the except block below handles it.
            else:
                processed_case_details, is_downloaded_this_row = await self._process_single_row_docket(
                    case_page, initial_case_details, court_id, iso_date,
                    start_date_obj, end_date_obj, row_num, log_prefix_row_post_docket,
                    docket_page_screenshot_subdir, processor_config_for_dockets, court_logger
                )

                if processed_case_details:
                    row_counts["successful"] += 1
                    current_row_status_summary = "Success"
                    if is_downloaded_this_row:
                        row_counts["downloaded"] += 1
                        current_row_status_summary += "_Downloaded"
                    if processed_case_details.get('_reason_irrelevant'):
                        reason_key_full_proc = processed_case_details['_reason_irrelevant']
                        row_counts["skipped_other_relevance"] += 1
                        current_row_status_summary += f"_IrrelevantFullParse ({reason_key_full_proc})"
                    elif processed_case_details.get('_processing_notes'):
                        court_logger.info(
                            f"{log_prefix_row_post_docket} Processing notes: {processed_case_details['_processing_notes']}")
                else:
                    court_logger.error(
                        f"{log_prefix_row_post_docket} DocketProcessor.process_docket_page failed critically.")
                    row_counts["failed"] += 1
                    current_row_status_summary = "Fail_DocketProcess"

        except PlaywrightError as pe_row:
            # This will catch critical errors re-raised from _prepare_and_open_case_page
            err_str_lower = str(pe_row).lower()
            is_critical_network_error = "net::err_connection_reset" in err_str_lower or \
                                        "net::err_name_not_resolved" in err_str_lower or \
                                        "net::err_timed_out" in err_str_lower or \
                                        "net::err_address_unreachable" in err_str_lower
            is_target_closed_error = "target page, context or browser has been closed" in err_str_lower or \
                                     "execution context was destroyed" in err_str_lower

            if is_critical_network_error or is_target_closed_error:
                court_logger.critical(
                    f"{log_prefix_row_post_docket} Critical PlaywrightError, propagating to potentially retry court task: {pe_row}",
                    exc_info=False)  # Keep exc_info for file log if desired, but console can be cleaner
                raise  # Re-raise to be caught by _process_report_rows
            else:  # Non-critical PlaywrightError, handle as before
                court_logger.error(f"{log_prefix_row_post_docket} PlaywrightError: {pe_row}", exc_info=True)
                row_counts["failed"] += 1
                current_row_status_summary = f"Fail_Playwright ({type(pe_row).__name__})"
                if not original_page.is_closed():
                    try:
                        temp_original_page_nav = PacerNavigator(original_page)
                        temp_original_page_nav.set_screenshot_dir(str(report_screenshot_dir))
                        await temp_original_page_nav.save_screenshot(
                            f"row_{row_num}_playwright_error_{cleaned_docket_num_for_log.replace(':', '_')}")
                    except Exception as ss_e_pe:
                        court_logger.warning(f"Screenshot failed during PlaywrightError on original page: {ss_e_pe}")
        # Keep PlaywrightTimeoutError separate if specific handling is needed, otherwise it's caught by PlaywrightError
        except PlaywrightTimeoutError as pte_row:  # This might be redundant if PlaywrightError catches it, but explicit
            court_logger.error(f"{log_prefix_row_post_docket} Timeout during row processing: {pte_row}", exc_info=False)
            row_counts["failed"] += 1
            current_row_status_summary = "Fail_Timeout"
            if not original_page.is_closed():
                try:
                    temp_original_page_nav = PacerNavigator(original_page)
                    temp_original_page_nav.set_screenshot_dir(str(report_screenshot_dir))
                    await temp_original_page_nav.save_screenshot(
                        f"row_{row_num}_timeout_error_{cleaned_docket_num_for_log.replace(':', '_')}")
                except Exception as ss_e_to:
                    court_logger.warning(f"Screenshot failed during timeout on original page: {ss_e_to}")

        except Exception as e_row:
            court_logger.error(f"{log_prefix_row_post_docket} Unexpected error: {e_row}", exc_info=True)
            row_counts["failed"] += 1
            current_row_status_summary = f"Fail_Unexpected ({type(e_row).__name__})"
            if not original_page.is_closed():
                try:
                    temp_original_page_nav = PacerNavigator(original_page)
                    temp_original_page_nav.set_screenshot_dir(str(report_screenshot_dir))
                    await temp_original_page_nav.save_screenshot(
                        f"row_{row_num}_unexpected_error_{cleaned_docket_num_for_log.replace(':', '_')}")
                except Exception as ss_e_unex:
                    court_logger.warning(f"Screenshot failed during unexpected error on original page: {ss_e_unex}")
        finally:
            await self._cleanup_row_resources(case_page, row_num, log_prefix_row_post_docket, court_logger)
            court_logger.info(f"--- {log_prefix_row_post_docket} Finished. Status: {current_row_status_summary} ---")

    async def _process_report_rows(self,
                                   navigator: PacerNavigator,
                                   court_id: str,
                                   iso_date: str,
                                   start_date_obj: DateType,
                                   end_date_obj: DateType,
                                   context: BrowserContext,
                                   processor_config_for_dockets: Dict[str, Any],
                                   court_logger: logging.Logger
                                   ) -> Dict[str, int]:
        log_prefix_main = f"[{court_id}] ReportRows_DEBUG:"  # Keep for context in messages
        court_logger.info(
            f"{log_prefix_main} ENTERING _process_report_rows. Current Page URL: {navigator.page.url}, Title: '{await navigator.page.title()}'")

        row_counts = {
            "attempted": 0, "successful": 0, "downloaded": 0, "skipped_date": 0,
            "skipped_irrelevant_local_json_today": 0, "skipped_exists_db_gsi": 0,
            "skipped_exists_local_date_range": 0, "skipped_downloaded_local_json_today": 0,
            "skipped_report_data_irrelevant": 0, "skipped_other_relevance": 0,
            "skipped_historical_irrelevant": 0,
            "skipped_nos_defendant_check": 0,
            "skipped_md_case": 0,
            "failed": 0,
            "failed_extract_elements": 0, "failed_get_docket_num": 0
        }

        report_screenshot_dir = Path(navigator._screenshot_dir)
        report_screenshot_dir.mkdir(parents=True, exist_ok=True)
        await navigator.save_screenshot(f"DEBUG_entry_process_report_rows_{court_id}")

        rows_selector_xpath = "//*[@id='cmecfMainContent']/table[@border='1']/tbody/tr[position() > 1]"
        court_logger.info(f"{log_prefix_main} Using XPath for data rows: {rows_selector_xpath}")

        all_row_locators: List[Locator] = []
        total_data_rows_found = 0
        consecutive_critical_row_failures = 0
        max_consecutive_critical_failures_threshold = self.config.get('max_consecutive_row_failures_for_court_retry', 3)

        try:
            court_logger.info(f"{log_prefix_main} STEP 1: Verifying page readiness and main content table.")
            if navigator.page.is_closed():
                court_logger.error(f"{log_prefix_main} Page was CLOSED at the start of _process_report_rows. Aborting.")
                row_counts["failed"] += 1  # Or some other indicator of total failure
                raise PlaywrightError("Page closed at start of _process_report_rows")  # Propagate for retry

            main_table_body_selector = "//div[@id='cmecfMainContent']/table[@border='1']/tbody"
            await navigator.page.wait_for_selector(main_table_body_selector, state="attached", timeout=15000)
            court_logger.info(f"{log_prefix_main} Main report table tbody ({main_table_body_selector}) is attached.")
            await navigator.save_screenshot(f"DEBUG_after_tbody_wait_{court_id}")

            court_logger.info(f"{log_prefix_main} STEP 2: Attempting to get count of data rows using locator.count().")
            data_rows_collection_locator = navigator.page.locator(rows_selector_xpath)

            try:
                if await data_rows_collection_locator.count() > 0:
                    await data_rows_collection_locator.first.get_attribute("class", timeout=2000)
                    court_logger.info(f"{log_prefix_main} Quick check on first element of collection locator passed.")
                else:
                    court_logger.info(
                        f"{log_prefix_main} Collection locator count is 0, skipping quick check on first element.")
            except Exception as e_quick_check:
                court_logger.error(
                    f"{log_prefix_main} QUICK CHECK FAILED on collection locator '{rows_selector_xpath}': {type(e_quick_check).__name__} - {str(e_quick_check)}. This might indicate an issue with the selector or page state.",
                    exc_info=True)
                await navigator.save_screenshot(f"DEBUG_quick_check_failed_on_collection_locator_{court_id}")

            total_data_rows_found = await data_rows_collection_locator.count()
            court_logger.info(
                f"{log_prefix_main} navigator.page.locator().count() found {total_data_rows_found} data rows for court {court_id} using XPath.")
            await navigator.save_screenshot(f"DEBUG_after_row_count_{court_id}")

            if total_data_rows_found == 0:
                court_logger.warning(
                    f"{log_prefix_main} No data rows found (count is 0) for {court_id}. Saving current HTML for inspection.")
                page_html_on_no_rows = await navigator.page.content()
                no_rows_html_path = report_screenshot_dir / f"DEBUG_HTML_no_data_rows_found_{court_id}.html"
                with open(no_rows_html_path, "w", encoding="utf-8") as f_html: f_html.write(page_html_on_no_rows)
                court_logger.info(f"{log_prefix_main} Saved HTML of report page to {no_rows_html_path}")
                return row_counts  # No rows to process, not an error for the court task itself

            court_logger.info(f"{log_prefix_main} STEP 3: Attempting to get all row locators using locator.all().")
            all_row_locators = await data_rows_collection_locator.all()
            court_logger.info(
                f"{log_prefix_main} Collected {len(all_row_locators)} locators using .all() for court {court_id}.")
            await navigator.save_screenshot(f"DEBUG_after_row_all_{court_id}")

            if not all_row_locators and total_data_rows_found > 0:  # Should not happen
                court_logger.error(
                    f"{log_prefix_main} CRITICAL MISMATCH: .count() was {total_data_rows_found}, but .all() returned {len(all_row_locators)} for {court_id}. This should not happen.")
                # This could be a sign of instability, consider raising to retry court.
                raise PlaywrightError(
                    f"Row count mismatch for {court_id}: count {total_data_rows_found}, all() {len(all_row_locators)}")


        except PlaywrightError as pe:  # Catch Playwright errors during row finding/counting
            court_logger.error(
                f"{log_prefix_main} PlaywrightError during row location/counting for {court_id}: {str(pe)}",
                exc_info=True)
            if navigator.is_ready and not navigator.page.is_closed(): await navigator.save_screenshot(
                f"DEBUG_PLAYWRIGHT_ERROR_process_report_rows_{court_id}")
            # Re-raise this error to be caught by _process_single_court_task for retry
            raise
        except Exception as e:  # Catch other unexpected errors
            court_logger.error(
                f"{log_prefix_main} UNEXPECTED Exception during row location/counting for {court_id}: {type(e).__name__} - {str(e)}",
                exc_info=True)
            if navigator.is_ready and not navigator.page.is_closed(): await navigator.save_screenshot(
                f"DEBUG_UNEXPECTED_ERROR_process_report_rows_{court_id}")
            # Re-raise this error to be caught by _process_single_court_task for retry
            raise RuntimeError(f"Unexpected error during row location for {court_id}") from e

        if not all_row_locators:  # Should be caught by total_data_rows_found == 0 check earlier if no rows
            court_logger.warning(
                f"{log_prefix_main} No data row locators were ultimately collected for {court_id}. No rows will be processed from list.")
            return row_counts

        court_logger.info(
            f"{log_prefix_main} Successfully located {len(all_row_locators)} data rows. Proceeding to iterate.")

        allowed_iso_dates: Set[str] = set(generate_filing_dates_iso(start_date_obj, end_date_obj))
        added_date_iso_for_filemanager = end_date_obj.strftime('%Y%m%d')
        original_page = navigator.page

        try:
            relevance_engine_instance = CaseRelevanceEngine(
                relevance_config=self.relevance_config,
                relevant_defendants_lower=self.relevant_defendants_lower,
                usa_defendant_regex_compiled=self.usa_defendant_regex_compiled,
                current_court_id=court_id,
                logger=court_logger  # Pass court_logger here
            )
            court_logger.info(f"{log_prefix_main} Initialized CaseRelevanceEngine for court {court_id}.")
        except Exception as cre_init_e:
            court_logger.error(
                f"{log_prefix_main} CRITICAL: Failed to initialize CaseRelevanceEngine for court {court_id}: {cre_init_e}. Aborting row processing.",
                exc_info=True)
            # Re-raise to trigger court retry as this is a setup issue
            raise RuntimeError(f"CaseRelevanceEngine init failed for {court_id}") from cre_init_e

        court_logger.info(
            f"{log_prefix_main} Starting loop to process {total_data_rows_found} data rows for {court_id}.")

        for i, row_locator_instance in enumerate(all_row_locators):
            row_num_for_log = i + 1
            court_logger.info(
                f"{log_prefix_main} === Processing data row {row_num_for_log}/{total_data_rows_found} ===")
            if original_page.is_closed():
                court_logger.critical(
                    f"{log_prefix_main} Original page or context closed PREMATURELY before processing row {row_num_for_log} for {court_id}. Aborting further rows.")
                row_counts["failed"] += (total_data_rows_found - i)
                # This is a critical failure, propagate up to retry the court
                raise PlaywrightError(f"Original page closed prematurely during row processing for {court_id}")

            try:
                await self._process_single_row_from_report(
                    row_locator_instance, row_num_for_log, total_data_rows_found,
                    court_id, iso_date, start_date_obj, end_date_obj,
                    context, original_page, processor_config_for_dockets,
                    allowed_iso_dates, added_date_iso_for_filemanager,
                    relevance_engine_instance,
                    report_screenshot_dir,
                    row_counts,
                    court_logger
                )
                consecutive_critical_row_failures = 0  # Reset on successful row processing (even if row is skipped)
            except PlaywrightError as critical_row_error:
                # This catches critical errors re-raised by _process_single_row_from_report
                err_str_lower = str(critical_row_error).lower()
                is_network_related = "net::err_" in err_str_lower
                is_target_closed = "target page" in err_str_lower or "execution context" in err_str_lower

                if is_network_related or is_target_closed:
                    consecutive_critical_row_failures += 1
                    court_logger.warning(
                        f"{log_prefix_main} Consecutive critical row failure #{consecutive_critical_row_failures} (threshold: {max_consecutive_critical_failures_threshold}). Error: {critical_row_error}")
                    if consecutive_critical_row_failures >= max_consecutive_critical_failures_threshold:
                        court_logger.error(
                            f"{log_prefix_main} Reached threshold for consecutive critical row failures. Re-raising to trigger court task retry.")
                        raise  # Propagate to _process_single_court_task
                    else:
                        # Log, count failure, but continue to next row for now
                        row_counts["failed"] += 1
                else:
                    # Non-critical (or not recognized as critical for retry) PlaywrightError from row processing
                    # This should ideally not happen if _process_single_row_from_report handles non-critical ones itself
                    court_logger.error(
                        f"{log_prefix_main} Unexpected PlaywrightError propagated from row processing: {critical_row_error}")
                    row_counts["failed"] += 1
                    consecutive_critical_row_failures = 0  # Reset as it wasn't a "critical for retry" type

            await asyncio.sleep(
                self.config.get('delay_between_rows_s', 0.2))

        court_logger.info(
            f"{log_prefix_main} Finished processing all {total_data_rows_found} data rows for {court_id}.")
        summary_lines = [
            f"TOTAL DATA ROWS IDENTIFIED BY LOCATOR: {total_data_rows_found}",
            f"Rows Attempted Processing (after skips): {row_counts.get('attempted', 0)}",
            f"Rows Successful: {row_counts.get('successful', 0)}",
            f"Rows Downloaded: {row_counts.get('downloaded', 0)}",
            f"Rows Failed (during processing): {row_counts.get('failed', 0)}",
            "--- Skip Counts ---",
            f"Skipped (Date Out of Range): {row_counts.get('skipped_date', 0)}",
            f"Skipped (Exists in DB - GSI): {row_counts.get('skipped_exists_db_gsi', 0)}",
            f"Skipped (Exists Locally - Date Range): {row_counts.get('skipped_exists_local_date_range', 0)}",
            f"Skipped (Irrelevant Local JSON Today): {row_counts.get('skipped_irrelevant_local_json_today', 0)}",
            f"Skipped (Downloaded Local JSON Today): {row_counts.get('skipped_downloaded_local_json_today', 0)}",
            f"Skipped (Irrelevant - Report Data via CRE): {row_counts.get('skipped_report_data_irrelevant', 0)}",
            f"Skipped (Irrelevant - Full Parse in DP): {row_counts.get('skipped_other_relevance', 0)}",
            f"Skipped (Historical Irrelevant): {row_counts.get('skipped_historical_irrelevant', 0)}",
            f"Skipped (NOS/Defendant Check): {row_counts.get('skipped_nos_defendant_check', 0)}",
            f"Skipped (MD Case in Docket Number): {row_counts.get('skipped_md_case', 0)}",
            "--- Failure Details (Pre-Attempt) ---",
            f"Failed to Extract Row Elements: {row_counts.get('failed_extract_elements', 0)}",
            f"Failed to Get Docket Number: {row_counts.get('failed_get_docket_num', 0)}"
        ]
        court_logger.warning(f"{log_prefix_main} DETAILED ROW PROCESSING SUMMARY FOR {court_id}:\n" + "\n".join(
            [f"  - {line}" for line in summary_lines]))
        self.logger.info(
            f"[{court_id}] ReportRows: Processed {total_data_rows_found} rows. Attempted: {row_counts.get('attempted', 0)}, Successful: {row_counts.get('successful', 0)}, Failed: {row_counts.get('failed', 0)}.")

        return row_counts

    async def process_courts(self,
                             court_ids: List[str],
                             start_date_str: Optional[str],
                             end_date_str: Optional[str],
                             multi_docket_court_id: Optional[str] = None,
                             multi_docket_numbers: Optional[List[str]] = None,
                             docket_list_input: Optional[List[Dict[str, Any]]] = None
                             ) -> Tuple[List[str], List[Dict[str, Any]]]:  # MODIFIED return type
        start_date_obj, end_date_obj = self._get_date_range(start_date_str, end_date_str)
        iso_date_for_run = end_date_obj.strftime('%Y%m%d')
        from_date_fmt = format_date_mmddyy(start_date_obj)
        to_date_fmt = format_date_mmddyy(end_date_obj)

        self._setup_directories(iso_date_for_run)
        await self._load_historical_irrelevant_cases(iso_date_for_run)

        active_tasks: List[asyncio.Task] = []

        max_workers = self.config.get('max_workers', self.DEFAULT_MAX_WORKERS)
        retries_per_task = self.config.get('retries_per_court', self.DEFAULT_RETRIES_PER_COURT)
        semaphore = asyncio.Semaphore(max_workers)

        if docket_list_input:
            self.logger.info(
                f"Mode: List of Docket Objects. Processing {len(docket_list_input)} docket entries.")
            court_to_dockets_map = OrderedDict()
            processed_docket_keys = set()
            for item_idx, item in enumerate(docket_list_input):
                c_id, d_num = item.get('court_id'), item.get('docket_num')
                if c_id and d_num:
                    docket_key = (c_id, d_num)
                    if docket_key in processed_docket_keys: continue
                    court_to_dockets_map.setdefault(c_id, []).append(d_num)
                    processed_docket_keys.add(docket_key)
                else:
                    self.logger.warning(f"Invalid docket item: {item} at index {item_idx}.")

            if not court_to_dockets_map:
                self.logger.warning("No valid dockets from docket_list_input.")
            else:
                self.logger.info(
                    f"Aggregated dockets for {len(court_to_dockets_map)} courts: {list(court_to_dockets_map.keys())}")
                for court_id_from_map, dockets_for_court in court_to_dockets_map.items():
                    task_coro = self._process_multiple_dockets_for_court_task(
                        semaphore, court_id_from_map, dockets_for_court, iso_date_for_run,
                        start_date_obj, end_date_obj, retries_per_task)
                    task_name = f"Task-{court_id_from_map}-MultiDocket-{uuid.uuid4().hex[:4]}"
                    active_tasks.append(asyncio.create_task(task_coro, name=task_name))
        elif multi_docket_court_id and multi_docket_numbers:
            self.logger.info(
                f"Mode: Multi-Docket. Processing {len(multi_docket_numbers)} dockets for {multi_docket_court_id}.")
            task_coro = self._process_multiple_dockets_for_court_task(
                semaphore, multi_docket_court_id, multi_docket_numbers, iso_date_for_run,
                start_date_obj, end_date_obj, retries_per_task)
            task_name = f"Task-{multi_docket_court_id}-MultiDocketSingle-{uuid.uuid4().hex[:4]}"
            active_tasks.append(asyncio.create_task(task_coro, name=task_name))
        elif court_ids:
            self.logger.info(f"Mode: Report Scraping for courts: {court_ids}")
            for court_id in court_ids:
                task_coro = self._process_single_court_task(
                    semaphore, court_id, iso_date_for_run, start_date_obj, end_date_obj,
                    from_date_fmt, to_date_fmt, retries_per_task)
                task_name = f"Task-{court_id}-Report-{uuid.uuid4().hex[:4]}"
                active_tasks.append(asyncio.create_task(task_coro, name=task_name))
        else:
            self.logger.warning("No specific processing mode determined. No tasks generated.")

        if not active_tasks:
            self.logger.info("No tasks generated for processing.")
            await self.browser_service.close()
            return [], []  # MODIFIED: Return empty lists for failed_courts and processed_stats_list

        self.logger.info(f"Starting processing for {len(active_tasks)} tasks...")

        failed_courts_final: List[str] = []
        processed_stats_list_final: List[Dict[str, Any]] = []  # MODIFIED: Collect all processed stats here

        for completed_future in asyncio.as_completed(active_tasks):
            task_display_name = "UnknownTask (name not accessible from yielded future)"
            if hasattr(completed_future, 'get_name') and callable(completed_future.get_name):
                task_display_name = completed_future.get_name()
            elif hasattr(completed_future, 'cr_code') and hasattr(completed_future.cr_code,
                                                                  'co_name'):  # For coroutine objects
                task_display_name = f"Coroutine({completed_future.cr_code.co_name})"

            processed_court_id = f"UNKNOWN_COURT_FOR_TASK_{task_display_name}"  # Placeholder

            try:
                result = await completed_future

                if isinstance(result, tuple) and len(result) == 3:
                    processed_court_id, success_flag, counts_dict = result

                    # Collect stats for main process to log
                    if counts_dict:  # Ensure counts_dict is not empty
                        # Add CourtID to the dictionary for the main process's CSV logger
                        counts_dict["CourtID"] = processed_court_id
                        processed_stats_list_final.append(counts_dict)

                    if success_flag:
                        self.logger.info(
                            f"Task for court '{processed_court_id}' (task_obj_name: {task_display_name}) completed successfully.")
                    else:
                        self.logger.warning(
                            f"Task for court '{processed_court_id}' (task_obj_name: {task_display_name}) reported failure.")
                        failed_courts_final.append(processed_court_id)  # Collect failed courts

                elif isinstance(result, Exception):
                    self.logger.error(
                        f"Task (task_obj_name: {task_display_name}) yielded an unhandled Exception result: {result}",
                        exc_info=result)
                    # We don't know the court_id if the task failed before returning it.
                    failed_courts_final.append(f"{processed_court_id} (Unhandled Exception)")
                else:
                    self.logger.error(
                        f"Task (task_obj_name: {task_display_name}) returned unexpected result type: {type(result)}. Value: {str(result)[:200]}")
                    failed_courts_final.append(f"{processed_court_id} (Unexpected Result Type)")

            except Exception as e_await:
                self.logger.error(
                    f"Error awaiting result from as_completed for task (task_obj_name: {task_display_name}): {e_await}",
                    exc_info=True)
                self.logger.debug(f"Object yielded by as_completed that caused await error: {completed_future}")
                failed_courts_final.append(f"{processed_court_id} (Await Error)")

        total_time = time.monotonic() - self.overall_start_time
        self.logger.info("--- Overall Processing Summary ---")
        self.logger.info(f"Total Tasks Submitted: {len(active_tasks)}")
        self.logger.info(f"Tasks Successfully Completed: {len(active_tasks) - len(failed_courts_final)}")
        self.logger.info(f"Tasks Failed or Errored: {len(failed_courts_final)}")
        self.logger.info(f"Total Execution Time: {total_time:.2f} seconds")
        if failed_courts_final:
            self.logger.warning("Failed Tasks Details:")
            for cid in failed_courts_final:
                self.logger.warning(f"  - {cid}")

        await self.browser_service.close()
        return failed_courts_final, processed_stats_list_final  # MODIFIED: Return both lists

    @staticmethod
    async def _navigate_to_query_page(navigator: PacerNavigator, court_id: str, court_logger: logging.Logger) -> bool:
        """Navigates to the main query page after ECF login."""
        log_prefix = f"[{court_id}] NavToQuery:"
        try:
            court_logger.info(f"{log_prefix} Navigating to Query page...")
            query_link_selectors = [
                "a:text('Query')",
                "a[href*='iquery.pl']",
                "//ul[@class='nav-links']//a[contains(text(),'Query')]",
                "//a[normalize-space()='Query']"
            ]
            clicked = False
            for idx, selector in enumerate(query_link_selectors):
                try:
                    court_logger.debug(f"{log_prefix} Attempting Query link selector #{idx + 1}: {selector}")

                    locator_collection = await navigator.locator(selector)  # Get Locator for collection
                    if await locator_collection.count() == 0:
                        court_logger.debug(f"{log_prefix} Query link selector '{selector}' found no elements.")
                        continue

                    query_first_link_locator = locator_collection.first  # Get Locator for the first element

                    await query_first_link_locator.wait_for(state='attached', timeout=5000)
                    if await query_first_link_locator.is_visible(timeout=3000):
                        court_logger.info(f"{log_prefix} Query link '{selector}' is visible. Attempting click.")
                        async with navigator.page.expect_navigation(wait_until='domcontentloaded',
                                                                    timeout=navigator.timeout + 10000):
                            await query_first_link_locator.click()  # Click the resolved specific locator

                        court_logger.info(
                            f"{log_prefix} Clicked Query link using '{selector}'. Current URL: {navigator.page.url}")

                        verify_selectors = ["input[name*='case_num']", "input[id*='case_num']",
                                            "input[name='caseid']", "#case_number_text_area_id_0"]
                        verified_on_query_page = False
                        for v_sel in verify_selectors:
                            verify_loc_collection = await navigator.locator(v_sel)
                            if await verify_loc_collection.count() > 0 and await verify_loc_collection.first.is_visible(
                                    timeout=2000):
                                court_logger.info(f"{log_prefix} Verified on query page using selector '{v_sel}'.")
                                verified_on_query_page = True
                                break
                        if not verified_on_query_page:
                            court_logger.warning(
                                f"{log_prefix} Clicked query link, but verification of query page failed. URL: {navigator.page.url}")

                        clicked = True
                        return True
                except PlaywrightError as e_sel:
                    court_logger.debug(
                        f"{log_prefix} Query link selector '{selector}' failed: {type(e_sel).__name__} - {str(e_sel).splitlines()[0]}")

            if not clicked:
                court_logger.error(f"{log_prefix} Could not find or click any Query link.")
                await navigator.save_screenshot("query_link_nav_fail")
                return False
            return clicked  # Should be true if loop finishes and one was clicked
        except PlaywrightError as e:
            court_logger.error(f"{log_prefix} Error navigating to Query page: {e}", exc_info=True)
            await navigator.save_screenshot("query_page_nav_error")
            return False
        except Exception as e_unexp:
            court_logger.error(f"{log_prefix} Unexpected error navigating to Query page: {e_unexp}", exc_info=True)
            await navigator.save_screenshot("query_page_nav_unexpected_error")
            return False

    async def _query_docket_and_get_page(self,
                                         main_navigator: PacerNavigator,
                                         context: BrowserContext,
                                         court_id: str,
                                         docket_num: str,
                                         screenshot_dir_base: Path,
                                         court_logger: logging.Logger
                                         ) -> Optional[Page]:
        """
        Orchestrates the multi-step process of querying a docket number and arriving at the docket sheet page.
        Assumes main_navigator.page is already on the query input page.
        Returns the new page object for the docket sheet, or None on failure.
        """
        log_prefix = f"[{court_id}][{docket_num}] QueryDocket:"
        court_logger.info(f"{log_prefix} Starting multi-step query process.")

        query_screenshot_dir = screenshot_dir_base / f"query_{docket_num.replace(':', '_').replace('/', '_')}"
        query_screenshot_dir.mkdir(parents=True, exist_ok=True)

        original_main_nav_screenshot_dir = main_navigator._screenshot_dir
        main_navigator.set_screenshot_dir(str(query_screenshot_dir))  # Set for helpers using main_navigator

        case_page_for_sheet: Optional[Page] = None

        try:
            if not await self._fill_docket_num_and_trigger_find(main_navigator, docket_num, log_prefix, court_logger):
                return None

            if not await self._click_run_query_and_navigate_on_query_page(main_navigator, log_prefix, court_logger):
                return None
            # At this point, main_navigator.page is the intermediate page (select report type)

            case_page_for_sheet = await self._click_docket_report_link_on_intermediate_page(main_navigator, context,
                                                                                            log_prefix, court_logger)
            if not case_page_for_sheet or case_page_for_sheet.is_closed():
                # Error logging is done within _click_docket_report_link
                return None

            # Now, case_page_for_sheet is the page that might have another "Run Report" button or is the docket sheet.
            if not await self._handle_final_run_report_button_on_case_page(case_page_for_sheet, main_navigator.timeout,
                                                                           log_prefix, query_screenshot_dir,
                                                                           court_logger):
                if case_page_for_sheet and not case_page_for_sheet.is_closed(): await case_page_for_sheet.close()
                return None

            # Verify the final page
            if not await self._verify_docket_sheet_content_on_case_page(case_page_for_sheet, log_prefix,
                                                                        query_screenshot_dir, court_logger):
                if case_page_for_sheet and not case_page_for_sheet.is_closed(): await case_page_for_sheet.close()
                return None

            court_logger.info(
                f"{log_prefix} Successfully obtained and verified docket sheet page: {case_page_for_sheet.url}")
            return case_page_for_sheet

        except Exception as e:  # Catch-all for unexpected issues in orchestration logic
            court_logger.error(f"{log_prefix} Unexpected error during docket query orchestration: {e}", exc_info=True)
            if main_navigator.is_ready: await main_navigator.save_screenshot("query_docket_orchestration_error")
            if case_page_for_sheet and not case_page_for_sheet.is_closed(): await case_page_for_sheet.close()
            return None
        finally:
            main_navigator.set_screenshot_dir(original_main_nav_screenshot_dir)  # Restore original screenshot dir
            court_logger.info(f"{log_prefix} Finished multi-step query process.")

    async def _process_multiple_dockets_for_court_task(
            self,
            semaphore: asyncio.Semaphore,
            court_id: str,
            docket_numbers: List[str],
            iso_date: str,
            start_date_obj: DateType,
            end_date_obj: DateType,
            max_attempts: int
    ) -> Tuple[str, bool, Dict[str, Any]]:
        async with semaphore:
            log_prefix_task = f"[{court_id}] MultiDocketTask:"  # For self.logger (console)
            self.logger.info(f"{log_prefix_task} Starting for {len(docket_numbers)} dockets. ISO Date: {iso_date}")

            log_file_path = Path(self.config.get('data_path', get_default_data_path(
                self.config))) / iso_date / "logs" / f"pacer_{court_id}.log"
            court_logger_name = f"pacer_run.{court_id}.multidocket"
            court_logger = _setup_court_file_logger(court_logger_name, log_file_path)
            court_logger.info(
                f"{log_prefix_task} Detailed logging for this multi-docket task started. Processing {len(docket_numbers)} dockets.")

            context: Optional[BrowserContext] = None
            main_page: Optional[Page] = None
            main_navigator: Optional[PacerNavigator] = None

            overall_counts = {
                "attempted_dockets": 0, "successful_dockets": 0, "downloaded_dockets": 0,
                "skipped_dockets_existence": 0, "failed_docket_query": 0, "failed_docket_processing": 0
            }
            task_overall_success = False
            last_critical_exception_str = "No critical exception"
            final_counts_to_return = {key: 0 for key in ["attempted", "successful", "downloaded", "skipped_date",
                                                         "skipped_exists_db_gsi", "skipped_exists_local_date_range",
                                                         "skipped_irrelevant_local_json_today",
                                                         "skipped_downloaded_local_json_today",
                                                         "skipped_report_data_irrelevant",
                                                         "skipped_other_relevance", "failed",
                                                         "skipped_historical_irrelevant",  # Ensure all keys are present
                                                         "skipped_nos_defendant_check", "skipped_md_case"
                                                         ]}

            base_temp_downloads_for_court = self.file_manager.base_data_dir / iso_date / "dockets" / "temp" / f"{court_id}_multidocket_dl_{uuid.uuid4().hex[:4]}"
            try:
                if base_temp_downloads_for_court.exists(): shutil.rmtree(base_temp_downloads_for_court)
                base_temp_downloads_for_court.mkdir(parents=True, exist_ok=True)
            except Exception as dir_err:
                court_logger.error(
                    f"{log_prefix_task} CRITICAL: Failed to manage base temp download dir {base_temp_downloads_for_court}: {dir_err}. Aborting task.")
                self.logger.error(f"{log_prefix_task} CRITICAL dir error for {base_temp_downloads_for_court}")
                final_counts_to_return["failed"] = len(docket_numbers)
                return court_id, False, final_counts_to_return

            processor_config = self.config.copy()
            processor_config['context_download_path'] = str(base_temp_downloads_for_court)
            processor_config.setdefault('delay', self.DEFAULT_DELAY_S)

            screenshot_dir_base_path = Path(self.config.get('data_path',
                                                            get_default_data_path(
                                                                self.config))) / iso_date / 'screenshots' / f"{court_id}_multidocket_run_{uuid.uuid4().hex[:4]}"
            screenshot_dir_base_path.mkdir(parents=True, exist_ok=True)
            query_page_url_for_court = ""

            try:
                for attempt in range(max_attempts):
                    court_logger.info(f"{log_prefix_task} Overall Task Attempt {attempt + 1}/{max_attempts}")
                    self.logger.info(f"{log_prefix_task} Attempt {attempt + 1}/{max_attempts} for {court_id}")
                    attempt_success_flag = False
                    current_step_for_attempt = "Initialization"

                    try:
                        current_step_for_attempt = "BrowserContextCreation"
                        context = await self.browser_service.new_context(
                            download_path=str(base_temp_downloads_for_court))
                        main_page = await context.new_page()
                        main_navigator = PacerNavigator(main_page, processor_config['delay'],
                                                        self.browser_service.timeout_ms)
                        main_nav_screenshot_dir = screenshot_dir_base_path / f"attempt_{attempt + 1}_main_nav"
                        main_nav_screenshot_dir.mkdir(parents=True, exist_ok=True)
                        main_navigator.set_screenshot_dir(str(main_nav_screenshot_dir))

                        current_step_for_attempt = "AuthenticatorCreation"
                        authenticator = PacerAuthenticator(main_navigator, processor_config['username_prod'],
                                                           processor_config['password_prod'],
                                                           logger=court_logger)

                        current_step_for_attempt = "ECFLoginSequence"
                        if not await self._perform_ecf_login_sequence(main_navigator, court_id, authenticator,
                                                                      court_logger):
                            raise Exception("ECF Login Sequence Failed")

                        current_step_for_attempt = "NavigateToQueryPage"
                        if not await self._navigate_to_query_page(main_navigator, court_id, court_logger):
                            raise Exception("Navigation to Query page failed")

                        query_page_url_for_court = main_page.url
                        court_logger.info(
                            f"{log_prefix_task} Successfully logged in and navigated to query page: {query_page_url_for_court}")

                        for docket_idx, docket_num_to_process in enumerate(docket_numbers):
                            log_prefix_docket = f"[{court_id}][{docket_num_to_process} ({docket_idx + 1}/{len(docket_numbers)})]"
                            court_logger.info(f"--- {log_prefix_docket} Starting processing ---")
                            if attempt == 0:  # Only count attempts on the first try for a docket
                                overall_counts["attempted_dockets"] += 1

                            case_page_for_docket: Optional[Page] = None
                            try:
                                if main_page.is_closed() or main_page.url.rstrip(
                                        '/') != query_page_url_for_court.rstrip('/'):
                                    court_logger.info(
                                        f"{log_prefix_docket} Main page not on query page or closed. Re-navigating.")
                                    if main_page.is_closed():
                                        main_page = await context.new_page()  # type: ignore # context is not None here
                                        main_navigator = PacerNavigator(main_page, processor_config['delay'],
                                                                        self.browser_service.timeout_ms)
                                        main_navigator.set_screenshot_dir(str(main_nav_screenshot_dir))
                                    await main_navigator.goto(query_page_url_for_court, wait_until="domcontentloaded",
                                                              # type: ignore # main_navigator is not None
                                                              timeout_override_ms=main_navigator.timeout + 10000)  # type: ignore
                                    case_num_input_locator_collection = await main_navigator.locator(  # type: ignore
                                        "input[name*='case_num']")
                                    await case_num_input_locator_collection.first.wait_for(state="visible",
                                                                                           timeout=10000)

                                case_page_for_docket = await self._query_docket_and_get_page(main_navigator, context,
                                                                                             # type: ignore
                                                                                             court_id,
                                                                                             docket_num_to_process,
                                                                                             screenshot_dir_base_path,
                                                                                             court_logger)
                                if not case_page_for_docket or case_page_for_docket.is_closed():
                                    court_logger.error(
                                        f"{log_prefix_docket} Failed to query or open docket page. Skipping this docket for this attempt.")
                                    if attempt == 0: overall_counts["failed_docket_query"] += 1
                                    continue

                                initial_details = {
                                    'court_id': court_id, 'docket_num': docket_num_to_process,
                                    'filing_date': None,  # Explicit dockets don't have a report filing date
                                    'added_date': end_date_obj.strftime('%Y-%m-%d'),  # Use run's end_date
                                    'source_page': 'Direct Query MultiDocket', '_is_explicitly_requested': True
                                }
                                case_page_navigator = PacerNavigator(case_page_for_docket, processor_config['delay'],
                                                                     self.browser_service.timeout_ms)
                                case_docket_screenshot_dir = screenshot_dir_base_path / f"docket_attempt_{attempt + 1}_{docket_num_to_process.replace(':', '_').replace('/', '_')}"
                                case_docket_screenshot_dir.mkdir(parents=True, exist_ok=True)
                                case_page_navigator.set_screenshot_dir(str(case_docket_screenshot_dir))

                                async with DocketProcessor(
                                        court_id, iso_date, start_date_obj, end_date_obj,
                                        case_page_navigator, self.file_manager, self.pacer_db, self.dc_db,
                                        self.s3_manager, self.gpt_interface, processor_config, logger=court_logger
                                ) as docket_processor_instance:
                                    processed_output = await docket_processor_instance.process_docket_page(
                                        initial_details)

                                if processed_output:
                                    if attempt == 0:
                                        overall_counts["successful_dockets"] += 1
                                        if docket_processor_instance.is_downloaded: overall_counts[
                                            "downloaded_dockets"] += 1
                                        # Check for specific skip reason if available
                                        if processed_output.get('_processing_notes') and "Skipped (DB" in \
                                                processed_output['_processing_notes']:
                                            overall_counts["skipped_dockets_existence"] += 1
                                    court_logger.info(
                                        f"{log_prefix_docket} Processing successful or skipped as existing for this attempt.")
                                else:  # DocketProcessor failed critically
                                    if attempt == 0: overall_counts["failed_docket_processing"] += 1
                                    court_logger.error(
                                        f"{log_prefix_docket} DocketProcessor failed critically for this docket in attempt {attempt + 1}.")

                            except Exception as docket_e:  # Error for a single docket within the loop
                                court_logger.error(
                                    f"{log_prefix_docket} Unhandled error processing this docket in attempt {attempt + 1}: {docket_e}",
                                    exc_info=True)
                                if attempt == 0: overall_counts["failed_docket_processing"] += 1
                                if main_navigator and main_navigator.is_ready and not (
                                        main_page and main_page.is_closed()):  # type: ignore
                                    await main_navigator.save_screenshot(  # type: ignore
                                        f"multidocket_docket_loop_error_{docket_num_to_process.replace(':', '_')}")
                            finally:
                                if case_page_for_docket and not case_page_for_docket.is_closed(): await case_page_for_docket.close()
                                court_logger.info(
                                    f"--- {log_prefix_docket} Finished processing this docket for attempt {attempt + 1} ---")
                                if len(docket_numbers) > 1:  # Small delay if processing multiple dockets
                                    await asyncio.sleep(self.config.get('delay_between_dockets_s', 0.5))

                        attempt_success_flag = True  # If loop completes, this attempt was successful
                        task_overall_success = True  # Mark overall task as successful
                        court_logger.info(
                            f"{log_prefix_task} Attempt {attempt + 1} completed processing all dockets successfully.")
                        self.logger.info(f"{log_prefix_task} Attempt {attempt + 1} succeeded for {court_id}")
                        break  # Exit from the attempt loop

                    except FileNotFoundError as fnf_err:
                        if "playwright/driver/node" in str(fnf_err) or "No such file or directory" in str(fnf_err):
                            last_critical_exception_str = (
                                f"Attempt {attempt + 1} FileNotFoundError at step '{current_step_for_attempt}': Playwright driver/node missing. "
                                f"Run 'playwright install'. Error: {str(fnf_err).splitlines()[0]}"
                            )
                            court_logger.critical(f"{log_prefix_task} {last_critical_exception_str}", exc_info=False)
                            self.logger.critical(f"{log_prefix_task} {last_critical_exception_str}")
                            task_overall_success = False  # Fatal error for this task
                            break  # Stop retrying this task
                        else:  # Other FileNotFoundError
                            last_critical_exception_str = f"Attempt {attempt + 1} FileNotFoundError at step '{current_step_for_attempt}': {type(fnf_err).__name__} - {str(fnf_err).splitlines()[0]}"
                            court_logger.error(f"{log_prefix_task} {last_critical_exception_str}", exc_info=True)
                            # This falls through to the generic Exception handler below for retry logic.
                            raise  # Re-raise to be caught by generic Exception for retry logic.

                    except Exception as current_attempt_e:  # Error for the entire attempt
                        last_critical_exception_str = f"Attempt {attempt + 1} Error at step '{current_step_for_attempt}': {type(current_attempt_e).__name__} - {str(current_attempt_e).splitlines()[0]}"
                        court_logger.error(
                            f"{log_prefix_task} Attempt {attempt + 1} failed critically: {last_critical_exception_str}",
                            exc_info=True)
                        self.logger.error(
                            f"{log_prefix_task} Attempt {attempt + 1} failed for {court_id}: {last_critical_exception_str}")
                        if main_navigator and main_navigator.is_ready and not (
                                main_page and main_page.is_closed()):  # type: ignore
                            await main_navigator.save_screenshot(  # type: ignore
                                f"multidocket_task_attempt_{attempt + 1}_critical_error")

                        attempt_success_flag = False
                        if "Target closed" in last_critical_exception_str or "Browser has been closed" in last_critical_exception_str:
                            court_logger.critical(
                                f"{log_prefix_task} Fatal browser/context error on attempt {attempt + 1}. Aborting retries.")
                            task_overall_success = False
                            break  # Exit attempt loop
                    finally:
                        if main_page and not main_page.is_closed(): await main_page.close()
                        if context:
                            court_logger.debug(f"{log_prefix_task} Attempt {attempt + 1}: Closing main context.")
                            await context.close()
                            context = None
                        court_logger.info(f"{log_prefix_task} Attempt {attempt + 1}/{max_attempts} cleanup finished.")

                    if not attempt_success_flag:
                        if not task_overall_success and (
                                "Playwright driver/node missing" in last_critical_exception_str or "Fatal browser/context error" in last_critical_exception_str):
                            self.logger.info(f"{log_prefix_task} Not retrying {court_id} due to fatal error.")
                            break  # Already broken from inner loop if fatal, ensures outer loop also breaks.

                        if attempt < max_attempts - 1:
                            court_logger.info(
                                f"{log_prefix_task} Attempt {attempt + 1} failed. Retrying in {5 * (attempt + 1)}s...")
                            self.logger.info(
                                f"{log_prefix_task} Attempt {attempt + 1} failed for {court_id}, retrying.")
                            await asyncio.sleep(5 * (attempt + 1))
                        else:
                            court_logger.error(
                                f"{log_prefix_task} All {max_attempts} attempts failed. Last error for task: {last_critical_exception_str}")
                            self.logger.error(f"{log_prefix_task} All {max_attempts} attempts failed for {court_id}.")
                            task_overall_success = False  # Explicitly mark as failed if all retries exhausted

                if not task_overall_success:  # If loop exited due to break from fatal error or all retries exhausted
                    court_logger.error(
                        f"{log_prefix_task} Task for court {court_id} concluded with failure. Last error: {last_critical_exception_str}")

            except FileNotFoundError as fnf_err_outer:  # Catch FileNotFoundError if it happens before attempt loop
                if "playwright/driver/node" in str(fnf_err_outer) or "No such file or directory" in str(fnf_err_outer):
                    task_overall_success = False
                    last_critical_exception_str = (
                        f"OuterTaskError FileNotFoundError: Playwright driver/node missing. "
                        f"Run 'playwright install'. Error: {str(fnf_err_outer).splitlines()[0]}"
                    )
                    court_logger.critical(f"{log_prefix_task} {last_critical_exception_str}", exc_info=False)
                    self.logger.critical(f"{log_prefix_task} {last_critical_exception_str}")
                else:  # Other FileNotFoundError
                    task_overall_success = False
                    last_critical_exception_str = f"OuterTaskError FileNotFoundError: {type(fnf_err_outer).__name__} - {str(fnf_err_outer).splitlines()[0]}"
                    court_logger.error(f"{log_prefix_task} {last_critical_exception_str}", exc_info=True)
                    self.logger.error(f"{log_prefix_task} {last_critical_exception_str}")
                if overall_counts["attempted_dockets"] == 0 and len(docket_numbers) > 0:
                    overall_counts["failed_docket_processing"] = len(
                        docket_numbers)  # All dockets failed if setup fails

            except Exception as task_e_outer:  # Catch-all for other errors before/outside attempt loop
                task_overall_success = False
                last_critical_exception_str = f"OuterTaskError: {type(task_e_outer).__name__} - {str(task_e_outer).splitlines()[0]}"
                court_logger.error(
                    f"{log_prefix_task} Unrecoverable error in multi-docket task structure: {task_e_outer}",
                    exc_info=True)
                self.logger.error(f"{log_prefix_task} Outer task error for {court_id}: {task_e_outer}")
                if overall_counts["attempted_dockets"] == 0 and len(docket_numbers) > 0:
                    overall_counts["failed_docket_processing"] = len(docket_numbers)
                    overall_counts["failed_docket_query"] = 0

            finally:
                court_logger.debug(
                    f"{log_prefix_task} Entering _process_multiple_dockets_for_court_task final cleanup (after all attempts).")
                if base_temp_downloads_for_court.exists():
                    try:
                        shutil.rmtree(base_temp_downloads_for_court)
                    except Exception as rmtree_err:
                        court_logger.warning(
                            f"{log_prefix_task} Failed to remove base temp dir {base_temp_downloads_for_court}: {rmtree_err}")

            if task_overall_success:
                court_logger.info(
                    f"{log_prefix_task} Task completed successfully for {court_id}. Final counts: {pformat(overall_counts)}")
                self.logger.info(f"{log_prefix_task} Task for {court_id} finished successfully.")
            else:
                court_logger.error(
                    f"{log_prefix_task} Task FAILED for {court_id}. Last critical error: {last_critical_exception_str}. Final counts: {pformat(overall_counts)}")
                self.logger.warning(
                    f"{log_prefix_task} Task for {court_id} FAILED. Last error: {last_critical_exception_str}")

            # Populate final_counts_to_return from overall_counts
            final_counts_to_return["attempted"] = overall_counts.get("attempted_dockets", 0)
            final_counts_to_return["successful"] = overall_counts.get("successful_dockets", 0)
            final_counts_to_return["downloaded"] = overall_counts.get("downloaded_dockets", 0)
            final_counts_to_return["failed"] = overall_counts.get("failed_docket_processing", 0) + \
                                               overall_counts.get("failed_docket_query", 0)
            # 'skipped_other_relevance' is used as a general skip category for multi-docket mode
            final_counts_to_return["skipped_other_relevance"] = overall_counts.get("skipped_dockets_existence", 0)
            # Ensure all expected keys are present in the returned dict for consistent logging by caller
            # Other keys like "skipped_date", etc., default to 0 as they are not applicable to multi-docket mode.

            return court_id, task_overall_success, final_counts_to_return

    @staticmethod
    async def _handle_court_specific_ui(navigator: PacerNavigator, court_id: str, court_logger: logging.Logger):
        """Handles known UI quirks for specific courts after ECF login."""
        log_prefix = f"[{court_id}] UIHandler:"
        court_logger.debug(f"{log_prefix} Checking for court-specific UI elements on page: {navigator.page.url}")
        try:
            if court_id == 'mdd':
                popup_div_selector = "div.popupAlert"
                close_button_selector = "#closePopupAlert"
                court_logger.info(f"{log_prefix} Court is MDD. Targeting popup '{popup_div_selector}'.")

                popup_loc = navigator.page.locator(popup_div_selector)
                popup_initially_visible = False
                try:
                    await popup_loc.first.wait_for(state="visible", timeout=7000)
                    popup_initially_visible = True
                    court_logger.info(f"{log_prefix} MDD popup '{popup_div_selector}' IS initially visible.")
                except PlaywrightTimeoutError:
                    court_logger.info(
                        f"{log_prefix} MDD popup '{popup_div_selector}' NOT initially visible within timeout. Assuming not present or already handled.")

                if popup_initially_visible:
                    try:
                        close_button_loc = navigator.page.locator(close_button_selector)
                        court_logger.info(
                            f"{log_prefix} Attempting to click MDD popup close button '{close_button_selector}'.")
                        await close_button_loc.first.click(timeout=10000)
                        court_logger.info(f"{log_prefix} Clicked MDD popup close button.")
                        court_logger.info(
                            f"{log_prefix} Waiting for MDD popup '{popup_div_selector}' to hide (JS: style.display == 'none' or element gone).")
                        await navigator.page.wait_for_function(
                            f"() => {{ const el = document.querySelector('{popup_div_selector}'); return !el || el.style.display === 'none'; }}",
                            timeout=10000
                        )
                        court_logger.info(f"{log_prefix} Confirmed MDD popup '{popup_div_selector}' is hidden.")

                    except PlaywrightTimeoutError as e_dismiss_timeout:
                        court_logger.error(
                            f"{log_prefix} Timeout while trying to dismiss VISIBLE MDD popup or confirming its dismissal: {str(e_dismiss_timeout).splitlines()[0]}")
                        is_still_truly_visible = False
                        if await popup_loc.count() > 0:
                            try:
                                if await popup_loc.first.is_visible(timeout=500):
                                    is_still_truly_visible = True
                            except PlaywrightError:
                                pass
                        if is_still_truly_visible:
                            court_logger.error(
                                f"{log_prefix} CRITICAL: MDD popup REMAINS VISIBLE after dismissal attempt. Raising error.")
                            await navigator.save_screenshot("mdd_popup_remains_visible_critical")
                            raise RuntimeError(
                                f"Failed to dismiss MDD court popup ({popup_div_selector}) for {court_id}. It remains visible.")
                        else:
                            court_logger.warning(
                                f"{log_prefix} MDD popup is no longer visible, even though a timeout occurred during dismissal/confirmation. Proceeding cautiously.")
                    except PlaywrightError as e_dismiss_general:
                        court_logger.error(
                            f"{log_prefix} CRITICAL PlaywrightError during dismissal of VISIBLE MDD popup: {str(e_dismiss_general).splitlines()[0]}",
                            exc_info=True)
                        await navigator.save_screenshot("mdd_popup_dismiss_playwright_error")
                        raise RuntimeError(
                            f"PlaywrightError dismissing MDD court popup ({popup_div_selector}) for {court_id}: {e_dismiss_general}")
            court_logger.debug(f"{log_prefix} Finished checking court-specific UI for {court_id}.")
        except RuntimeError:
            raise
        except Exception as e_unexp:
            court_logger.error(f"{log_prefix} Unexpected error in UI handler for {court_id}: {e_unexp}", exc_info=True)
            if navigator.is_ready: await navigator.save_screenshot("court_ui_handler_unexpected_error")

    @staticmethod
    async def _navigate_to_court_ecf(navigator: PacerNavigator, court_id: str,
                                     court_logger: logging.Logger) -> bool:
        """Navigates from PACER main site to the specific court's ECF login page."""
        log_prefix = f"[{court_id}]"
        lookup_url = "https://pacer.uscourts.gov/file-case/court-cmecf-lookup"
        court_logger.info(f"{log_prefix} Navigating to Court CM/ECF Lookup page: {lookup_url}")
        try:
            if navigator.page.url.rstrip('/') != lookup_url.rstrip('/'):
                await navigator.goto(lookup_url, timeout_override_ms=navigator.timeout + 20000)
                await navigator.page.wait_for_load_state('domcontentloaded', timeout=15000)
                if navigator.page.url.rstrip('/') != lookup_url.rstrip('/'):
                    court_logger.error(
                        f"{log_prefix} Failed to land on ECF lookup page. Current URL: {navigator.page.url}")
                    await navigator.save_screenshot("nav_ecf_lookup_redirect")
                    return False

            court_id_lower = court_id.lower()
            court_link_selectors = [
                f"a[href*='//ecf.{court_id_lower}.uscourts.gov' i]",
                f"a[href*='//pacer.{court_id_lower}.uscourts.gov' i]",
                f"a[href*='//{court_id_lower}.uscourts.gov' i]",
                f"a:has-text('{court_id_lower.upper()}')"
            ]
            for selector in court_link_selectors:
                try:
                    potential_locator = await navigator.locator(selector)
                    if await potential_locator.count() > 0:
                        link_locator = potential_locator.first
                        await link_locator.wait_for(state='visible', timeout=15000)
                        court_logger.info(f"{log_prefix} Found court link for {court_id}. Clicking...")
                        await link_locator.click()
                        await navigator.page.wait_for_load_state('domcontentloaded', timeout=navigator.timeout + 15000)
                        court_logger.info(
                            f"{log_prefix} Successfully navigated to court {court_id} page URL: {navigator.page.url}")
                        return True
                except (PlaywrightError, PlaywrightTimeoutError) as e_sel:
                    court_logger.debug(f"{log_prefix} Selector '{selector}' failed: {e_sel}")

            court_logger.error(f"{log_prefix} Failed to find/click court link for {court_id}.")
            await navigator.save_screenshot(f"nav_court_link_fail_{court_id}")
            return False
        except PlaywrightError as e:
            court_logger.error(f"{log_prefix} Error during navigation to/interaction with ECF Lookup page: {e}",
                               exc_info=True)
            await navigator.save_screenshot("nav_ecf_lookup_fail")
            return False
        except Exception as e_unexp:
            court_logger.error(f"{log_prefix} Unexpected error navigating to ECF Lookup page: {e_unexp}", exc_info=True)
            await navigator.save_screenshot("nav_ecf_lookup_fail_unexpected")
            return False

    @staticmethod
    async def _login_to_court_ecf(navigator: PacerNavigator, court_id: str, court_logger: logging.Logger) -> bool:
        """Clicks the ECF login button on the specific court's page."""
        log_prefix = f"[{court_id}]"
        court_logger.info(
            f"{log_prefix} Attempting to log into court ECF from URL: {navigator.page.url}")

        try:
            await navigator.page.wait_for_selector("body", state="visible", timeout=5000)
            court_logger.debug(f"{log_prefix} Body element is visible.")
        except PlaywrightTimeoutError:
            court_logger.warning(
                f"{log_prefix} Timeout waiting for body visibility before ECF login. Page might be slow or incorrect.")
            await navigator.save_screenshot("ecf_login_body_timeout")

        ecf_login_selectors = [
            "a.usa-button[href*='login']", "a.login", "a:has-text('CM/ECF Login')",
            "a:has-text('Document Filing System')", "a:text('NextGen CM/ECF Login')",
            "area[alt='Click here to login']", "#loginLink", "button:text-matches('/login/i')",
            "a[href*='aws.uscourts.gov/']",
            "a:text('Log into CM/ECF')",
            "a[title*='Login to CM/ECF']"
        ]

        for i, selector in enumerate(ecf_login_selectors):
            try:
                court_logger.debug(f"{log_prefix} Trying ECF login selector #{i + 1}: {selector}")
                locator = await navigator.locator(selector)
                if await locator.count() > 0:
                    await locator.first.wait_for(state='visible', timeout=7000)
                    court_logger.info(f"{log_prefix} Element for selector '{selector}' is visible. Attempting click.")
                    await navigator.click(selector, wait_for_nav=True,
                                          timeout_override_ms=navigator.timeout + 10000)
                    court_logger.info(
                        f"{log_prefix} Clicked court ECF login link using selector: {selector}. New URL: {navigator.page.url}")
                    await asyncio.sleep(1)
                    return True
                else:
                    court_logger.debug(
                        f"{log_prefix} Selector '{selector}' did not find any elements on initial count.")
            except PlaywrightTimeoutError:
                court_logger.debug(f"{log_prefix} ECF login selector '{selector}' not visible within 7s.")
            except PlaywrightError as e:
                court_logger.debug(
                    f"{log_prefix} ECF login selector failed: {selector} ({type(e).__name__} - {str(e).splitlines()[0]})")
                if "Target page, context or browser has been closed" in str(e):
                    court_logger.error(f"{log_prefix} Target closed during ECF login attempt. Aborting for this court.")
                    return False
                if not isinstance(e, PlaywrightTimeoutError):
                    await navigator.save_screenshot(f"ecf_login_click_fail_{i + 1}")

        court_logger.error(f"{log_prefix} All ECF login selectors failed on page: {navigator.page.url}")
        await navigator.save_screenshot("ecf_login_all_selectors_failed")
        return False

    # --- Refactored Row Processing ---

    @staticmethod
    async def _get_cleaned_docket_number(docket_link_loc: Locator, row_num: int, log_prefix: str,
                                         court_logger: logging.Logger) -> Optional[
        str]:
        """Extracts and cleans the docket number from the link locator."""
        try:
            docket_num_raw = await docket_link_loc.text_content(timeout=5000) or ""
            docket_num_raw = docket_num_raw.strip()
            if not docket_num_raw:
                court_logger.warning(f"{log_prefix} Row {row_num}: Extracted docket number is empty. Skipping.")
                return None

            match = re.search(r'(\d:\d{2}-\w+-\d{1,5})', docket_num_raw)
            if match:
                docket_num_cleaned = match.group(1)
                if docket_num_cleaned != docket_num_raw:
                    court_logger.debug(
                        f"{log_prefix} Row {row_num}: Cleaned docket num '{docket_num_raw}' -> '{docket_num_cleaned}'")
            else:
                docket_num_cleaned = docket_num_raw
                court_logger.debug(
                    f"{log_prefix} Row {row_num}: Using raw docket num '{docket_num_raw}' as no cleaning pattern matched.")

            return docket_num_cleaned
        except PlaywrightError as e:
            court_logger.error(f"{log_prefix} Row {row_num}: Error getting docket number text: {e}", exc_info=True)
            return None

    async def _check_docket_existence_and_relevance(
            self, court_id: str, docket_num: str, versus_loc: Locator,
            start_date_obj: DateType,
            end_date_obj: DateType,
            row_num: int, log_prefix: str,
            added_date_iso_for_filemanager: str,
            row_filing_date_obj: Optional[DateType],
            court_logger: logging.Logger
    ) -> ExistenceCheckResult:
        """
        Checks if a docket already exists and should be skipped.
        Allows GSI check to be bypassed if the row's filing date is the same as the run's end_date.
        """
        try:
            versus_text_for_filename = await versus_loc.text_content(timeout=2000) or "UnknownVersus"
        except PlaywrightError:
            versus_text_for_filename = "UnknownVersus"

        base_filename = self.file_manager.create_base_filename_static(court_id, docket_num,
                                                                      versus_text_for_filename.strip())

        if self.pacer_db and hasattr(self.pacer_db, 'check_docket_exists_by_id_num'):
            docket_exists_in_gsi = await asyncio.to_thread(self.pacer_db.check_docket_exists_by_id_num, court_id,
                                                           docket_num)
            if docket_exists_in_gsi:
                if row_filing_date_obj and row_filing_date_obj == end_date_obj:
                    court_logger.info(
                        f"{log_prefix} Row {row_num} ({docket_num}): Found in PacerDB (GSI), but its filing date ({row_filing_date_obj.strftime('%Y-%m-%d')}) "
                        f"matches run's end date ({end_date_obj.strftime('%Y-%m-%d')}). Bypassing GSI skip to re-check/re-process."
                    )
                else:
                    court_logger.info(
                        f"{log_prefix} Row {row_num} ({docket_num}): Found in PacerDB (GSI). Filing date ({row_filing_date_obj.strftime('%Y-%m-%d') if row_filing_date_obj else 'N/A'}) "
                        f"does not match run's end date ({end_date_obj.strftime('%Y-%m-%d')}). SKIPPING."
                    )
                    return ExistenceCheckResult(should_skip=True, reason="skipped_exists_db_gsi",
                                                base_filename=base_filename)

        if await self.file_manager.check_if_downloaded_across_dates(
                start_date_obj, end_date_obj, base_filename
        ):
            court_logger.info(
                f"{log_prefix} Row {row_num} ({docket_num}): Not in DB (or GSI skip bypassed), but found fully downloaded (JSON+PDF/ZIP) across run's date range. SKIPPING.")
            return ExistenceCheckResult(should_skip=True, reason="skipped_exists_local_date_range",
                                        base_filename=base_filename)

        existing_json_data_today = await self.file_manager.load_json_if_exists(added_date_iso_for_filemanager,
                                                                               base_filename)
        if existing_json_data_today:
            if '_reason_irrelevant' in existing_json_data_today:
                reason = existing_json_data_today['_reason_irrelevant']
                court_logger.info(
                    f"{log_prefix} Row {row_num} ({docket_num}): Local JSON for today exists and is marked irrelevant: {reason}. SKIPPING.")
                return ExistenceCheckResult(should_skip=True, reason="skipped_irrelevant_local_json_today",
                                            base_filename=base_filename)
            if existing_json_data_today.get('s3_link') or existing_json_data_today.get('is_downloaded') == True:
                s3_link_exists = bool(existing_json_data_today.get('s3_link'))
                is_downloaded_flag = existing_json_data_today.get('is_downloaded') == True
                court_logger.info(
                    f"{log_prefix} Row {row_num} ({docket_num}): Local JSON for today exists and indicates already downloaded/processed (s3_link: {s3_link_exists}, is_downloaded_flag: {is_downloaded_flag}). SKIPPING.")
                return ExistenceCheckResult(should_skip=True, reason="skipped_downloaded_local_json_today",
                                            base_filename=base_filename)

        court_logger.info(
            f"{log_prefix} Row {row_num} ({docket_num}): Not skipped by GSI (or bypassed), not fully downloaded across range, and not marked irrelevant/downloaded locally today. Proceeding.")
        return ExistenceCheckResult(should_skip=False, reason=None, base_filename=base_filename)

    @staticmethod
    async def _is_date_in_range(date_cell_loc: Locator, allowed_iso_dates: Set[str], row_num: int,
                                log_prefix: str, docket_num_cleaned: str,
                                court_logger: logging.Logger) -> DateCheckResult:
        """Extracts date from cell and checks if it falls within the allowed range."""
        try:
            date_text_raw = await date_cell_loc.text_content(timeout=5000) or ""
            date_text_cleaned = date_text_raw.strip()
            date_match = re.search(r"(?:Filed:?\s*)?(\d{1,2}/\d{1,2}/\d{2,4})", date_text_cleaned, re.IGNORECASE)

            if not date_match:
                court_logger.warning(
                    f"{log_prefix} Row {row_num} ({docket_num_cleaned}): Could not find date pattern in '{date_text_cleaned[:50]}...'.")
                return DateCheckResult(is_in_range=False, filing_date_obj=None, filing_date_for_processor=None)

            row_date_str = date_match.group(1)
            try:
                row_date_obj = parse_date_str(row_date_str)
                if row_date_obj:
                    row_date_iso = row_date_obj.strftime('%Y%m%d')
                    filing_date_iso_fmt_for_processor = row_date_obj.strftime('%Y-%m-%d')
                    if row_date_iso not in allowed_iso_dates:
                        court_logger.info(
                            f"{log_prefix} Row {row_num} ({docket_num_cleaned}): Filed date {row_date_str} ({row_date_iso}) outside allowed range. Skipping.")
                        return DateCheckResult(is_in_range=False, filing_date_obj=row_date_obj,
                                               filing_date_for_processor=filing_date_iso_fmt_for_processor)
                    else:
                        court_logger.debug(
                            f"{log_prefix} Row {row_num} ({docket_num_cleaned}): Extracted date {filing_date_iso_fmt_for_processor} is within range.")
                        return DateCheckResult(is_in_range=True, filing_date_obj=row_date_obj,
                                               filing_date_for_processor=filing_date_iso_fmt_for_processor)
                else:
                    court_logger.warning(
                        f"{log_prefix} Row {row_num} ({docket_num_cleaned}): Could not parse extracted date string '{row_date_str}'.")
                    return DateCheckResult(is_in_range=False, filing_date_obj=None, filing_date_for_processor=None)
            except ValueError:
                court_logger.warning(
                    f"{log_prefix} Row {row_num} ({docket_num_cleaned}): Invalid date format in extracted string '{row_date_str}'.")
                return DateCheckResult(is_in_range=False, filing_date_obj=None, filing_date_for_processor=None)
        except PlaywrightError as e:
            court_logger.error(
                f"{log_prefix} Row {row_num} ({docket_num_cleaned}): Error getting date cell content: {e}",
                exc_info=True)
            return DateCheckResult(is_in_range=False, filing_date_obj=None, filing_date_for_processor=None)

    async def _try_click_run_report(self, page: Page, navigator: PacerNavigator, log_prefix: str,
                                    screenshot_name_prefix: str, court_logger: logging.Logger) -> bool:
        """
        Attempts to find and click a 'Run Report' button, trying standard click then JS fallback.
        Returns True if a click action leading to navigation was successful, False otherwise.
        Assumes `page` and `navigator` are for the page where the button might exist.
        """
        run_report_selector = "form input[name='button1'][value='Run Report']"
        court_logger.debug(
            f"{log_prefix} Attempting to find and click 'Run Report' button with selector '{run_report_selector}' on {page.url}.")

        rr_button_loc_coll = await navigator.locator(run_report_selector)
        if await rr_button_loc_coll.count() == 0:
            court_logger.debug(f"{log_prefix} 'Run Report' button not found by selector.")
            return False

        rr_button_loc = rr_button_loc_coll.first
        try:
            await rr_button_loc.wait_for(state='visible', timeout=7000)
            court_logger.info(f"{log_prefix} 'Run Report' button found and visible. Attempting standard click...")
            async with page.expect_navigation(wait_until="domcontentloaded",
                                              timeout=self.browser_service.timeout_ms + 30000):
                await rr_button_loc.click(timeout=10000)
            court_logger.info(f"{log_prefix} Standard click on 'Run Report' successful. New URL: {page.url}")
            await page.wait_for_timeout(1000)
            return True
        except PlaywrightError as e_click:
            court_logger.warning(
                f"{log_prefix} Standard click on '{run_report_selector}' failed: {e_click}. Attempting direct form submission via JS.")
            await navigator.save_screenshot(f"{screenshot_name_prefix}_standard_click_fail")
            try:
                court_logger.info(f"{log_prefix} Attempting direct JS form submission (document.forms[0].submit()).")
                async with page.expect_navigation(wait_until="domcontentloaded",
                                                  timeout=self.browser_service.timeout_ms + 30000):
                    await page.evaluate(
                        "() => { if(document.forms[0]) { document.forms[0].submit(); } else { throw new Error('No form found to submit for Run Report');} }")
                court_logger.info(
                    f"{log_prefix} JavaScript direct form submission for 'Run Report' appears successful. New URL: {page.url}")
                await page.wait_for_timeout(1000)
                return True
            except PlaywrightError as e_js_submit:
                court_logger.error(
                    f"{log_prefix} JavaScript direct form submission for 'Run Report' failed: {e_js_submit}.")
                await navigator.save_screenshot(f"{screenshot_name_prefix}_js_submit_fail")
            except Exception as e_js_eval:
                court_logger.error(
                    f"{log_prefix} Unexpected error during JS direct form submission for 'Run Report': {e_js_eval}.")
                await navigator.save_screenshot(f"{screenshot_name_prefix}_js_eval_fail")
        return False

    async def _prepare_and_open_case_page(self, docket_link_loc: Locator, original_page: Page, context: BrowserContext,
                                          row_num: int, log_prefix_outer: str, docket_num_cleaned: str,
                                          base_screenshot_dir_for_row: Path, court_logger: logging.Logger) -> Optional[
        Page]:
        current_page: Optional[Page] = None
        docket_link_href: Optional[str] = None
        log_prefix = f"{log_prefix_outer} PrepOpen:"

        try:
            docket_link_href = await docket_link_loc.get_attribute('href', timeout=5000)
            if not docket_link_href:
                court_logger.error(f"{log_prefix} Row {row_num}: Skipping, failed to get href from docket link.")
                return None

            from urllib.parse import urljoin
            docket_link_href = urljoin(original_page.url, docket_link_href)

            court_logger.debug(
                f"{log_prefix} Row {row_num}: Creating new page for docket link, navigating to: {docket_link_href}")
            current_page = await context.new_page()
            await current_page.goto(docket_link_href, wait_until='domcontentloaded',
                                    timeout=self.browser_service.timeout_ms + 25000)
            court_logger.info(
                f"{log_prefix} Row {row_num}: Opened initial page from docket link: {current_page.url} (Title: '{await current_page.title()}')")

            current_navigator = PacerNavigator(current_page, self.config.get('delay', self.DEFAULT_DELAY_S),
                                               self.browser_service.timeout_ms)
            current_navigator.set_screenshot_dir(str(base_screenshot_dir_for_row / "01_after_docket_link_click"))
            await current_navigator.save_screenshot("page_content")

            court_logger.info(
                f"{log_prefix} Row {row_num}: Attempting direct 'Run Report' (S1RR) on {current_page.url}.")
            if await self._try_click_run_report(current_page, current_navigator,
                                                f"{log_prefix} [S1RR]", "s1_run_report", court_logger):
                court_logger.info(
                    f"{log_prefix} Row {row_num}: Direct 'Run Report' (S1RR) successful. Final page: {current_page.url}")
                return current_page

            # S1RR failed. current_page (from docket_link_href) is suspect.
            court_logger.info(
                f"{log_prefix} Row {row_num}: S1RR failed. Looking for 'Docket Report' link (DRL) on {current_page.url}.")

            dr_link_selectors = [
                "a:text-matches('/Docket Report/i')", "a:has-text('Docket Report')",
                "//a[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'docket report')]"
            ]
            found_and_clicked_dr_link = False

            # Keep a reference to the page S1RR failed on, in case DRL click opens a new tab.
            page_s1rr_failed_on = current_page

            for dr_sel_idx, dr_sel in enumerate(dr_link_selectors):
                if current_page.is_closed():  # Check if page_s1rr_failed_on (or its replacement) got closed unexpectedly
                    court_logger.error(
                        f"{log_prefix} Row {row_num}: Page closed before DRL attempt with selector {dr_sel}. Aborting DRL search.")
                    return None  # page_s1rr_failed_on should have been closed if it was the one that became invalid

                dr_link_loc_coll = await current_navigator.locator(dr_sel)  # current_navigator is for current_page
                if await dr_link_loc_coll.count() == 0:
                    continue

                dr_link_loc_instance = dr_link_loc_coll.first
                try:
                    await dr_link_loc_instance.wait_for(state='visible', timeout=7000)
                    court_logger.info(f"{log_prefix} Row {row_num}: Found 'Docket Report' link ({dr_sel}). Clicking...")

                    target_attr = await dr_link_loc_instance.get_attribute("target")
                    is_new_tab_link = target_attr and target_attr.lower() == "_blank"

                    page_after_dr_click_event: Optional[Page] = None
                    if is_new_tab_link:
                        async with context.expect_page(
                                timeout=self.browser_service.timeout_ms + 10000) as new_page_event:
                            await dr_link_loc_instance.click(timeout=10000)
                        page_after_dr_click_event = await new_page_event.value
                        await page_after_dr_click_event.wait_for_load_state("domcontentloaded",
                                                                            timeout=self.browser_service.timeout_ms + 20000)
                        # Close the page that S1RR failed on, as we have a new tab from DRL.
                        if page_s1rr_failed_on and not page_s1rr_failed_on.is_closed():
                            await page_s1rr_failed_on.close()
                        current_page = page_after_dr_click_event  # Update current_page to the new tab
                    else:  # Same page navigation
                        async with current_page.expect_navigation(wait_until="domcontentloaded",
                                                                  timeout=self.browser_service.timeout_ms + 30000):
                            await dr_link_loc_instance.click(timeout=10000)
                        # current_page is now the navigated page. page_s1rr_failed_on is the same object.

                    found_and_clicked_dr_link = True
                    court_logger.info(
                        f"{log_prefix} Row {row_num}: Clicked DRL. New current page: {current_page.url}")

                    # Update navigator for the (potentially new) current_page
                    current_navigator = PacerNavigator(current_page, self.config.get('delay', self.DEFAULT_DELAY_S),
                                                       self.browser_service.timeout_ms)
                    current_navigator.set_screenshot_dir(
                        str(base_screenshot_dir_for_row / "02_after_docket_report_link_click"))
                    await current_navigator.save_screenshot("page_content")
                    break  # Exit DRL selector loop

                except PlaywrightError as e_dr_click:
                    court_logger.warning(
                        f"{log_prefix} Row {row_num}: DRL ({dr_sel}) click/visibility failed: {e_dr_click}")
                    await current_navigator.save_screenshot(f"s2_dr_link_click_fail_{dr_sel_idx}")

            # After DRL attempts:
            if found_and_clicked_dr_link:
                # current_page is now the one resulting from the DRL click.
                court_logger.info(
                    f"{log_prefix} Row {row_num}: Attempting 'Run Report' (S2aRR) on {current_page.url} (after DRL).")
                if await self._try_click_run_report(current_page, current_navigator,
                                                    f"{log_prefix} [S2aRR]", "s2a_run_report", court_logger):
                    court_logger.info(
                        f"{log_prefix} Row {row_num}: S2aRR successful after DRL. Final page: {current_page.url}")
                    return current_page
                else:
                    # S2aRR failed. The page after DRL is also bad.
                    court_logger.error(f"{log_prefix} Row {row_num}: S2aRR failed after DRL. Closing this page.")
                    if current_page and not current_page.is_closed(): await current_page.close()
                    return None
            else:
                # DRL not found/actioned. S1RR had already failed on page_s1rr_failed_on.
                court_logger.error(
                    f"{log_prefix} Row {row_num}: S1RR failed AND DRL not found/actioned. Closing initial page.")
                if page_s1rr_failed_on and not page_s1rr_failed_on.is_closed():
                    await page_s1rr_failed_on.close()
                return None

        except PlaywrightError as page_err:
            court_logger.error(
                f"{log_prefix} Row {row_num}: PlaywrightError during page preparation for {docket_link_href or 'N/A'}: {page_err}",
                exc_info=True)  # Keep exc_info for detailed logging

            # Save screenshot if possible
            page_to_screenshot = current_page if current_page and not current_page.is_closed() else \
                (
                    page_s1rr_failed_on if 'page_s1rr_failed_on' in locals() and page_s1rr_failed_on and not page_s1rr_failed_on.is_closed() else None)
            if page_to_screenshot:
                nav_err_ss = PacerNavigator(page_to_screenshot)
                nav_err_ss.set_screenshot_dir(str(base_screenshot_dir_for_row / "error_screenshots"))
                await nav_err_ss.save_screenshot("playwright_error_in_prep")

            # Close pages
            if current_page and not current_page.is_closed(): await current_page.close()
            if 'page_s1rr_failed_on' in locals() and page_s1rr_failed_on and not page_s1rr_failed_on.is_closed() and page_s1rr_failed_on != current_page:
                await page_s1rr_failed_on.close()

            # Re-raise if critical for retry mechanism
            err_str = str(page_err).lower()
            if "net::err_connection_reset" in err_str or \
                    "net::err_name_not_resolved" in err_str or \
                    "net::err_timed_out" in err_str or \
                    "net::err_address_unreachable" in err_str or \
                    "target page, context or browser has been closed" in err_str or \
                    "execution context was destroyed" in err_str:
                court_logger.info(
                    f"{log_prefix} Critical PlaywrightError detected. Re-raising for potential court task retry.")
                raise  # Re-raise the original PlaywrightError
            return None  # For non-critical errors, still return None

        except Exception as page_ex:  # General exceptions
            court_logger.error(
                f"{log_prefix} Row {row_num}: Unexpected error during page preparation for {docket_link_href or 'N/A'}: {page_ex}",
                exc_info=True)
            # Screenshot and close logic similar to PlaywrightError
            page_to_screenshot = current_page if current_page and not current_page.is_closed() else \
                (
                    page_s1rr_failed_on if 'page_s1rr_failed_on' in locals() and page_s1rr_failed_on and not page_s1rr_failed_on.is_closed() else None)
            if page_to_screenshot:
                nav_err_ss = PacerNavigator(page_to_screenshot)
                nav_err_ss.set_screenshot_dir(str(base_screenshot_dir_for_row / "error_screenshots"))
                await nav_err_ss.save_screenshot("unexpected_error_in_prep")

            if current_page and not current_page.is_closed(): await current_page.close()
            if 'page_s1rr_failed_on' in locals() and page_s1rr_failed_on and not page_s1rr_failed_on.is_closed() and page_s1rr_failed_on != current_page:
                await page_s1rr_failed_on.close()
            # Consider if general exceptions should also be re-raised if they imply systemic issues
            # For now, let's assume most general exceptions here are row-specific.
            return None

    async def _process_single_row_docket(self, case_page: Page, initial_details: Dict[str, Any], court_id: str,
                                         iso_date: str,
                                         start_date_obj: DateType, end_date_obj: DateType, row_num: int,
                                         log_prefix: str,
                                         screenshot_dir: Path, processor_config_for_dockets: Dict[str, Any],
                                         court_logger: logging.Logger) -> Tuple[
        Optional[Dict[str, Any]], bool]:
        processed_details: Optional[Dict[str, Any]] = None
        is_downloaded = False
        try:
            case_navigator = PacerNavigator(case_page, processor_config_for_dockets.get('delay', self.DEFAULT_DELAY_S),
                                            self.browser_service.timeout_ms)
            case_navigator.set_screenshot_dir(str(screenshot_dir))

            async with DocketProcessor(
                    court_id, iso_date, start_date_obj, end_date_obj,
                    case_navigator, self.file_manager, self.pacer_db, self.dc_db,
                    self.s3_manager, self.gpt_interface, processor_config_for_dockets,
                    logger=court_logger  # Pass court_logger
            ) as row_processor:
                processed_details = await row_processor.process_docket_page(initial_details)
                is_downloaded = row_processor.is_downloaded
                return processed_details, is_downloaded

        except Exception as proc_err:
            court_logger.error(
                f"{log_prefix} Row {row_num} ({initial_details.get('docket_num', 'UNKNOWN')}): Error during DocketProcessor execution: {proc_err}",
                exc_info=True)
            if case_page and not case_page.is_closed():
                try:
                    screenshot_name = f"row_{row_num}_docket_processor_error_{uuid.uuid4().hex[:4]}.png"
                    await case_page.screenshot(path=screenshot_dir / screenshot_name, full_page=True)
                except PlaywrightError as ss_err:
                    court_logger.warning(
                        f"{log_prefix} Row {row_num}: Failed to take screenshot after DocketProcessor error: {ss_err}")
            return None, False

    @staticmethod
    async def _cleanup_row_resources(case_page: Optional[Page], row_num: int, log_prefix: str,
                                     court_logger: logging.Logger):
        """Closes the case page if it exists and is open."""
        if case_page and not case_page.is_closed():
            try:
                await case_page.close()
                court_logger.debug(f"{log_prefix} Row {row_num}: Closed case page tab.")
            except PlaywrightError as e_close:
                court_logger.warning(f"{log_prefix} Row {row_num}: Error closing case page: {e_close}")
        else:
            court_logger.debug(f"{log_prefix} Row {row_num}: No case page tab to close (or already closed).")

    @staticmethod
    async def _extract_row_elements(row_locator: Locator, row_num: int, log_prefix: str,
                                    court_logger: logging.Logger) -> Optional[RowElements]:
        """Extracts key locators and additional data from a report row."""
        try:
            await row_locator.wait_for(state='attached', timeout=5000)
            cell_locators = await row_locator.locator("td").all()
            if len(cell_locators) < 2:
                court_logger.warning(f"{log_prefix} Row {row_num}: Found fewer than 2 cells. Skipping row.")
                return None

            docket_link_loc = cell_locators[0].locator("a").first
            versus_loc = cell_locators[0].locator("b").first
            date_cell_loc = cell_locators[1]

            if await docket_link_loc.count() == 0:
                court_logger.warning(f"{log_prefix} Row {row_num}: Could not find docket link element. Skipping.")
                return None

            cause_str: Optional[str] = None
            nos_str: Optional[str] = None
            case_flags_str: Optional[str] = None

            if len(cell_locators) >= 4:
                details_cell_index = 3
                details_cell_loc = cell_locators[details_cell_index]
                try:
                    inner_text = await details_cell_loc.evaluate("el => el.innerText") or ""
                    court_logger.debug(
                        f"{log_prefix} Row {row_num}, Cell {details_cell_index + 1} innerText: '{inner_text[:200]}...'")
                    lines = inner_text.splitlines()
                    for line in lines:
                        line_clean = line.strip()
                        if line_clean.lower().startswith('cause:'):
                            cause_str = line_clean[len('cause:'):].strip()
                        elif line_clean.lower().startswith('nos:'):
                            nos_str = line_clean[len('nos:'):].strip()
                        elif line_clean.lower().startswith('case flags:'):
                            case_flags_str = line_clean[len('case flags:'):].strip()

                    if not cause_str or not nos_str or not case_flags_str:
                        html_content = await details_cell_loc.inner_html()
                        court_logger.debug(
                            f"{log_prefix} Row {row_num}, Cell {details_cell_index + 1} HTML for fallback: '{html_content[:200]}...'")
                        if not cause_str:
                            cause_match = re.search(r"<i>Cause:</i>\s*([^<]+)<br>", html_content, re.IGNORECASE)
                            if cause_match: cause_str = cause_match.group(1).strip()
                        if not nos_str:
                            nos_match = re.search(r"<i>NOS:</i>\s*([^<]+)<br>", html_content, re.IGNORECASE)
                            if nos_match: nos_str = nos_match.group(1).strip()
                        if not case_flags_str:
                            flags_match = re.search(r"<i>Case flags:</i>\s*([^<]+)(?:<br>|$)", html_content,
                                                    re.IGNORECASE)
                            if flags_match: case_flags_str = flags_match.group(1).strip()
                except Exception as e_detail_extract:
                    court_logger.warning(
                        f"{log_prefix} Row {row_num}: Error extracting Cause/NOS/Flags from cell {details_cell_index + 1}: {e_detail_extract}")
            else:
                court_logger.debug(
                    f"{log_prefix} Row {row_num}: Less than 4 cells, Cause/NOS/Flags might be missing or in other cells.")

            if cause_str or nos_str or case_flags_str:
                court_logger.info(
                    f"{log_prefix} Row {row_num}: Extracted - Cause: '{cause_str}', NOS: '{nos_str}', Case Flags: '{case_flags_str}'")

            return RowElements(
                docket_link_loc=docket_link_loc,
                versus_loc=versus_loc,
                date_cell_loc=date_cell_loc,
                cause=cause_str,
                nos=nos_str,
                case_flags=case_flags_str
            )
        except PlaywrightError as e:
            court_logger.error(f"{log_prefix} Row {row_num}: Error extracting basic elements: {e}", exc_info=True)
            return None

    async def _process_single_court_task(
            self,
            semaphore: asyncio.Semaphore,
            court_id: str,
            iso_date: str,
            start_date_obj: DateType,
            end_date_obj: DateType,
            from_date_pacer_fmt: str,
            to_date_pacer_fmt: str,
            max_attempts: int
    ) -> Tuple[str, bool, Dict[str, int]]:
        async with semaphore:
            log_prefix_console = f"[{court_id}] SingleCourtTask:"  # For self.logger (console)
            self.logger.info(f"{log_prefix_console} Starting task for court {court_id}...")

            # Setup court-specific file logger
            log_file_path = Path(self.config.get('data_path', get_default_data_path(
                self.config))) / iso_date / "logs" / f"pacer_{court_id}.log"
            court_logger_name = f"pacer_run.{court_id}.report"  # Specific name for report scraping task
            court_logger = _setup_court_file_logger(court_logger_name, log_file_path)
            # Use a log_prefix for messages within the court_logger file, for consistency if desired
            log_prefix_file = f"[{court_id}] SingleCourtReportTask:"
            court_logger.info(f"{log_prefix_file} Detailed logging for this report task started.")

            context: Optional[BrowserContext] = None
            page: Optional[Page] = None

            expected_keys_for_log = [
                "attempted", "successful", "downloaded", "skipped_date",
                "skipped_exists_db_gsi", "skipped_exists_local_date_range",
                "skipped_irrelevant_local_json_today", "skipped_downloaded_local_json_today",
                "skipped_report_data_irrelevant", "skipped_other_relevance",
                "skipped_historical_irrelevant", "skipped_nos_defendant_check", "skipped_md_case",  # Added MD
                "failed", "failed_extract_elements", "failed_get_docket_num"  # Added these
            ]
            attempt_counts = {key: 0 for key in expected_keys_for_log}
            task_overall_success = False
            last_critical_exception_str = "No critical exception"

            base_temp_downloads_for_court_attempts = self.file_manager.base_data_dir / iso_date / "dockets" / "temp" / f"{court_id}_ctx_dl_report"

            for attempt in range(max_attempts):
                self.logger.info(f"{log_prefix_console} Attempt {attempt + 1}/{max_attempts} for {court_id}")
                court_logger.info(f"{log_prefix_file} Attempt {attempt + 1}/{max_attempts}")
                attempt_success_flag = False

                attempt_specific_download_path_str = str(
                    base_temp_downloads_for_court_attempts / f"attempt_{attempt + 1}_{uuid.uuid4().hex[:8]}")
                court_logger.debug(
                    f"{log_prefix_file} Attempt {attempt + 1} will use download path: {attempt_specific_download_path_str}")

                processor_config = self.config.copy()
                processor_config['context_download_path'] = attempt_specific_download_path_str

                navigator: Optional[PacerNavigator] = None
                current_step = "Initialization"
                has_cases = False # Initialize has_cases for broader scope

                try:
                    current_step = "BrowserContextCreation"
                    court_logger.info(
                        f"{log_prefix_file} Attempt {attempt + 1}: Creating browser context with download_path='{attempt_specific_download_path_str}'...")
                    context = await self.browser_service.new_context(
                        download_path=attempt_specific_download_path_str
                    )
                    page = await context.new_page()
                    court_logger.info(f"{log_prefix_file} Browser context and page created successfully.")

                    current_step = "ScreenshotDirSetup"
                    screenshot_dir = Path(self.config.get('data_path',
                                                          get_default_data_path(
                                                              self.config))) / iso_date / 'screenshots' / f"{court_id}_attempt_{attempt + 1}"
                    await asyncio.to_thread(screenshot_dir.mkdir, parents=True,
                                            exist_ok=True)

                    current_step = "NavigatorSetup"
                    navigator = PacerNavigator(page, processor_config.get('delay', self.DEFAULT_DELAY_S),
                                               self.browser_service.timeout_ms)
                    navigator.set_screenshot_dir(str(screenshot_dir))

                    current_step = "AuthenticatorCreation"
                    authenticator = PacerAuthenticator(navigator, processor_config['username_prod'],
                                                       processor_config['password_prod'],
                                                       logger=court_logger)  # Pass court_logger

                    current_step = "ECFLoginSequence"
                    court_logger.info(f"{log_prefix_file} Attempt {attempt + 1}: Performing ECF login sequence...")
                    if not await self._perform_ecf_login_sequence(navigator, court_id, authenticator, court_logger):
                        raise Exception("ECF Login Sequence Failed")
                    court_logger.info(f"{log_prefix_file} Attempt {attempt + 1}: ECF login sequence successful.")

                    current_step = "ReportHandlerCreation"
                    report_handler = ReportHandler(navigator, court_id, from_date_pacer_fmt, to_date_pacer_fmt,
                                                   logger=court_logger)  # Pass court_logger

                    current_step = "NavigateToCaseFiledReport"
                    court_logger.info(f"{log_prefix_file} Attempt {attempt + 1}: Navigating to case filed report...")
                    await report_handler.navigate_to_case_filed_report()
                    court_logger.info(
                        f"{log_prefix_file} Attempt {attempt + 1}: Navigation to report page successful. URL: {page.url}")

                    current_step = "ConfigureCaseFiledReport"
                    court_logger.info(f"{log_prefix_file} Attempt {attempt + 1}: Configuring case filed report...")
                    await report_handler.configure_case_filed_report()
                    court_logger.info(
                        f"{log_prefix_file} Attempt {attempt + 1}: Report configuration successful. URL: {page.url}")

                    current_step = "RunReport"
                    court_logger.info(f"{log_prefix_file} Attempt {attempt + 1}: Running report...")
                    has_cases = await report_handler.run_report()
                    court_logger.info(
                        f"{log_prefix_file} Attempt {attempt + 1}: Report run. Has cases: {has_cases}. Current URL: {page.url}")

                    if not has_cases:
                        court_logger.info(
                            f"{log_prefix_file} No cases found in report for attempt {attempt + 1}. Task successful.")
                        self.logger.info(
                            f"{log_prefix_console} No cases reported for {court_id} in attempt {attempt + 1}.")
                        attempt_counts = {key: 0 for key in expected_keys_for_log}  # Reset counts for this case
                        attempt_success_flag = True
                        task_overall_success = True  # Task itself succeeded (found no cases)
                        break

                    current_step = "ProcessReportRows"
                    court_logger.info(f"{log_prefix_file} Attempt {attempt + 1}: Processing report rows...")
                    row_processing_counts = await self._process_report_rows(
                        navigator, court_id, iso_date, start_date_obj, end_date_obj, context, processor_config,
                        court_logger
                    )
                    for key_count in expected_keys_for_log:  # Ensure all keys are populated
                        attempt_counts[key_count] = row_processing_counts.get(key_count, 0)

                    court_logger.info(
                        f"{log_prefix_file} Finished processing rows for attempt {attempt + 1}. Counts: {pformat(attempt_counts)}")
                    self.logger.info(
                        f"{log_prefix_console} Row processing complete for {court_id} attempt {attempt + 1}.")
                    attempt_success_flag = True
                    task_overall_success = True
                    break  # Successful attempt, exit retry loop

                except FileNotFoundError as fnf_err:
                    if "playwright/driver/node" in str(fnf_err) or "No such file or directory" in str(fnf_err):
                        last_critical_exception_str = (
                            f"Attempt {attempt + 1} FileNotFoundError at step '{current_step}': Playwright driver/node missing. "
                            f"Run 'playwright install'. Error: {str(fnf_err).splitlines()[0]}"
                        )
                        court_logger.critical(f"{log_prefix_file} {last_critical_exception_str}", exc_info=False) # exc_info=False for cleaner log
                        self.logger.critical(f"{log_prefix_console} {last_critical_exception_str}")
                        task_overall_success = False # Fatal error for this task
                        break  # Stop retrying this task
                    else: # Other FileNotFoundError
                        last_critical_exception_str = f"Attempt {attempt + 1} FileNotFoundError at step '{current_step}': {type(fnf_err).__name__} - {str(fnf_err).splitlines()[0]}"
                        court_logger.error(f"{log_prefix_file} {last_critical_exception_str}", exc_info=True)
                        # This will fall through to generic Exception handling if not retrying

                except PlaywrightTimeoutError as pte:
                    last_critical_exception_str = f"Attempt {attempt + 1} PlaywrightTimeoutError at step '{current_step}': {str(pte).splitlines()[0]}"
                    court_logger.error(f"{log_prefix_file} {last_critical_exception_str}", exc_info=True)
                    if navigator and navigator.is_ready:
                        await navigator.save_screenshot(f"timeout_at_step_{current_step}")

                except PlaywrightError as pe:
                    last_critical_exception_str = f"Attempt {attempt + 1} PlaywrightError at step '{current_step}': {type(pe).__name__} - {str(pe).splitlines()[0]}"
                    court_logger.error(f"{log_prefix_file} {last_critical_exception_str}", exc_info=True)
                    if navigator and navigator.is_ready:
                        await navigator.save_screenshot(f"playwright_error_at_step_{current_step}")
                    if "Target closed" in str(pe) or "Browser has been closed" in str(pe):
                        court_logger.critical(
                            f"{log_prefix_file} Fatal browser/context error at step '{current_step}'. Aborting retries.")
                        self.logger.critical(
                            f"{log_prefix_console} Fatal browser/context error for {court_id}, aborting retries.")
                        task_overall_success = False  # Mark task as failed
                        break  # Exit retry loop due to fatal error

                except Exception as e:
                    last_critical_exception_str = f"Attempt {attempt + 1} Exception at step '{current_step}': {type(e).__name__} - {str(e).splitlines()[0]}"
                    court_logger.error(f"{log_prefix_file} {last_critical_exception_str}", exc_info=True)
                    if navigator and navigator.is_ready:
                        await navigator.save_screenshot(f"exception_at_step_{current_step}")
                    if "Target closed" in last_critical_exception_str or "Browser has been closed" in last_critical_exception_str: # Check this for generic Exception too
                        court_logger.critical(
                            f"{log_prefix_file} Fatal browser/context error (general catch) at step '{current_step}'. Aborting retries.")
                        self.logger.critical(
                            f"{log_prefix_console} Fatal browser/context error (general) for {court_id}, aborting retries.")
                        task_overall_success = False
                        break
                finally:
                    court_logger.info(
                        f"{log_prefix_file} Attempt {attempt + 1}: Entering finally block. Last successful step: {current_step}")
                    if page and not page.is_closed():
                        court_logger.debug(f"{log_prefix_file} Attempt {attempt + 1}: Closing page.")
                        await page.close()
                    if context:
                        court_logger.debug(f"{log_prefix_file} Attempt {attempt + 1}: Closing context.")
                        await context.close()
                        context = None

                    attempt_specific_download_path_obj = Path(attempt_specific_download_path_str)
                    if await asyncio.to_thread(attempt_specific_download_path_obj.exists):
                        try:
                            court_logger.debug(
                                f"{log_prefix_file} Attempt {attempt + 1}: Removing dir {attempt_specific_download_path_obj}")
                            await asyncio.to_thread(shutil.rmtree, attempt_specific_download_path_obj)
                        except Exception as rmtree_err:
                            court_logger.warning(
                                f"{log_prefix_file} Failed to remove {attempt_specific_download_path_obj}: {rmtree_err}")
                    court_logger.info(f"{log_prefix_file} Attempt {attempt + 1}/{max_attempts} cleanup finished.")

                if attempt_success_flag:  # If this attempt was successful, break from retry loop
                    break

                # If retry loop should be broken due to fatal error (already handled above, but double check)
                if not task_overall_success and ("Fatal browser/context error" in last_critical_exception_str or "Playwright driver/node missing" in last_critical_exception_str):
                    self.logger.info(
                        f"{log_prefix_console} Not retrying {court_id} due to fatal error: {last_critical_exception_str}")
                    break

                if attempt < max_attempts - 1:  # If not the last attempt and not a fatal error
                    self.logger.info(f"{log_prefix_console} Retrying task for {court_id} in {5 * (attempt + 1)}s...")
                    court_logger.info(f"{log_prefix_file} Retrying task in {5 * (attempt + 1)}s...")
                    await asyncio.sleep(5 * (attempt + 1))
                else:  # All attempts failed
                    self.logger.error(
                        f"{log_prefix_console} All {max_attempts} attempts failed for court {court_id}. Last error at step '{current_step}': {last_critical_exception_str}")
                    court_logger.error(
                        f"{log_prefix_file} All {max_attempts} attempts failed. Last error at step '{current_step}': {last_critical_exception_str}")
                    task_overall_success = False  # Explicitly mark as failed

            # Final outcome logging for this court task
            if task_overall_success:
                self.logger.info(
                    f"{log_prefix_console} Task for court {court_id} completed successfully. Final counts: {pformat(attempt_counts)}")
                court_logger.info(
                    f"{log_prefix_file} Task completed successfully. Final counts: {pformat(attempt_counts)}")
            else:
                self.logger.error(
                    f"{log_prefix_console} Task FAILED for court {court_id}. Last critical error at step '{current_step}': {last_critical_exception_str}. Final counts: {pformat(attempt_counts)}")
                court_logger.error(
                    f"{log_prefix_file} Task FAILED. Last critical error at step '{current_step}': {last_critical_exception_str}. Final counts: {pformat(attempt_counts)}")
                # If counts are all zero but it wasn't a "no cases found" success, mark a general failure.
                if attempt_counts.get("attempted", 0) == 0 and \
                   attempt_counts.get("successful", 0) == 0 and \
                   attempt_counts.get("failed", 0) == 0 and \
                   not (current_step == "RunReport" and not has_cases and task_overall_success):
                    attempt_counts["failed"] = 1

            return court_id, task_overall_success, attempt_counts



# --- [ Main Execution Logic, Standalone Function, Config Loading, get_court_ids, __main__ block remain largely the same ] ---
# Ensure they call the appropriate orchestrator methods

# --- Main Execution Logic (Async) ---
async def main_async(params: Dict[str, Any]):
    # (Keep existing logging setup)
    log_level_str = os.environ.get('LOG_LEVEL', 'INFO').upper()
    log_level = getattr(logging, log_level_str, logging.INFO)
    # Configure root logger first
    logging.basicConfig(level=log_level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', force=True)
    # Then set levels for noisy loggers
    logging.getLogger('playwright').setLevel(logging.WARNING)
    logging.getLogger('fasteners').setLevel(logging.WARNING)
    logging.getLogger('portalocker').setLevel(logging.WARNING)
    logging.getLogger('websockets.client').setLevel(logging.INFO)  # Can be noisy on DEBUG

    logger = logging.getLogger(__name__)  # Get logger for this module

    logger.info("--- Pacer Client V4 (Async Refactored) Starting ---")
    logger.info(f"Effective Parameters: {pformat(params)}")

    try:
        config = load_config_local(
            date_str=params.get('end_date'), start_date=params.get('start_date'),
            headless=params.get('headless', True), html_only=params.get('html_only', False),
            data_path=params.get('data_path', get_default_data_path(params)), max_workers=params.get('max_workers'),
            retries_per_court=params.get('retries_per_court'),
            username_prod=params.get('pacer_username') or os.environ.get("PACER_USERNAME"),
            password_prod=params.get('pacer_password') or os.environ.get("PACER_PASSWORD"),
            bucket_name=params.get('s3_bucket') or os.environ.get("S3_BUCKET"),
            aws_region=params.get('aws_region') or os.environ.get("AWS_REGION"),
            openai_api_key=params.get('openai_key') or os.environ.get("OPENAI_API_KEY"),
        )
        logger.debug(f"Loaded Configuration: {pformat(config)}")
    except Exception as config_err:
        logger.error(f"Failed to load configuration: {config_err}", exc_info=True)
        return

    try:
        orchestrator = PacerOrchestrator(config)
    except Exception as init_err:
        logger.error(f"Failed to initialize PacerOrchestrator: {init_err}", exc_info=True)
        return

    start_date_param = params.get('start_date')
    end_date_param = params.get('end_date')

    if params.get('scraper', False):
        logger.info("Scraper flag is set.")

        single_court_param_list = params.get(
            'process_single_court')  # This is expected to be a list from env var parsing
        docket_num_param = params.get('docket_num')

        # Mode 1: Single Docket Standalone
        if isinstance(docket_num_param, str) and single_court_param_list and len(single_court_param_list) == 1:
            court_id_for_single = single_court_param_list[0]
            logger.info(f"Mode: Single Docket. Court={court_id_for_single}, Docket={docket_num_param}")
            await process_single_docket_standalone(
                config, court_id_for_single, docket_num_param,
                html_only=config.get('html_only', False)
            )
        # Mode 2: Multiple Dockets for a Single Court
        elif isinstance(docket_num_param, list) and single_court_param_list and len(single_court_param_list) == 1:
            court_id_for_multi_docket = single_court_param_list[0]
            logger.info(
                f"Mode: Multi-Docket. Processing {len(docket_num_param)} dockets for court {court_id_for_multi_docket}.")
            failed_tasks = await orchestrator.process_courts(
                court_ids=[],  # Not used for task creation in this mode
                start_date_str=start_date_param,
                end_date_str=end_date_param,
                multi_docket_court_id=court_id_for_multi_docket,
                multi_docket_numbers=docket_num_param
            )
            if failed_tasks:
                logger.warning(
                    f"Multi-docket processing task for {court_id_for_multi_docket} may have failed (see logs).")
            else:
                logger.info(f"Multi-docket processing task for {court_id_for_multi_docket} finished.")

        # Mode 3: Report Scraping (list of courts or all courts)
        elif not docket_num_param:
            courts_to_process_reports = []
            if single_court_param_list:  # Specific courts for report scraping
                courts_to_process_reports = single_court_param_list
                logger.info(f"Mode: Report Scraping for specific courts: {courts_to_process_reports}")
            else:  # All courts for report scraping
                try:
                    courts_to_process_reports = get_court_ids()
                    if not courts_to_process_reports:
                        logger.error("No court IDs found for report mode. Exiting.")
                        return
                    logger.info(f"Mode: Report Scraping for all {len(courts_to_process_reports)} district courts.")
                except Exception as e:
                    logger.error(f"Failed to get court IDs: {e}", exc_info=True)
                    return

            if courts_to_process_reports:
                failed_courts = await orchestrator.process_courts(
                    courts_to_process_reports,
                    start_date_param,
                    end_date_param
                )
                if failed_courts:
                    logger.warning(
                        f"Report orchestration finished with {len(failed_courts)} failed courts: {failed_courts}")
                else:
                    logger.info("Report orchestration finished successfully.")
            else:
                logger.info("No courts selected for report processing.")
        else:
            logger.warning(
                f"Invalid parameter combination: docket_num ('{docket_num_param}' type: {type(docket_num_param)}), process_single_court ('{single_court_param_list}'). No action taken.")

    else:
        logger.info("Scraper flag is not set. No scraping actions performed.")

    logger.info("--- Pacer Client V4 Main Execution Finished ---")


# Standalone function for single docket processing (remains the same structure)
async def process_single_docket_standalone(config: Dict[str, Any], court_id: str, docket_num: str,
                                           html_only: bool = False):
    # Initial logger setup for standalone mode (console primarily)
    console_logger = logging.getLogger(
        f"{__name__}.SingleDocketStandalone.{court_id}.{docket_num.replace(':', '_').replace('/', '_')}")
    console_logger.info(
        f"--- Starting Single Docket (Standalone): Court={court_id}, Docket={docket_num}, HTML_Only={html_only} ---")

    iso_date_for_paths: Optional[str] = config.get('iso_date')
    target_date_obj: Optional[DateType] = None
    if not iso_date_for_paths:
        console_logger.warning(f"config['iso_date'] missing. Deriving from config['date'] ('{config.get('date')}')")
        if config.get('date'):
            try:
                parsed_dt = parse_date_str(config['date'])
                if parsed_dt:
                    target_date_obj = parsed_dt
                    iso_date_for_paths = target_date_obj.strftime('%Y%m%d')
                else:
                    console_logger.error(f"Failed to parse config['date'] ('{config.get('date')}')")
            except ValueError as e:
                console_logger.error(f"Error parsing config['date']: {e}")
        if not iso_date_for_paths:  # Fallback to today if still not determined
            target_date_obj = datetime.now().date()
            iso_date_for_paths = target_date_obj.strftime('%Y%m%d')
            console_logger.warning(f"iso_date undetermined, falling back to current date: {iso_date_for_paths}")
    else:
        try:
            target_date_obj = datetime.strptime(iso_date_for_paths, '%Y%m%d').date()
        except ValueError:
            console_logger.error(f"CRITICAL: config['iso_date'] ('{iso_date_for_paths}') invalid. Using current date.")
            target_date_obj = datetime.now().date()
            iso_date_for_paths = target_date_obj.strftime('%Y%m%d')

    # Setup court-specific file logger for this standalone run
    log_file_path = Path(config.get('data_path', get_default_data_path(
        config))) / iso_date_for_paths / "logs" / f"pacer_{court_id}_docket_{docket_num.replace(':', '_').replace('/', '_')}.log"
    court_logger_name = f"pacer_run.{court_id}.docket.{docket_num.replace(':', '_').replace('/', '_')}"
    # Use console_logger for messages before court_logger is set up, then switch
    court_logger = _setup_court_file_logger(court_logger_name, log_file_path,
                                            level=logging.DEBUG)  # More verbose for single docket
    court_logger.info(
        f"--- Detailed logging for Single Docket: Court={court_id}, Docket={docket_num}, HTML_Only={html_only}, ISO_Date={iso_date_for_paths} ---")

    start_date_obj_for_dp = target_date_obj
    end_date_obj_for_dp = target_date_obj
    court_logger.info(f"Using iso_date_for_paths: {iso_date_for_paths}, target_date_obj: {target_date_obj}")

    # These initializations can raise exceptions if config is bad (e.g., missing keys for S3Manager)
    # The main_async function usually handles config loading and PacerOrchestrator init with try-except.
    # Here, we assume config is mostly valid by the time this standalone function is called.
    pacer_db: Optional[PacerManager] = None
    dc_db: Optional[DistrictCourtsManager] = None
    s3_manager: Optional[S3Manager] = None
    gpt_interface: Optional[GPT4] = None
    file_manager: Optional[PacerFileManager] = None
    temp_orchestrator: Optional[PacerOrchestrator] = None

    try:
        pacer_db = PacerManager(config, use_local=False)
        dc_db = DistrictCourtsManager(config, use_local=False)
        s3_manager = S3Manager(config, config['bucket_name'])
        gpt_interface = GPT4(config)
        file_manager = PacerFileManager(base_data_dir=config.get('data_path', get_default_data_path(config)))

        date_dir_for_run = Path(config.get('data_path', get_default_data_path(config))) / iso_date_for_paths
        subdirs = ['dockets', 'logs', 'html', 'screenshots']
        for sd_name in subdirs:
            sd_path = date_dir_for_run / sd_name
            sd_path.mkdir(parents=True, exist_ok=True)
            if sd_name == 'dockets': (sd_path / 'temp').mkdir(parents=True, exist_ok=True); (sd_path / '.locks').mkdir(
                parents=True, exist_ok=True)
        court_logger.info(f"Ensured directories under: {date_dir_for_run}")

        temp_orchestrator = PacerOrchestrator(
            config)  # This also inits its own BrowserService if not careful with how it's used.
        # Here, it's for helper methods that don't need an active browser from *this* orchestrator instance.
        # The actual browser for this standalone function is created below.

    except Exception as setup_err:
        court_logger.error(f"Error during initial setup (Managers, FileManager, Dirs): {setup_err}", exc_info=True)
        console_logger.error(f"Initial setup error for {court_id}/{docket_num}: {setup_err}")
        return

    main_navigator: Optional[PacerNavigator] = None  # Define here for broader scope in finally block
    main_page: Optional[Page] = None
    case_page_for_processing: Optional[Page] = None
    single_docket_download_path: Optional[Path] = None

    try:
        # This async with block will manage BrowserService lifecycle.
        # The FileNotFoundError for playwright/driver/node will be caught here if it occurs during BrowserService.__aenter__
        async with BrowserService(headless=config.get('headless', True),
                                  timeout_ms=config.get('timeout',
                                                        PacerOrchestrator.DEFAULT_TIMEOUT_S) * 1000) as browser_service:

            single_docket_download_path = date_dir_for_run / "dockets" / "temp" / f"single_{court_id}_{docket_num.replace(':', '_').replace('/', '_')}_{uuid.uuid4().hex[:8]}"
            single_docket_download_path.mkdir(parents=True, exist_ok=True)

            async with await browser_service.new_context(download_path=str(single_docket_download_path)) as context:
                main_page = await context.new_page()
                main_navigator = PacerNavigator(main_page, config.get('delay', PacerOrchestrator.DEFAULT_DELAY_S),
                                                browser_service.timeout_ms)

                base_screenshot_dir = date_dir_for_run / 'screenshots' / f"single_{court_id}_{docket_num.replace(':', '_').replace('/', '_')}"
                base_screenshot_dir.mkdir(parents=True, exist_ok=True)
                main_navigator.set_screenshot_dir(str(base_screenshot_dir / "main_nav"))

                court_logger.info("Initializing authenticator for single docket processing...")
                authenticator = PacerAuthenticator(main_navigator, config['username_prod'], config['password_prod'],
                                                   logger=court_logger)

                if not await temp_orchestrator._perform_ecf_login_sequence(main_navigator, court_id, authenticator,
                                                                           # type: ignore
                                                                           court_logger):
                    raise Exception("ECF Login Sequence Failed for single docket.")

                court_logger.info("ECF Login Sequence successful. Navigating to Query page...")
                if not await temp_orchestrator._navigate_to_query_page(main_navigator, court_id,
                                                                       court_logger):  # type: ignore
                    raise Exception("Navigation to Query page failed for single docket.")

                court_logger.info(f"Attempting to query docket: {docket_num}")
                case_page_for_processing = await temp_orchestrator._query_docket_and_get_page(  # type: ignore
                    main_navigator=main_navigator,
                    context=context,
                    court_id=court_id,
                    docket_num=docket_num,
                    screenshot_dir_base=base_screenshot_dir,
                    court_logger=court_logger
                )

                if not case_page_for_processing or case_page_for_processing.is_closed():
                    raise Exception(f"Failed to retrieve docket sheet page for {docket_num}")

                court_logger.info(f"Successfully navigated to docket sheet page: {case_page_for_processing.url}")
                docket_page_navigator = PacerNavigator(case_page_for_processing,
                                                       config.get('delay', PacerOrchestrator.DEFAULT_DELAY_S),
                                                       browser_service.timeout_ms)
                docket_processor_screenshot_dir = base_screenshot_dir / "docket_processor"
                docket_processor_screenshot_dir.mkdir(parents=True, exist_ok=True)
                docket_page_navigator.set_screenshot_dir(str(docket_processor_screenshot_dir))

                processor_specific_config = {**config,
                                             'html_only': html_only,
                                             'upload_to_s3': False,
                                             # Typically false for single direct processing, but can be configured
                                             'context_download_path': str(single_docket_download_path)
                                             }
                court_logger.info(
                    f"Standalone docket: S3 document (PDF/ZIP) upload explicitly set in processor_specific_config (current: {processor_specific_config['upload_to_s3']}). HTML_Only: {html_only}")

                async with DocketProcessor(
                        court_id, iso_date_for_paths, start_date_obj_for_dp, end_date_obj_for_dp,  # type: ignore
                        docket_page_navigator, file_manager, pacer_db, dc_db, s3_manager, gpt_interface,  # type: ignore
                        processor_specific_config, logger=court_logger
                ) as docket_processor:
                    court_logger.info("Processing docket page content...")
                    initial_details = {
                        'court_id': court_id, 'docket_num': docket_num,
                        'filing_date': None,  # Explicitly requested, filing date will be parsed from HTML
                        'added_date': end_date_obj_for_dp.strftime('%Y-%m-%d'),  # type: ignore
                        '_is_explicitly_requested': True
                    }
                    result = await docket_processor.process_docket_page(initial_details)
                    status_msg = "Processed"
                    if docket_processor.is_downloaded: status_msg += " & Downloaded/Copied"
                    if html_only and not docket_processor.is_downloaded: status_msg += " (HTML Only)"

                    if result:
                        court_logger.info(f"--- Single Docket Finished Successfully: {status_msg} ---")
                        console_logger.info(
                            f"--- Single Docket {court_id}/{docket_num} Finished Successfully: {status_msg} ---")
                    else:
                        court_logger.warning(
                            "--- Single Docket Finished with Errors/Failures (DocketProcessor returned None) ---")
                        console_logger.warning(
                            f"--- Single Docket {court_id}/{docket_num} Finished with Errors/Failures ---")

    except FileNotFoundError as fnf_err:
        if "playwright/driver/node" in str(fnf_err) or "No such file or directory" in str(fnf_err):
            error_message = (
                f"Playwright driver/node executable not found: {fnf_err}. "
                "This usually means Playwright drivers are not installed correctly. "
                "Please run 'playwright install' in your environment."
            )
            console_logger.critical(error_message)  # Use critical for console
            court_logger.critical(error_message, exc_info=False)  # exc_info=False for cleaner log
        else:
            console_logger.error(f"A FileNotFoundError occurred during BrowserService initialization: {fnf_err}",
                                 exc_info=True)
            court_logger.error(f"A FileNotFoundError occurred during BrowserService initialization: {fnf_err}",
                               exc_info=True)
        # No return here, finally block will execute. Function will implicitly return None.

    except Exception as e:
        court_logger.error(f"Error during single docket workflow: {e}", exc_info=True)
        console_logger.error(f"Error during single docket {court_id}/{docket_num} workflow: {e}",
                             exc_info=False)
        if main_navigator and main_navigator.is_ready:
            try:
                await main_navigator.save_screenshot("single_docket_workflow_error")
            except Exception as ss_e:
                court_logger.warning(f"Failed to save error screenshot: {ss_e}")
    finally:
        if case_page_for_processing and not case_page_for_processing.is_closed(): await case_page_for_processing.close()
        if main_page and not main_page.is_closed(): await main_page.close()
        # BrowserService and its context are managed by `async with` blocks, will close automatically.
        if single_docket_download_path and single_docket_download_path.exists():
            try:
                shutil.rmtree(single_docket_download_path)
            except Exception as rmtree_err:
                court_logger.warning(
                    f"Failed to remove temp download dir {single_docket_download_path}: {rmtree_err}")

    court_logger.info(f"--- Single Docket Processing Complete for {court_id} / {docket_num} ---")
    console_logger.info(f"--- Single Docket Processing Complete for {court_id} / {docket_num} ---")


# --- Configuration Loading ---
def load_config_local(date_str: Optional[str] = None, start_date: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """Loads configuration (remains the same)."""
    logger = logging.getLogger(__name__ + ".load_config_local")
    logger.debug(f"Loading config with date: {date_str}, start_date: {start_date}, kwargs: {kwargs}")
    conf = {
        "username_prod": None, "password_prod": None, "headless": True,
        "delay": PacerOrchestrator.DEFAULT_DELAY_S, "timeout": PacerOrchestrator.DEFAULT_TIMEOUT_S,
        "bucket_name": None, "aws_region": "us-west-2", "openai_api_key": None,
        "date": date_str, "start_date": start_date, "iso_date": None,
        "data_path": os.path.join(os.getcwd(), "data"), "max_workers": PacerOrchestrator.DEFAULT_MAX_WORKERS,
        "retries_per_court": PacerOrchestrator.DEFAULT_RETRIES_PER_COURT, "html_only": False,
        "zip_pdf_downloads": True, "upload_to_s3": True, "cdn_base_url": "https://cdn.lexgenius.ai",
    }
    env_map = {
        "PACER_USERNAME": "username_prod", "PACER_PASSWORD": "password_prod",
        "HEADLESS": ("headless", lambda v: v.lower() == 'true'), "S3_BUCKET": "bucket_name",
        "AWS_REGION": "aws_region", "OPENAI_API_KEY": "openai_api_key", "DATA_PATH": "data_path",
        "MAX_WORKERS": ("max_workers", int), "RETRIES_PER_COURT": ("retries_per_court", int),
        "HTML_ONLY": ("html_only", lambda v: v.lower() == 'true'),
        "ZIP_PDF_DOWNLOADS": ("zip_pdf_downloads", lambda v: v.lower() == 'true'),
        "UPLOAD_TO_S3": ("upload_to_s3", lambda v: v.lower() == 'true'),
        "CDN_BASE_URL": "cdn_base_url",
    }
    for env_var, conf_key_info in env_map.items():
        value = os.environ.get(env_var)
        if value is not None:
            if isinstance(conf_key_info, tuple):
                key, processor = conf_key_info
                try:
                    conf[key] = processor(value)
                except ValueError:
                    logger.warning(f"Invalid value '{value}' for env var {env_var}")
            else:
                conf[conf_key_info] = value

    kwargs_filtered = {k: v for k, v in kwargs.items() if v is not None}
    conf.update(kwargs_filtered)

    if conf.get('date'):
        try:
            end_date_obj = parse_date_str(conf['date'])
            if end_date_obj: conf['iso_date'] = end_date_obj.strftime('%Y%m%d')
        except Exception as e:
            logger.warning(f"Error setting iso_date from '{conf['date']}': {e}")

    missing_critical = [k for k in ['username_prod', 'password_prod', 'bucket_name', 'openai_api_key'] if
                        not conf.get(k)]
    if missing_critical:
        raise ValueError(f"Missing critical configuration: {missing_critical}")
    return conf


def get_court_ids() -> List[str]:
    """Loads court IDs from the district courts JSON file, ensuring uniqueness and preserving order."""
    try:
        courts_file = Path(DISTRICT_COURTS_PATH)
        if not courts_file.exists():
            logging.error(f"District courts file not found at {courts_file}")
            raise FileNotFoundError(f"District courts file not found at {courts_file}")

        with open(courts_file) as f:
            district_courts_data = json.load(f)

        ids = []
        for court in district_courts_data:
            court_id = court.get('court_id')
            # Basic validation: expect format like 'cacd1', remove trailing digit
            if isinstance(court_id, str) and len(court_id) > 1 and court_id[-1].isdigit():
                cleaned_id = court_id[:-1]
                if cleaned_id:  # Ensure not empty after slicing
                    ids.append(cleaned_id)
            elif isinstance(court_id, str) and court_id:  # Handle cases like 'jpml' which don't end in a digit
                ids.append(court_id)

        # Use OrderedDict.fromkeys to get unique IDs while preserving order
        # then convert back to a list.
        # For Python 3.7+, dict.fromkeys also preserves insertion order,
        # but OrderedDict is more explicit for older versions or clarity.
        unique_ordered_ids = list(OrderedDict.fromkeys(ids))

        logging.info(f"Loaded {len(unique_ordered_ids)} unique court IDs from {DISTRICT_COURTS_PATH}.")
        return unique_ordered_ids
    except FileNotFoundError:  # Already handled above, but good for specific catch
        # Logging is done before raising
        raise
    except json.JSONDecodeError as e:
        logging.error(f"Error decoding JSON from {DISTRICT_COURTS_PATH}: {e}", exc_info=True)
        raise  # Re-raise after logging
    except Exception as e:
        logging.error(f"Unexpected error loading court IDs from {DISTRICT_COURTS_PATH}: {e}", exc_info=True)
        raise  # Re-raise after logging


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    today_date = datetime.now().date()
    default_end_date = today_date.strftime('%m/%d/%Y')
    default_start_date = calculate_default_start_date(today_date).strftime('%m/%d/%Y')

    params = {
        'start_date': os.environ.get('START_DATE', default_start_date),
        'end_date': os.environ.get('END_DATE', default_end_date),
        'headless': os.environ.get('HEADLESS', 'True').lower() == 'true',
        'scraper': os.environ.get('RUN_SCRAPER', 'True').lower() == 'true',
        'docket_num': os.environ.get('DOCKET_NUM', None),
        'process_single_court': [c.strip() for c in os.environ.get('COURT_IDS', '').split(',') if c.strip()],
        'html_only': os.environ.get('HTML_ONLY', 'False').lower() == 'true',
        'max_workers': int(os.environ.get('MAX_WORKERS')) if os.environ.get('MAX_WORKERS') else None,
        'retries_per_court': int(os.environ.get('RETRIES_PER_COURT')) if os.environ.get('RETRIES_PER_COURT') else None,
        'data_path': os.environ.get('DATA_PATH', os.path.join(os.getcwd(), "data")),
    }

    try:
        if os.name == 'nt':
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
        asyncio.run(main_async(params))
    except KeyboardInterrupt:
        print("\nExecution interrupted by user.")
        logging.warning("Execution interrupted.")
    except ValueError as config_err:
        print(f"\nConfiguration Error: {config_err}")
        logging.critical(f"Configuration Error: {config_err}")
    except RuntimeError as runtime_err:
        print(f"\nRuntime Error: {runtime_err}")
        logging.critical(f"Runtime Error: {runtime_err}", exc_info=True)
    except Exception as main_err:
        print(f"\nUnhandled error: {main_err}")
        logging.exception("Unhandled error during main execution:")
    print("Script finished.")

# TODO: Check if_downloaded prior to opening docket page
# TODO: Proceedings for case 3:25-cv-03913 are not available
