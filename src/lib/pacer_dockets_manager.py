#!/usr/bin/env python3
import argparse
# --- ADDED IMPORTS ---
import decimal
import json  # Ensure json is imported if used later, e.g., for logging
from collections import defaultdict
from datetime import datetime, timed<PERSON>ta
from pprint import pformat
from typing import List, Dict, Any, Optional, Union, Tuple

import pandas as pd
from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError
from rich.console import Console
from rich.table import Table

from src.lib.dynamodb_base_manager import DynamoDbBaseManager


# --- ADDED HELPER FUNCTION ---
# Helper function to recursively convert Decimals in a dictionary/list
def replace_decimals(obj):
    if isinstance(obj, list):
        return [replace_decimals(x) for x in obj]
    elif isinstance(obj, dict):
        return {k: replace_decimals(v) for k, v in obj.items()}
    elif isinstance(obj, decimal.Decimal):  # Use the imported decimal module
        if obj % 1 == 0:
            return int(obj)
        else:
            return float(obj)
    else:
        return obj


# --- END ADDED HELPER FUNCTION ---
console = Console()


class PacerDocketManager(DynamoDbBaseManager):  # Renamed class
    def __init__(self, config: Dict[str, Any], use_local: bool = False,
                 local_port: int = 8000) -> None:
        """
        Initialize the PacerDocketManager.
        Relies entirely on the base class for AWS/Local setup for the 'PacerDockets' table.
        """
        super().__init__(config, 'PacerDockets', use_local=use_local, local_port=local_port)  # Changed table name
        self.logger.info("PacerDocketManager initialization complete (relying on base setup for 'PacerDockets' table).")

        if self.table is None:
            raise ValueError(f"CRITICAL: PacerDocketManager table reference is None after base initialization.")

    def add_or_update_record(self, record: Dict[str, Any]) -> None:
        record_pascal = self.snake_or_camel_to_pascal_case(record)
        record_sanitized = self.sanitize_record(record_pascal)

        # Apply specific string conversion *after* sanitization if needed for PacerDockets
        record_final = {k: str(v) if not isinstance(v, bool) else v for k, v in record_sanitized.items() if
                        v is not None}

        try:
            # PK for PacerDockets is CourtId, DocketNum
            if 'CourtId' not in record_final or 'DocketNum' not in record_final:
                self.logger.error(
                    f"Record missing CourtId or DocketNum. Cannot add/update. Record: {str(record_final)[:200]}")
                return

            key = {'CourtId': record_final['CourtId'], 'DocketNum': record_final['DocketNum']}
            existing_item_raw = self.table.get_item(Key=key, ConsistentRead=True).get('Item')
            existing_item = replace_decimals(existing_item_raw) if existing_item_raw else None

            if existing_item:
                update_data = {k: v for k, v in record_final.items() if v != existing_item.get(k)}
                update_data.pop('CourtId', None)  # Exclude PK from update_data
                update_data.pop('DocketNum', None)  # Exclude SK from update_data

                if update_data:
                    success = self.update_item(key, update_data)
                    if not success:
                        self.logger.warning(f"Base update_item failed for key {key}. Check base logs.")
                else:
                    self.logger.debug(f"No update needed for existing record: {key}")
            else:
                self.table.put_item(Item=record_final)
                self.logger.info(f"New record added: {key}")

        except ClientError as e:
            self.logger.error(
                f"ClientError adding/updating record {record_final.get('CourtId')}/{record_final.get('DocketNum')}: {e.response['Error']['Message']}")
        except KeyError as e:
            self.logger.error(
                f"KeyError: '{e}' missing in record_final for add/update. Keys: {list(record_final.keys())}")
        except Exception as e:
            self.logger.error(f"Unexpected error adding/updating record: {e}", exc_info=True)

    def get_mdl_count_in_date_range(self, mdl_num: str, start_date: str, end_date: str) -> int:
        start_date = datetime.strptime(start_date, '%Y%m%d').strftime('%Y%m%d')
        end_date = datetime.strptime(end_date, '%Y%m%d').strftime('%Y%m%d')

        try:
            # MdlNum-FilingDate-index is available on PacerDockets
            response = self.table.query(
                IndexName='MdlNum-FilingDate-index',
                KeyConditionExpression=Key('MdlNum').eq(mdl_num) & Key('FilingDate').between(start_date, end_date),
                Select='COUNT'
            )
            return response['Count']
        except ClientError as e:
            self.logger.error(
                f"Failed to query items for MdlNum {mdl_num} in date range {start_date} to {end_date}: {e.response['Error']['Message']}")
            return 0

    def get_filings_by_court(self, mdl_num: str, start_date: str, end_date: str) -> pd.DataFrame:
        start_date = datetime.strptime(start_date, '%Y%m%d').strftime('%Y%m%d')
        end_date = datetime.strptime(end_date, '%Y%m%d').strftime('%Y%m%d')

        try:
            # MdlNum-FilingDate-index is available on PacerDockets
            response = self.table.query(
                IndexName='MdlNum-FilingDate-index',
                KeyConditionExpression=Key('MdlNum').eq(mdl_num) & Key('FilingDate').between(start_date, end_date)
            )
            items_raw = response.get('Items', [])
            items = replace_decimals(items_raw)

            filings_by_court = defaultdict(list)
            for item in items:
                court = item.get('CourtId')
                docket_num = item.get('DocketNum')
                title = item.get('Title')
                new_filename = item.get('NewFilename')
                filing_date = item.get('FilingDate')

                if court:
                    filings_by_court[court].append({
                        'DocketNum': docket_num,
                        'Title': title,
                        'NewFilename': new_filename,
                        'FilingDate': filing_date,
                        'Court': court  # Original key was Court, seems redundant if CourtId is used
                    })

            for court in filings_by_court:
                filings_by_court[court].sort(key=lambda x: x['FilingDate'])

            all_filings = []
            for court, filings_list in filings_by_court.items():  # Renamed to avoid conflict
                for filing in filings_list:
                    all_filings.append(filing)

            df = pd.DataFrame(all_filings)
            return df
        except ClientError as e:
            self.logger.error(
                f"Failed to query items for MdlNum {mdl_num} in date range {start_date} to {end_date}: {e.response['Error']['Message']}")
            return pd.DataFrame()

    def search_pacer_by_title(self, search_string: str) -> List[Dict[str, Any]]:
        import re
        try:
            if search_string.lower() in ['depo provera', 'depo-provera']:
                pattern = re.compile(r"Depo[\s-]Provera", re.IGNORECASE)
            else:
                pattern = re.compile(search_string, re.IGNORECASE)

            # Using base class scan_table which handles pagination and decimal conversion
            all_items = list(self.scan_table(
                FilterExpression="contains(Title, :search_str_placeholder)",
                # This might be inefficient on large tables
                ExpressionAttributeValues={":search_str_placeholder": search_string}
                # DynamoDB contains is case-sensitive
            ))
            # If DynamoDB's contains is case-sensitive, and we need case-insensitive, filter in Python
            # items_raw = list(self.scan_table()) # Scan all
            # items = replace_decimals(items_raw) # Already done by scan_table in base

            # Python-side filtering for case-insensitivity (if scan filter doesn't suffice)
            # For large tables, scanning all and filtering in Python is very inefficient.
            # Consider if a GSI on a normalized Title field or OpenSearch integration is better.
            # For now, let's assume self.scan_table() handles basic filtering if possible, or we filter after.
            # The original PacerManager did a full scan then Python re.search. Let's stick to that pattern for now.

            items_raw_scan = list(self.scan_table(ProjectionExpression="Title"))  # Scan only Title for efficiency
            filtered_items = [item_with_title_only for item_with_title_only in items_raw_scan
                              if pattern.search(item_with_title_only.get('Title', ''))
                              # If found, we need to re-fetch the full item or project all needed fields in scan
                              ]
            # This approach is problematic as we only get Title.
            # Reverting to scanning all attributes and filtering in Python, like original:

            all_items_full = list(self.scan_table())  # scan_table already handles decimals.

            filtered_items_final = [item for item in all_items_full if pattern.search(item.get('Title', ''))]
            return filtered_items_final

        except ClientError as e:
            self.logger.error(f"Failed to search PacerDockets by Title: {e.response['Error']['Message']}")
            return []
        except Exception as e:
            self.logger.error(f"An unexpected error occurred while searching PacerDockets by Title: {str(e)}")
            return []

    def get_records_by_added_on(self, start_date: str, end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        try:
            start_dt = datetime.strptime(start_date, '%Y%m%d')
            end_dt = datetime.strptime(end_date, '%Y%m%d') if end_date else start_dt

            all_items_collected = []  # Renamed to avoid confusion with base class items
            current_date = start_dt

            while current_date <= end_dt:
                iso_date = current_date.strftime('%Y%m%d')
                # AddedOn-index is available on PacerDockets
                # Using query_items from base which handles pagination and decimals
                items_for_day = self.query_items(
                    key_conditions={'AddedOn': iso_date},
                    index_name='AddedOn-index'
                )
                all_items_collected.extend(items_for_day)
                self.logger.info(f"Found {len(items_for_day)} items with AddedOn date {iso_date}")
                current_date += timedelta(days=1)

            self.logger.info(f"Total items found and processed: {len(all_items_collected)}")
            return all_items_collected
        except ClientError as e:  # query_items handles its own logging
            return []
        except Exception as e:
            self.logger.error(f"An unexpected error occurred in get_records_by_added_on: {str(e)}", exc_info=True)
            return []

    def query_pacer_by_date(self, start_date: str, end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        # This method now calls the base class version which handles Decimals
        # and uses FilingDate-index for PacerDockets.
        return self.query_by_date(start_date_str=start_date, end_date_str=end_date, date_field='FilingDate',
                                  index_name='FilingDate-index')

    def docket_exists(self, court_id: str, docket_num: str, filing_date: Optional[str] = None) -> Union[
        List[Dict[str, Any]], bool]:
        try:
            docket_parts = docket_num.split(':')
            original_prefix = docket_parts[0]
            new_docket_num_base = docket_parts[1] if len(docket_parts) > 1 else docket_num

            # Attempt 1: Direct get_item with provided court_id and docket_num
            response = self.table.get_item(Key={'CourtId': court_id, 'DocketNum': docket_num})
            if 'Item' in response:
                item = replace_decimals(response['Item'])
                if filing_date and item.get('FilingDate') != filing_date:
                    self.logger.info(
                        f"Docket {court_id}:{docket_num} exists with PK, but FilingDate {item.get('FilingDate')} != {filing_date}")
                return [item]

            # Attempt 2: Query GSI CourtId-DocketNum-index
            response_gsi = self.table.query(
                IndexName='CourtId-DocketNum-index',
                KeyConditionExpression=Key('CourtId').eq(court_id) & Key('DocketNum').eq(docket_num)
            )
            items_raw_gsi = response_gsi.get('Items', [])
            if items_raw_gsi:
                items_gsi = replace_decimals(items_raw_gsi)
                if filing_date:
                    filtered_items = [item_gsi for item_gsi in items_gsi if item_gsi.get('FilingDate') == filing_date]
                    if filtered_items: return filtered_items
                else:
                    return items_gsi  # Return all matches if no filing_date filter

            # Attempt 3 & 4: Try variations with prefixes
            possible_prefixes = {str(i) for i in range(8) if str(i) != original_prefix}
            for prefix in sorted(possible_prefixes):
                modified_docket_num = f"{prefix}:{new_docket_num_base}"

                # Try get_item with modified_docket_num
                response_mod_pk = self.table.get_item(Key={'CourtId': court_id, 'DocketNum': modified_docket_num})
                if 'Item' in response_mod_pk:
                    item_mod_pk = replace_decimals(response_mod_pk['Item'])
                    if filing_date and item_mod_pk.get('FilingDate') != filing_date:
                        self.logger.info(
                            f"Docket {court_id}:{modified_docket_num} (PK) exists, but FilingDate mismatch.")
                        # Continue to GSI for this modified_docket_num if filing_date doesn't match PK item
                    else:
                        return [item_mod_pk]

                # Try GSI with modified_docket_num
                response_mod_gsi = self.table.query(
                    IndexName='CourtId-DocketNum-index',
                    KeyConditionExpression=Key('CourtId').eq(court_id) & Key('DocketNum').eq(modified_docket_num)
                )
                items_raw_mod_gsi = response_mod_gsi.get('Items', [])
                if items_raw_mod_gsi:
                    items_mod_gsi = replace_decimals(items_raw_mod_gsi)
                    if filing_date:
                        filtered_items = [item_mg for item_mg in items_mod_gsi if
                                          item_mg.get('FilingDate') == filing_date]
                        if filtered_items: return filtered_items
                    else:
                        return items_mod_gsi

            self.logger.info(f"No item found for CourtId: {court_id}, DocketNum variations of {docket_num}" + (
                f" with FilingDate: {filing_date}" if filing_date else ""))
            return False

        except ClientError as e:
            self.logger.error(f"ClientError checking record existence: {e.response['Error']['Message']}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error checking record existence: {str(e)}", exc_info=True)
            return False

    def check_docket_status(self, court_id: str, docket_num: str, filing_date: str) -> Tuple[
        str, Optional[Union[Dict[str, Any], List[Dict[str, Any]]]]]:
        try:
            # CourtId-DocketNum-index is available
            response = self.table.query(
                IndexName='CourtId-DocketNum-index',
                KeyConditionExpression=Key('CourtId').eq(court_id) & Key('DocketNum').eq(docket_num)
            )
            items_raw = response.get('Items', [])
            items = replace_decimals(items_raw)

            if len(items) > 1:
                filing_dates = set(item['FilingDate'] for item in items if 'FilingDate' in item)
                added_on_dates = set(item['AddedOn'] for item in items if 'AddedOn' in item)

                if len(filing_dates) > 1 and len(added_on_dates) == 1:
                    return 'multiple_filing_dates_same_added_on', items
                elif len(filing_dates) > 1:
                    return 'multiple_filing_dates', items
                else:  # Multiple items, same filing_date (or no filing_date field in some)
                    return 'multiple', items
            elif len(items) == 1:
                # Check if the filing_date of the single found item matches the provided one
                if items[0].get('FilingDate') != filing_date:
                    return 'different_filing_date', items[0]
                return 'single', items[0]
            else:  # No items found
                return 'new', None

        except ClientError as e:
            self.logger.error(f"ClientError checking docket status: {e.response['Error']['Message']}")
            return 'error', None
        except Exception as e:
            self.logger.error(f"Unexpected error checking docket status: {str(e)}", exc_info=True)
            return 'error', None

    def query_pacer_by_mdl_num(self, mdl_num: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> \
            List[Dict[str, Any]]:
        start_date_fmt = datetime.strptime(start_date, '%Y%m%d').strftime('%Y%m%d') if start_date else None
        end_date_fmt = datetime.strptime(end_date, '%Y%m%d').strftime('%Y%m%d') if end_date else None

        try:
            # MdlNum-FilingDate-index is available
            key_condition_expression = Key('MdlNum').eq(mdl_num)
            if start_date_fmt and end_date_fmt:
                key_condition_expression &= Key('FilingDate').between(start_date_fmt, end_date_fmt)
            elif start_date_fmt:
                key_condition_expression &= Key('FilingDate').gte(start_date_fmt)
            elif end_date_fmt:
                key_condition_expression &= Key('FilingDate').lte(end_date_fmt)

            # Using base class query_items
            items_queried = self.query_items(
                key_conditions={'MdlNum': mdl_num,
                                'FilingDate': {'between_or_gte_or_lte': (start_date_fmt, end_date_fmt)}},
                # Adapt for base query_items flexibility
                index_name='MdlNum-FilingDate-index'
            )
            # The above key_conditions format needs to be handled by query_items logic,
            # or we revert to manual query construction here.
            # For simplicity, let's use manual query construction for this specific GSI and key structure.

            all_items_response_raw = []
            current_response = self.table.query(
                IndexName='MdlNum-FilingDate-index',
                KeyConditionExpression=key_condition_expression
            )
            all_items_response_raw.extend(current_response.get('Items', []))
            while 'LastEvaluatedKey' in current_response:
                current_response = self.table.query(
                    IndexName='MdlNum-FilingDate-index',
                    KeyConditionExpression=key_condition_expression,
                    ExclusiveStartKey=current_response['LastEvaluatedKey']
                )
                all_items_response_raw.extend(current_response.get('Items', []))

            return replace_decimals(all_items_response_raw)

        except ClientError as e:
            self.logger.error(f"Failed to query PacerDockets by MdlNum {mdl_num}: {e.response['Error']['Message']}")
            return []
        except Exception as e:
            self.logger.error(f"An unexpected error occurred while querying PacerDockets by MdlNum: {str(e)}",
                              exc_info=True)
            return []

    def query_pacer_by_filing_date(self, filing_date: str) -> List[Dict[str, Any]]:
        try:
            # Must use FilingDate-index GSI for PacerDockets
            # Using base class query_items
            items_queried = self.query_items(
                key_conditions={'FilingDate': filing_date},
                index_name='FilingDate-index'
            )
            return items_queried  # query_items already handles pagination and decimals
        except ClientError as e:  # query_items handles its own logging
            return []
        except Exception as e:
            self.logger.error(f"An unexpected error occurred while querying PacerDockets by FilingDate: {str(e)}",
                              exc_info=True)
            return []

    def query_pacer_by_court_id_and_docket_num(self, court_id: str, docket_num: str) -> List[Dict[str, Any]]:
        try:
            # CourtId-DocketNum-index is available
            # Using base class query_items
            items_queried = self.query_items(
                key_conditions={'CourtId': court_id, 'DocketNum': docket_num},
                index_name='CourtId-DocketNum-index'
            )
            if items_queried:
                self.logger.info(f"Found item(s) for CourtId: {court_id}, DocketNum: {docket_num}")
            else:
                self.logger.info(f"No item found for CourtId: {court_id}, DocketNum: {docket_num}")
            return items_queried
        except ClientError as e:  # query_items handles its own logging
            return []
        except Exception as e:
            self.logger.error(
                f"An unexpected error occurred while querying PacerDockets by CourtId and DocketNum: {str(e)}",
                exc_info=True)
            return []

    def get_mdl_dockets_by_date_range(self, mdl_num: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        self.logger.info(f"Querying MDL {mdl_num} from {start_date} to {end_date} on PacerDockets table")
        projection_expression = (
            "FilingDate, DocketNum, CourtId, AddedOn, Defendant, InitialFilingDate, "
            "IsRemoval, TransferredIn, LawFirm, PendingCto, Plaintiff, NumPlaintiffs, "
            "TransferorCourtId, TransferorDocketNum, Versus, S3Link"  # Added S3Link
        )
        try:
            start_date_fmt = datetime.strptime(start_date, '%Y%m%d').strftime('%Y%m%d')
            end_date_fmt = datetime.strptime(end_date, '%Y%m%d').strftime('%Y%m%d')

            # Using base query_items method
            # This might require query_items to handle complex key conditions better for GSI range keys.
            # For now, direct query with pagination for precision.
            key_condition_expression = Key('MdlNum').eq(mdl_num) & Key('FilingDate').between(start_date_fmt,
                                                                                             end_date_fmt)

            all_items_response_raw = []
            current_response = self.table.query(
                IndexName='MdlNum-FilingDate-index',
                KeyConditionExpression=key_condition_expression,
                ProjectionExpression=projection_expression
            )
            all_items_response_raw.extend(current_response.get('Items', []))
            while 'LastEvaluatedKey' in current_response:
                self.logger.debug(f"Paginating for MDL {mdl_num} after key: {current_response['LastEvaluatedKey']}")
                current_response = self.table.query(
                    IndexName='MdlNum-FilingDate-index',
                    KeyConditionExpression=key_condition_expression,
                    ProjectionExpression=projection_expression,
                    ExclusiveStartKey=current_response['LastEvaluatedKey']
                )
                all_items_response_raw.extend(current_response.get('Items', []))

            items = replace_decimals(all_items_response_raw)
            self.logger.info(
                f"Found and processed {len(items)} items for MDL {mdl_num} between {start_date} and {end_date}")
            return items

        except ClientError as e:
            self.logger.error(
                f"Failed to query PacerDockets for MdlNum {mdl_num} in date range {start_date} to {end_date}: {e.response['Error']['Message']}")
            return []
        except ValueError as e:  # For strptime
            self.logger.error(f"Invalid date format provided for MDL query: {e}")
            return []
        except Exception as e:
            self.logger.error(f"An unexpected error occurred while querying PacerDockets MDL by date range: {str(e)}",
                              exc_info=True)
            return []

    def check_docket_exists_by_id_num(self, court_id: str, docket_num: str) -> bool:
        if not hasattr(self, 'table') or self.table is None:
            self.logger.error("DynamoDB table object is not initialized in PacerDocketManager.")
            return False

        if not court_id or not docket_num:
            self.logger.warning("Court ID or Docket Number missing for existence check.")
            return False

        gsi_name = 'CourtId-DocketNum-index'
        self.logger.debug(
            f"Checking PacerDockets existence using GSI '{gsi_name}' for CourtId: {court_id}, DocketNum: {docket_num}")
        try:
            response = self.table.query(
                IndexName=gsi_name,
                KeyConditionExpression=Key('CourtId').eq(court_id) & Key('DocketNum').eq(docket_num),
                Select='COUNT'
            )
            count = response.get('Count', 0)
            exists = count > 0
            self.logger.debug(
                f"PacerDockets GSI check result for {court_id}-{docket_num}: Exists={exists} (Count={count})")
            return exists
        except ClientError as e:
            # Error handling as in original
            error_code = e.response.get('Error', {}).get('Code')
            if error_code == 'ResourceNotFoundException':
                self.logger.error(
                    f"DynamoDB ResourceNotFoundException for PacerDockets (Table '{self.table_name}' or GSI '{gsi_name}').")
            elif error_code == 'ValidationException':
                self.logger.error(
                    f"DynamoDB ValidationException for PacerDockets {court_id}-{docket_num} GSI '{gsi_name}'. Error: {e}")
            else:
                self.logger.error(
                    f"DynamoDB ClientError checking PacerDockets existence via GSI for {court_id}-{docket_num}: {e}")
            return False
        except Exception as e:
            self.logger.error(
                f"Unexpected error checking PacerDockets existence via GSI for {court_id}-{docket_num}: {e}",
                exc_info=True)
            return False

    def search_pacer_by_law_firm(self, law_firm: str) -> List[Dict[str, Any]]:
        # LawFirm-FilingDate-index is available on PacerDockets
        # This method scans a GSI. This can be very inefficient.
        # If performance is an issue, consider OpenSearch or a different GSI structure.
        # For now, maintaining original behavior of scanning the GSI.
        all_items_on_gsi_raw = []
        try:
            scan_kwargs = {
                'IndexName': 'LawFirm-FilingDate-index',
                'ProjectionExpression': "FilingDate, LawFirm, CourtId, MdlNum, DocketNum, Versus"
            }
            # The original implementation scanned this GSI without a filter, then Python filtered.
            # DynamoDB scan FilterExpression is case-sensitive.
            # To maintain case-insensitive search, we must scan and filter in Python.

            current_response = self.table.scan(**scan_kwargs)
            all_items_on_gsi_raw.extend(current_response.get('Items', []))
            while 'LastEvaluatedKey' in current_response:
                scan_kwargs['ExclusiveStartKey'] = current_response['LastEvaluatedKey']
                current_response = self.table.scan(**scan_kwargs)
                all_items_on_gsi_raw.extend(current_response.get('Items', []))

            items_on_gsi = replace_decimals(all_items_on_gsi_raw)
            filtered_items = [item for item in items_on_gsi if
                              item.get('LawFirm') and law_firm.lower() in item['LawFirm'].lower()]
            return filtered_items
        except ClientError as e:
            if e.response['Error']['Code'] == 'ValidationException' and 'index' in e.response['Error'][
                'Message'].lower():
                self.logger.error(
                    f"Index 'LawFirm-FilingDate-index' on PacerDockets may not exist or query is invalid: {e.response['Error']['Message']}")
            else:
                self.logger.error(f"Failed to search PacerDockets by law firm: {e.response['Error']['Message']}")
            return []
        except Exception as e:
            self.logger.error(f"An unexpected error occurred searching PacerDockets by law firm: {str(e)}",
                              exc_info=True)
            return []

    def search_pacer_by_versus(self, versus: str) -> List[Dict[str, Any]]:
        # This scans the entire table. VERY INEFFICIENT.
        # Consider OpenSearch or a GSI if this is used frequently.
        all_items_raw = []
        try:
            # FilterExpression is case-sensitive. If case-insensitivity is needed, filter in Python.
            scan_kwargs = {
                'FilterExpression': "contains(Versus, :versus)",
                'ExpressionAttributeValues': {":versus": versus}
            }
            # Using base scan_table for pagination and decimal conversion
            filtered_items = list(self.scan_table(**scan_kwargs))
            return filtered_items
        except ClientError as e:  # scan_table handles its own logging
            return []
        except Exception as e:
            self.logger.error(f"An unexpected error occurred searching PacerDockets by Versus: {str(e)}", exc_info=True)
            return []

    def query_by_transferee_court_id_and_docket_num(self, transferee_court_id: str, transferee_docket_num: str) -> List[
        Dict[str, Any]]:
        try:
            # TransfereeCourtId-TransfereeDocketNum-index is available
            # Using base query_items
            items_queried = self.query_items(
                key_conditions={'TransfereeCourtId': transferee_court_id, 'TransfereeDocketNum': transferee_docket_num},
                index_name='TransfereeCourtId-TransfereeDocketNum-index'
            )
            if items_queried:
                self.logger.info(
                    f"Found PacerDockets item(s) for TransfereeCourtId: {transferee_court_id}, TransfereeDocketNum: {transferee_docket_num}")
            else:
                self.logger.info(
                    f"No PacerDockets item found for TransfereeCourtId: {transferee_court_id}, TransfereeDocketNum: {transferee_docket_num}")
            return items_queried
        except ClientError as e:  # query_items handles its own logging
            return []
        except Exception as e:
            self.logger.error(
                f"An unexpected error occurred while querying PacerDockets by TransfereeCourtId and TransfereeDocketNum: {str(e)}",
                exc_info=True)
            return []

    def search_pacer_by_law_firm_with_totals(self, law_firm: str) -> List[Dict[str, Any]]:
        # This scans the entire table. VERY INEFFICIENT.
        all_items_raw = list(self.scan_table())  # scan_table handles decimals
        search_term = law_firm.lower()
        # Case-insensitive filter in Python
        filtered_items = [item for item in all_items_raw if search_term in item.get('LawFirm', '').lower()]

        result_list = []
        totals = defaultdict(lambda: defaultdict(int))

        for item in filtered_items:
            docket_num = item.get('DocketNum', 'N/A')
            mdl_num = item.get('MdlNum', 'N/A')
            title = item.get('Title', 'N/A')
            law_firm_val = item.get('LawFirm', 'N/A')
            filing_date = item.get('FilingDate', 'N/A')
            # CourtId is PK part, so should exist.
            court_id = item.get('CourtId', 'N/A')

            result_list.append({
                'FilingDate': filing_date,
                'DocketNum': docket_num,
                'MdlNum': mdl_num,
                'Title': title,
                'LawFirm': law_firm_val,
                'CourtId': court_id  # Added CourtId
            })
            totals[mdl_num][law_firm_val] += 1

        # Convert defaultdict to dict for JSON serialization if this list is dumped
        result_list.append({'Subtotals': {k: dict(v) for k, v in totals.items()}})
        return result_list

    def summarize_filings_by_law_firm_for_mdl(self, mdl_num: str, start_date: str, end_date: str) -> Tuple[
        pd.DataFrame, pd.DataFrame]:
        start_date_fmt = datetime.strptime(start_date, '%Y%m%d').strftime('%Y%m%d')
        end_date_fmt = datetime.strptime(end_date, '%Y%m%d').strftime('%Y%m%d')

        try:
            # MdlNum-FilingDate-index is available
            key_condition_expression = Key('MdlNum').eq(mdl_num) & Key('FilingDate').between(start_date_fmt,
                                                                                             end_date_fmt)
            projection_expression = "FilingDate, MdlNum, LawFirm, DocketNum, Title, Versus, S3Link, CourtId"

            all_items_response_raw = []
            current_response = self.table.query(
                IndexName='MdlNum-FilingDate-index',
                KeyConditionExpression=key_condition_expression,
                ProjectionExpression=projection_expression
            )
            all_items_response_raw.extend(current_response.get('Items', []))
            while 'LastEvaluatedKey' in current_response:
                current_response = self.table.query(
                    IndexName='MdlNum-FilingDate-index',
                    KeyConditionExpression=key_condition_expression,
                    ProjectionExpression=projection_expression,
                    ExclusiveStartKey=current_response['LastEvaluatedKey']
                )
                all_items_response_raw.extend(current_response.get('Items', []))

            items = replace_decimals(all_items_response_raw)
            summary = defaultdict(int)
            filings = []

            for item in items:
                law_firm = item.get('LawFirm', 'N/A')
                summary[law_firm] += 1
                if 'S3Link' in item and isinstance(item['S3Link'], str):
                    item['S3Link'] = item['S3Link'].replace('https://lexgenius.s3.us-west-2.amazonaws.com',
                                                            'https://cdn.lexgenius.ai')
                filings.append(item)

            summary_df = pd.DataFrame(
                [{"LawFirm": lf, "TotalFilings": total} for lf, total in summary.items()])  # Renamed law_firm variable
            filings_df = pd.DataFrame(filings)
            return summary_df, filings_df
        except ClientError as e:
            self.logger.error(
                f"Failed to summarize PacerDockets filings for MDL {mdl_num}: {e.response['Error']['Message']}")
            return pd.DataFrame(), pd.DataFrame()
        except Exception as e:
            self.logger.error(f"An unexpected error occurred while summarizing PacerDockets filings: {str(e)}",
                              exc_info=True)
            return pd.DataFrame(), pd.DataFrame()

    def get_pacer_records_df(self) -> pd.DataFrame:
        try:
            all_items = list(self.scan_table())  # scan_table handles decimals
            if not all_items:
                self.logger.warning("No items found in the PacerDockets table.")
                return pd.DataFrame()

            # Columns adjusted for PacerDockets PK
            required_columns = ['CourtId', 'DocketNum', 'FilingDate', 'Attorney', 'AttorneysGpt', 'S3Link', 'Title',
                                'MdlNum']
            filtered_records = [
                {key: item.get(key) for key in required_columns}
                for item in all_items
            ]
            df = pd.DataFrame(filtered_records)
            self.logger.info(f"DataFrame created with {len(df)} records from the PacerDockets table.")
            return df
        except Exception as e:
            self.logger.error(f"An error occurred while retrieving PacerDockets records: {str(e)}", exc_info=True)
            return pd.DataFrame()

    def count_identical_court_docket_combinations(self) -> Dict[Tuple[str, str], int]:
        try:
            items = list(self.scan_table())  # scan_table handles decimals
            combination_counts = {}
            for item in items:
                court_id = item.get('CourtId')
                docket_num = item.get('DocketNum')
                if court_id and docket_num:  # PK fields, should always exist if item is valid
                    key = (court_id, docket_num)
                    combination_counts[key] = combination_counts.get(key, 0) + 1

            duplicate_counts = {key: count for key, count in combination_counts.items() if count > 1}
            return duplicate_counts
        except ClientError as e:  # scan_table handles its own logging
            return {}
        except Exception as e:
            self.logger.error(f"An unexpected error occurred in count_identical_court_docket_combinations: {str(e)}",
                              exc_info=True)
            return {}

    def get_dockets_for_bubble(self, start_date: str, end_date: str, court_id: Optional[str] = None) -> pd.DataFrame:
        try:
            projection_expression = "DocketNum, CourtId, FilingDate, Title"
            items = []
            if court_id:
                # CourtId-FilingDate-index is available
                # This GSI has CourtId as HASH, FilingDate as RANGE.
                # Using base query_items to handle this.
                # Need to ensure query_items can create KCE for range key 'between'.
                # For now, constructing key_condition_expression manually for clarity with range key.
                key_expr = Key('CourtId').eq(court_id) & Key('FilingDate').between(start_date, end_date)
                items = self.query_items(
                    key_condition_expression_direct=key_expr,  # Assuming query_items can take a direct KCE object
                    index_name='CourtId-FilingDate-index',
                    ProjectionExpression=projection_expression
                )
            else:
                # No court_id, query by FilingDate range using FilingDate-index (HASH key FilingDate)
                # This means we iterate through dates, or query_by_date handles it.
                # query_by_date in base is already set up for this using FilingDate-index on PacerDockets.
                items = self.query_by_date(
                    start_date_str=start_date,
                    end_date_str=end_date,
                    # date_field and index_name inferred by base for PacerDockets
                    projection_expression=projection_expression
                )
            return pd.DataFrame(items)
        except ClientError as e:  # query_items/query_by_date handle their own logging
            return pd.DataFrame()
        except Exception as e:
            self.logger.error(f"An unexpected error occurred in get_dockets_for_bubble: {str(e)}", exc_info=True)
            return pd.DataFrame()

    def search_and_update_record(self, search_string: str, new_mdl_num: str, new_title: str) -> None:
        try:
            items_raw = list(self.scan_table(  # scan_table handles decimals
                ProjectionExpression="FilingDate, CourtId, DocketNum, Title, S3Link, Allegations, Claims, MdlNum"
            ))
            # Case-insensitive filter in Python
            filtered_items = [item for item in items_raw if
                              item.get('Title') and search_string.lower() in item['Title'].lower()]

            if not filtered_items:
                console.print("No records found matching the search criteria.")
                return

            console.print(f"Found {len(filtered_items)} records potentially matching.")
            console.print("Records to potentially update (using pprint):")
            console.print(pformat(filtered_items))

            confirm = Prompt.ask("Do you want to continue with these updates?", choices=["y", "n"], default="n",
                                 console=console)
            if confirm.lower() != 'y':
                console.print("Operation canceled.")
                return

            console.print("\nPlease review the records again before final confirmation:")
            console.print(pformat(filtered_items))

            confirm_final = Prompt.ask(
                f"Are you sure you want to replace MdlNum with '{new_mdl_num}' and Title with '{new_title}' for these records? Please type 'confirm' to proceed:",
                console=console
            )
            if confirm_final.lower() == 'confirm':
                updates_made = 0
                for item in filtered_items:
                    # PK is CourtId, DocketNum
                    key = {'CourtId': item['CourtId'], 'DocketNum': item['DocketNum']}
                    update_data = {'MdlNum': new_mdl_num, 'Title': new_title}
                    success = self.update_item(key, update_data, consistent_read_verify=False)
                    if success:
                        updates_made += 1
                        item['MdlNum'] = new_mdl_num
                        item['Title'] = new_title
                    else:
                        self.logger.error(f"Failed to update record: {key}")

                console.print(f"\nUpdate API calls completed for {updates_made} records.")
                console.print("Records after update attempt:")
                console.print(pformat(filtered_items))
            else:
                console.print("No changes made.")
        except ClientError as e:  # update_item handles its own logging
            pass
        except Exception as e:
            self.logger.error(f"An unexpected error occurred in search_and_update_record: {str(e)}", exc_info=True)

    def update_added_on_date(self, current_added_on: str, new_added_on: str) -> None:
        try:
            items = self.get_records_by_added_on(current_added_on)  # Handles decimals and pagination
            if not items:
                self.logger.info(f"No records found with AddedOn date: {current_added_on}")
                return

            updates_made = 0
            for item in items:
                # PK is CourtId, DocketNum
                key = {'CourtId': item['CourtId'], 'DocketNum': item['DocketNum']}
                update_data = {'AddedOn': new_added_on}
                success = self.update_item(key, update_data, consistent_read_verify=False)
                if success:
                    updates_made += 1
                else:
                    self.logger.error(f"Failed to update AddedOn for record: {key}")

            self.logger.info(
                f"Attempted to update {len(items)} records with AddedOn date {current_added_on} to {new_added_on}. Successful API calls: {updates_made}")
        except ClientError as e:  # update_item handles its own logging
            pass
        except Exception as e:
            self.logger.error(f"An unexpected error occurred while updating AddedOn date: {str(e)}", exc_info=True)

    def scan_for_duplicates(self) -> List[Dict[str, Any]]:
        try:
            duplicates_map = {}
            all_items = list(self.scan_table())  # scan_table handles decimals

            for item in all_items:
                court_id = item.get('CourtId')
                docket_num = item.get('DocketNum')
                if not court_id or not docket_num: continue  # Should not happen for valid items

                key = (court_id, docket_num)  # PK
                if key not in duplicates_map:
                    duplicates_map[key] = []
                duplicates_map[key].append(item)

            final_duplicates = []
            for key, items_list in duplicates_map.items():
                if len(items_list) > 1:  # If more than one item has the same PK (should not happen with put_item logic)
                    final_duplicates.extend(items_list)

            # This method's original intent might have been to find items that are "logical" duplicates
            # based on CourtId and DocketNum, even if their primary keys (e.g. FilingDate for old Pacer table) differed.
            # For PacerDockets, CourtId/DocketNum IS the PK. So true duplicates by PK are impossible if using put_item.
            # If the goal is to find items that became duplicates during data migration or transformation,
            # then this check is on CourtId/DocketNum as attributes, which is fine.

            if final_duplicates:
                with open('pacerdockets_duplicate_court_id_docket_num.json', 'w') as file:  # Filename changed
                    json.dump(final_duplicates, file, indent=4, default=str)
                self.logger.info(
                    f"Found {len(final_duplicates)} PacerDockets items considered duplicates by CourtId/DocketNum. Saved to 'pacerdockets_duplicate_court_id_docket_num.json'.")
            else:
                self.logger.info("No duplicate items (by CourtId/DocketNum) found in PacerDockets.")
            return final_duplicates
        except ClientError as e:  # scan_table handles its own logging
            return []
        except Exception as e:
            self.logger.error(f"An unexpected error occurred while scanning PacerDockets for duplicates: {str(e)}",
                              exc_info=True)
            return []

    def get_mdl_summary(self, date_str: str) -> pd.DataFrame:
        try:
            end_date = datetime.strptime(date_str, '%Y%m%d')
            start_date = end_date - timedelta(days=29)
            attributes_to_project = ["MdlNum", "Versus", "NumPlaintiffs",
                                     "FilingDate"]  # Added FilingDate for sorting before drop
            projection_expr_str = ", ".join(attributes_to_project)

            self.logger.info(
                f"Querying PacerDockets MDL summary for {date_str} ({start_date.strftime('%Y%m%d')} - {end_date.strftime('%Y%m%d')}) "
                f"with projection: {projection_expr_str}")

            # query_by_date for PacerDockets uses FilingDate-index
            items = self.query_by_date(
                start_date_str=start_date.strftime('%Y%m%d'),
                end_date_str=end_date.strftime('%Y%m%d'),
                projection_expression=projection_expr_str
            )

            if not items:
                self.logger.info("No items found for PacerDockets MDL summary in the date range.")
                return pd.DataFrame(columns=['MdlNum', 'Total'])

            df = pd.DataFrame(items)
            self.logger.info(f"Retrieved {len(df)} items for PacerDockets MDL summary before processing.")
            if df.empty: return pd.DataFrame(columns=['MdlNum', 'Total'])
            if 'MdlNum' not in df.columns:
                self.logger.warning(f"MdlNum column missing in PacerDockets projected query result for MDL summary.")
                return pd.DataFrame(columns=['MdlNum', 'Total'])

            # Sort by FilingDate if available, to make drop_duplicates more deterministic
            if 'FilingDate' in df.columns:
                df = df.sort_values(by='FilingDate')  # Sort before dropping

            if 'Versus' in df.columns:
                df = df.drop_duplicates(subset=['MdlNum', 'Versus'], keep='first')
            else:
                self.logger.warning("Versus column not found in PacerDockets, skipping drop_duplicates on Versus.")

            if 'NumPlaintiffs' in df.columns:
                df['filing_count'] = pd.to_numeric(df['NumPlaintiffs'], errors='coerce').fillna(1).astype(int)
            else:
                self.logger.warning(
                    "NumPlaintiffs column not found in PacerDockets results. Defaulting filing_count to 1.")
                df['filing_count'] = 1

            df_cleaned_mdl = df.dropna(subset=['MdlNum'])
            if len(df_cleaned_mdl) < len(df):
                self.logger.warning(
                    f"Dropped {len(df) - len(df_cleaned_mdl)} rows with missing MdlNum from PacerDockets before grouping.")
            if df_cleaned_mdl.empty:
                self.logger.info("PacerDockets DataFrame is empty after cleaning MdlNum. No summary to generate.")
                return pd.DataFrame(columns=['MdlNum', 'Total'])

            mdl_counts = df_cleaned_mdl.groupby('MdlNum')['filing_count'].sum().reset_index()
            mdl_counts.columns = ['MdlNum', 'Total']
            result_df = mdl_counts.sort_values('Total', ascending=False).reset_index(drop=True)
            self.logger.info(f"Generated PacerDockets MDL summary with {len(result_df)} unique MDLs.")
            return result_df
        except Exception as e:
            self.logger.error(f"An error occurred while getting PacerDockets MDL summary for date {date_str}: {str(e)}",
                              exc_info=True)
            return pd.DataFrame(columns=['MdlNum', 'Total'])

    def get_mdl_summary2(self, date_str: str) -> pd.DataFrame:
        # Simplified summary, just counts MdlNum occurrences from items in date range
        try:
            end_date = datetime.strptime(date_str, '%Y%m%d')
            start_date = end_date - timedelta(days=29)
            # query_by_date for PacerDockets uses FilingDate-index
            items = self.query_by_date(
                start_date_str=start_date.strftime('%Y%m%d'),
                end_date_str=end_date.strftime('%Y%m%d'),
                projection_expression='MdlNum'
            )
            mdl_counts = {}
            for item in items:
                mdl_num = item.get('MdlNum')
                if mdl_num:
                    mdl_counts[mdl_num] = mdl_counts.get(mdl_num, 0) + 1
            if not mdl_counts: return pd.DataFrame(columns=['MdlNum', 'Total'])
            df = pd.DataFrame([{'MdlNum': mdl, 'Total': count} for mdl, count in mdl_counts.items()])
            return df.sort_values('Total', ascending=False).reset_index(drop=True)
        except Exception as e:
            self.logger.error(f"An error occurred while getting PacerDockets MDL summary2: {str(e)}", exc_info=True)
            return pd.DataFrame(columns=['MdlNum', 'Total'])

    def get_docket_items(self, court_id: str, docket_num: str) -> List[Dict[str, Any]]:
        if not self.table:
            self.logger.error("DynamoDB table (PacerDockets) not initialized.")
            return []
        try:
            # CourtId-DocketNum-index is available
            # Using base query_items
            items_queried = self.query_items(
                key_conditions={'CourtId': court_id, 'DocketNum': docket_num},
                index_name='CourtId-DocketNum-index'
            )
            if items_queried:
                try:
                    with open('pacerdockets_get_docket_items_results.json', 'w') as file:  # Filename changed
                        json.dump(items_queried, file, indent=4, default=str)
                    self.logger.info(
                        f"Saved {len(items_queried)} PacerDockets items to 'pacerdockets_get_docket_items_results.json'")
                except Exception as json_err:
                    self.logger.error(f"Failed to save PacerDockets get_docket_items results to JSON: {json_err}")
                return items_queried
            else:
                self.logger.info(
                    f"No matching PacerDockets items found for CourtId: {court_id}, DocketNum: {docket_num}")
                return []
        except ClientError as e:  # query_items handles its own logging
            return []
        except Exception as e:
            self.logger.error(f"An unexpected error occurred in PacerDockets get_docket_items: {e}", exc_info=True)
            return []

    def query_by_transferee_docket(self, court_id: str, docket_num: str) -> List[Dict[str, Any]]:
        # This method's original logic was to query CourtId-DocketNum-index,
        # effectively the same as query_pacer_by_court_id_and_docket_num for PacerDockets
        # after variations. The name is misleading if it's not actually querying by transferee fields.
        # Assuming it should query by the actual TransfereeCourtId / TransfereeDocketNum fields
        # using the 'TransfereeCourtId-TransfereeDocketNum-index'.

        self.logger.info(f"Querying PacerDockets by Transferee info: CourtId={court_id}, DocketNum={docket_num}")
        try:
            items_queried = self.query_items(
                key_conditions={'TransfereeCourtId': court_id, 'TransfereeDocketNum': docket_num},
                index_name='TransfereeCourtId-TransfereeDocketNum-index'
            )
            if not items_queried:
                self.logger.info(
                    f"No PacerDockets item found for TransfereeCourtId: {court_id}, TransfereeDocketNum: {docket_num}")
            return items_queried
        except ClientError as e:  # query_items handles its own logging
            return []
        except Exception as e:
            self.logger.error(
                f"An unexpected error occurred while querying PacerDockets by transferee docket: {str(e)}",
                exc_info=True)
            return []

    def interactive_mdl_query_and_update(self) -> None:
        try:
            mdl_num = Prompt.ask("Enter the MdlNum to search for in PacerDockets", console=console)
            # MdlNum-FilingDate-index is available
            items = self.query_items(
                key_conditions={'MdlNum': mdl_num},
                index_name='MdlNum-FilingDate-index',
                ProjectionExpression="FilingDate, CourtId, DocketNum, Title, S3Link, Allegations, Claims, MdlNum"
            )
            if not items:
                console.print(f"No records found for MdlNum: {mdl_num} in PacerDockets")
                return

            console.print(f"Found {len(items)} records for MdlNum: {mdl_num}")
            console.print("Records found (using pprint):")
            console.print(pformat(items))

            update_prompt = Prompt.ask("Do you want to update the MdlNum and Title for these PacerDockets records?",
                                       choices=["y", "n"], default="n", console=console)
            if update_prompt.lower() != 'y':
                console.print("Operation canceled.")
                return

            new_mdl_num = Prompt.ask("Enter the new MdlNum", console=console)
            new_title = Prompt.ask("Enter the new Title", console=console)
            console.print("Records to potentially update (using pprint):")
            console.print(pformat(items))
            confirmation_prompt = Prompt.ask(
                f"Confirm update PacerDockets records to MdlNum '{new_mdl_num}' and Title '{new_title}'? (Type 'confirm')",
                console=console
            )
            if confirmation_prompt.lower() != 'confirm':
                console.print("Update canceled.")
                return

            updates_made = 0
            for item in items:
                # PK is CourtId, DocketNum
                key = {'CourtId': item['CourtId'], 'DocketNum': item['DocketNum']}
                update_data = {'MdlNum': new_mdl_num, 'Title': new_title}
                success = self.update_item(key, update_data, consistent_read_verify=False)
                if success:
                    updates_made += 1
                else:
                    self.logger.error(f"Failed update PacerDockets record: {key}")
            console.print(f"Update API calls completed for {updates_made} PacerDockets records.")
        except ClientError as e:  # update_item handles its own logging
            pass
        except Exception as e:
            self.logger.error(f"An unexpected error occurred: {str(e)}", exc_info=True)

    def interactive_title_search_and_update(self) -> None:
        try:
            search_string = Prompt.ask("Enter the search string to search for in PacerDockets Title", console=console)
            items = self.search_pacer_by_title(search_string)  # Handles decimals

            if not items:
                console.print(f"No PacerDockets records found with title containing: {search_string}")
                return

            console.print(f"Found {len(items)} PacerDockets records with title containing: {search_string}")
            console.print("Records found (using pprint):")
            console.print(pformat(items))

            update_prompt = Prompt.ask("Do you want to update the MdlNum and Title for these PacerDockets records?",
                                       choices=["y", "n"], default="n", console=console)
            if update_prompt.lower() != 'y':
                console.print("Operation canceled.")
                return

            new_mdl_num = Prompt.ask("Enter the new MdlNum", console=console)
            new_title = Prompt.ask("Enter the new Title", console=console)
            console.print("Records to potentially update (using pprint):")
            console.print(pformat(items))
            confirmation_prompt = Prompt.ask(
                f"Confirm update PacerDockets records to MdlNum '{new_mdl_num}' and Title '{new_title}'? (Type 'confirm')",
                console=console
            )
            if confirmation_prompt.lower() != 'confirm':
                console.print("Update canceled.")
                return

            updates_made = 0
            for item in items:
                if 'CourtId' in item and 'DocketNum' in item:  # PK fields
                    key = {'CourtId': item['CourtId'], 'DocketNum': item['DocketNum']}
                    update_data = {'MdlNum': new_mdl_num, 'Title': new_title}
                    success = self.update_item(key, update_data, consistent_read_verify=False)
                    if success:
                        updates_made += 1
                    else:
                        self.logger.error(f"Failed update PacerDockets record: {key}")
                else:
                    self.logger.warning(
                        f"Skipping update for PacerDockets item missing key fields: {str(item)[:100]}...")
            console.print(f"Update API calls completed for {updates_made} PacerDockets records.")
        except ClientError as e:  # update_item handles its own logging
            pass
        except Exception as e:
            self.logger.error(f"An unexpected error occurred: {str(e)}", exc_info=True)

    def query_transfer_info(self, court_id: str, docket_num: str) -> dict:
        result = {}
        item_found_raw = None
        log_prefix = f"[query_transfer_info_pacerdockets/{court_id}:{docket_num}]"

        def safe_query_local(index_name, key_expr_obj):
            try:
                self.logger.debug(f"{log_prefix} Running safe_query_local on index '{index_name}'")
                response = self.table.query(IndexName=index_name, KeyConditionExpression=key_expr_obj)
                items_list = response.get('Items', [])
                self.logger.debug(f"{log_prefix} safe_query_local on '{index_name}' found {len(items_list)} items.")
                return items_list
            except ClientError as client_e:
                self.logger.error(
                    f"{log_prefix} Query failed on index {index_name}: {client_e.response['Error']['Message']}")
                return []
            except Exception as general_e:
                self.logger.error(f"{log_prefix} Unexpected error during query on index {index_name}: {general_e}",
                                  exc_info=True)
                return []

        try:
            self.logger.debug(f"{log_prefix} Attempting query on CourtId-DocketNum-index for PacerDockets")
            items_primary_raw = safe_query_local('CourtId-DocketNum-index',
                                                 Key('CourtId').eq(court_id) & Key('DocketNum').eq(docket_num))
            if items_primary_raw:
                item_found_raw = items_primary_raw[0]
                self.logger.debug(f"{log_prefix} Found item via CourtId-DocketNum-index (PacerDockets).")

            if not item_found_raw:
                self.logger.debug(
                    f"{log_prefix} Not found via CourtId-DocketNum-index, trying TransfereeCourtId-TransfereeDocketNum-index (PacerDockets)")
                items_tf_raw = safe_query_local('TransfereeCourtId-TransfereeDocketNum-index',
                                                Key('TransfereeCourtId').eq(court_id) & Key('TransfereeDocketNum').eq(
                                                    docket_num))
                if items_tf_raw:
                    item_found_raw = items_tf_raw[0]
                    self.logger.debug(f"{log_prefix} Found PacerDockets item via Transferee index.")

            if not item_found_raw:
                self.logger.debug(
                    f"{log_prefix} Not found via previous, trying TransferorCourtId-TransferorDocketNum-index (PacerDockets)")
                items_tt_raw = safe_query_local('TransferorCourtId-TransferorDocketNum-index',
                                                Key('TransferorCourtId').eq(court_id) & Key('TransferorDocketNum').eq(
                                                    docket_num))
                if items_tt_raw:
                    item_found_raw = items_tt_raw[0]
                    self.logger.debug(f"{log_prefix} Found PacerDockets item via Transferor index.")

            if item_found_raw:
                item_processed = replace_decimals(item_found_raw)
                # Fill logic (can be simplified if the first found item is considered definitive)
                # The original fill logic might be redundant if GSIs correctly project all attributes.
                # For now, keeping the fill logic to maintain behavior parity.
                if not (item_processed.get('TransfereeCourtId') and item_processed.get('TransfereeDocketNum')):
                    items_tf_fill_raw = safe_query_local('TransfereeCourtId-TransfereeDocketNum-index',
                                                         Key('TransfereeCourtId').eq(court_id) & Key(
                                                             'TransfereeDocketNum').eq(docket_num))
                    if items_tf_fill_raw: item_tf_fill = replace_decimals(items_tf_fill_raw[0]); item_processed.update(
                        item_tf_fill)

                if not (item_processed.get('TransferorCourtId') and item_processed.get('TransferorDocketNum')):
                    items_tt_fill_raw = safe_query_local('TransferorCourtId-TransferorDocketNum-index',
                                                         Key('TransferorCourtId').eq(court_id) & Key(
                                                             'TransferorDocketNum').eq(docket_num))
                    if items_tt_fill_raw: item_tt_fill = replace_decimals(items_tt_fill_raw[0]); item_processed.update(
                        item_tt_fill)

                keys_to_include = ['FilingDate', 'MdlNum', 'LawFirm', 'CourtId',
                                   'TransfereeCourtId', 'TransfereeDocketNum',
                                   'TransferorCourtId', 'TransferorDocketNum',
                                   'S3Link', 's3_link']  # Case variations for S3Link

                for k_item in keys_to_include:
                    # Handle S3Link/s3_link precedence
                    if k_item == 's3_link' and result.get('S3Link') not in [None, "NA", ""]:
                        continue  # S3Link already populated

                    val_item = item_processed.get(k_item)

                    # If S3Link is None, try s3_link
                    if k_item == 'S3Link' and val_item is None:
                        val_item = item_processed.get('s3_link')
                        if val_item is not None:
                            self.logger.debug(f"{log_prefix} Used 's3_link' as fallback for 'S3Link'.")

                    result[
                        k_item if k_item != 's3_link' else 'S3Link'] = "NA" if val_item is None or val_item == "" else val_item

                # Clean up if s3_link was added to result but S3Link is preferred and different
                if 's3_link' in result and result.get('S3Link') != result.get('s3_link'):
                    if result.get('S3Link') in [None, "NA", ""]:  # If S3Link is bad, use s3_link
                        result['S3Link'] = result['s3_link']
                    result.pop('s3_link', None)


            else:  # No item found from any query
                self.logger.warning(f"{log_prefix} No PacerDockets item found after querying all relevant indices.")
                result = {"FilingDate": "NA", "MdlNum": "NA", "LawFirm": "NA", "CourtId": court_id, "S3Link": "NA",
                          "TransfereeCourtId": "NA", "TransfereeDocketNum": "NA",
                          "TransferorCourtId": "NA", "TransferorDocketNum": "NA"}
        except Exception as e:
            self.logger.error(f"{log_prefix} Error during PacerDockets query_transfer_info main block: {e}",
                              exc_info=True)
            result = {"FilingDate": "NA", "MdlNum": "NA", "LawFirm": "NA", "CourtId": court_id, "S3Link": "NA",
                      "TransfereeCourtId": "NA", "TransfereeDocketNum": "NA",
                      "TransferorCourtId": "NA", "TransferorDocketNum": "NA"}

        result['court_id_queried'] = court_id  # To distinguish from item's CourtId
        result['docket_num_queried'] = docket_num
        self.logger.debug(f"{log_prefix} Returning result for PacerDockets: {result}")
        return result


def display_results(items: List[Dict[str, Any]]):
    if not items:
        console.print("[bold yellow]No items found.[/bold yellow]")
        return

    # Determine columns dynamically or use a predefined set for PacerDockets
    # Example predefined set:
    predefined_cols = ['CourtId', 'DocketNum', 'FilingDate', 'MdlNum', 'Title', 'LawFirm', 'AddedOn', 'S3Link']
    # Get all unique keys from all items to be more robust
    all_keys = set()
    for item in items:
        all_keys.update(item.keys())

    # Order columns: predefined first, then others alphabetically
    display_columns = [col for col in predefined_cols if col in all_keys]
    remaining_cols = sorted([k for k in all_keys if k not in predefined_cols])
    display_columns.extend(remaining_cols)

    table = Table(title=f"Query Results ({len(items)} items)")
    for col in display_columns:
        table.add_column(col)
    for item in items:
        row_values = [str(item.get(col, 'N/A')) for col in display_columns]
        table.add_row(*row_values)
    console.print(table)


if __name__ == "__main__":
    from src.lib.config import load_config  # Ensure this path is correct for your structure
    import logging
    # --- Rich Prompt import ---
    from rich.prompt import Prompt  # For y/n confirmations

    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logging.getLogger('boto3').setLevel(logging.WARNING)
    logging.getLogger('botocore').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

    parser = argparse.ArgumentParser(description="Query the PacerDocketManager for PacerDockets table.")
    parser.add_argument("--docket-num", help="Docket number to query.")
    parser.add_argument("--court-id", help="Court ID to query.")
    parser.add_argument("--mdl-num", help="MDL number to query.")
    parser.add_argument("--filing-date", help="Filing date (YYYYMMDD) for GSI queries.")
    parser.add_argument("--start-date", help="Start date (YYYYMMDD) for date range queries.")
    parser.add_argument("--end-date", help="End date (YYYYMMDD) for date range queries.")
    parser.add_argument("--local", action="store_true", help="Use local DynamoDB.")
    parser.add_argument("--local-port", type=int, default=8000, help="Local DynamoDB port.")

    args = parser.parse_args()

    try:
        config = load_config('01/01/70')
        db = PacerDocketManager(config, use_local=args.local, local_port=args.local_port)  # Renamed class

        flags_provided = any(
            [args.docket_num, args.court_id, args.mdl_num, args.filing_date, args.start_date, args.end_date])

        if flags_provided:
            console.print("[bold blue]Executing CLI query on PacerDockets...[/bold blue]")
            items = []
            if args.court_id and args.docket_num and not any(
                    [args.mdl_num, args.filing_date, args.start_date, args.end_date]):
                items = db.query_pacer_by_court_id_and_docket_num(args.court_id, args.docket_num)
            elif args.mdl_num and args.start_date and args.end_date and not any(
                    [args.docket_num, args.court_id, args.filing_date]):
                items = db.get_mdl_dockets_by_date_range(args.mdl_num, args.start_date, args.end_date)
            elif args.mdl_num and not any(
                    [args.docket_num, args.court_id, args.filing_date, args.start_date, args.end_date]):
                items = db.query_pacer_by_mdl_num(args.mdl_num)
            elif args.filing_date and not any(
                    [args.docket_num, args.court_id, args.mdl_num, args.start_date, args.end_date]):
                items = db.query_pacer_by_filing_date(args.filing_date)  # Uses FilingDate-index
            elif args.start_date and args.end_date and not any(
                    [args.docket_num, args.court_id, args.mdl_num, args.filing_date]):
                items = db.query_pacer_by_date(args.start_date, args.end_date)  # Uses FilingDate-index
            else:
                console.print("[bold red]Invalid combination of flags provided for PacerDockets.[/bold red]")
                parser.print_help()

            if items is not None: display_results(items)

        else:  # Interactive mode
            console.print("[bold blue]Entering PacerDocketManager interactive mode...[/bold blue]")
            while True:
                console.print("\n[bold underline]PacerDocketManager Interactive Menu[/bold underline]")
                console.print("1. Query by Court ID and Docket Number")
                console.print("2. Query by MDL Number and Date Range (uses MdlNum-FilingDate-index)")
                console.print("3. Query by MDL Number (uses MdlNum-FilingDate-index)")
                console.print("4. Query by Filing Date (uses FilingDate-index)")
                console.print("5. Query by Date Range (uses FilingDate-index)")
                console.print("6. Get MDL Summary (uses FilingDate-index)")
                console.print("7. Search by Title and Update (interactive)")
                console.print("8. Search by MDL and Update (interactive)")
                console.print("q. Quit")

                choice = Prompt.ask("[bold green]Enter your choice: [/bold green]", console=console).strip().lower()
                if choice == 'q': break

                current_items = []  # Renamed from items to avoid scope issues
                try:
                    if choice == '1':
                        court_id = Prompt.ask("Enter Court ID", console=console).strip()
                        docket_num = Prompt.ask("Enter Docket Number", console=console).strip()
                        if court_id and docket_num:
                            current_items = db.query_pacer_by_court_id_and_docket_num(court_id, docket_num)
                        else:
                            console.print("[bold red]Court ID and Docket Number are required.[/bold red]")
                    elif choice == '2':
                        mdl_num = Prompt.ask("Enter MDL Number", console=console).strip()
                        start_date = Prompt.ask("Enter Start Date (YYYYMMDD)", console=console).strip()
                        end_date = Prompt.ask("Enter End Date (YYYYMMDD)", console=console).strip()
                        if mdl_num and start_date and end_date:
                            current_items = db.get_mdl_dockets_by_date_range(mdl_num, start_date, end_date)
                        else:
                            console.print("[bold red]MDL Number, Start Date, and End Date are required.[/bold red]")
                    elif choice == '3':
                        mdl_num = Prompt.ask("Enter MDL Number", console=console).strip()
                        if mdl_num:
                            current_items = db.query_pacer_by_mdl_num(mdl_num)
                        else:
                            console.print("[bold red]MDL Number is required.[/bold red]")
                    elif choice == '4':
                        filing_date = Prompt.ask("Enter Filing Date (YYYYMMDD)", console=console).strip()
                        if filing_date:
                            current_items = db.query_pacer_by_filing_date(filing_date)
                        else:
                            console.print("[bold red]Filing Date is required.[/bold red]")
                    elif choice == '5':
                        start_date = Prompt.ask("Enter Start Date (YYYYMMDD)", console=console).strip()
                        end_date = Prompt.ask("Enter End Date (YYYYMMDD)", console=console).strip()
                        if start_date and end_date:
                            current_items = db.query_pacer_by_date(start_date, end_date)
                        else:
                            console.print("[bold red]Start Date and End Date are required.[/bold red]")
                    elif choice == '6':
                        date_str = Prompt.ask("Enter End Date for MDL Summary (YYYYMMDD)", console=console).strip()
                        if date_str:
                            summary_df = db.get_mdl_summary(date_str)
                            if not summary_df.empty:
                                console.print(summary_df)
                            else:
                                console.print("[yellow]No MDL summary data found.[/yellow]")
                        else:
                            console.print("[red]End date is required.[/red]")
                    elif choice == '7':
                        db.interactive_title_search_and_update()
                    elif choice == '8':
                        db.interactive_mdl_query_and_update()
                    else:
                        console.print("[bold red]Invalid choice. Please try again.[/bold red]")
                        continue

                    if current_items is not None and choice not in ['6', '7', '8']: display_results(current_items)

                except Exception as query_err:
                    console.print(f"[bold red]An error occurred during the query: {query_err}[/bold red]")
                    logging.exception("Error during PacerDocketManager interactive query.")

    except ImportError as e:
        # Handle issues with config import if running from a different context
        print(f"ImportError: {e}. Ensure 'src.lib.config' is accessible or adjust import paths.")
        logging.exception("Error importing PacerDocketManager dependencies.")
    except Exception as main_err:
        logging.exception("Error during PacerDocketManager script execution.")
    finally:
        if 'db' in locals() and db.is_local and db._local_dynamodb_manager:
            console.print("[yellow]Stopping local DynamoDB container if it was started by this manager...[/yellow]")
            db._local_dynamodb_manager.stop_dynamodb()

    console.print("\nPacerDocketManager script finished.")