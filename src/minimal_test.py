import sys
import pprint
print("--- sys.path in minimal_test.py ---")
pprint.pprint(sys.path)
print("-------------------------------------")
print("Attempting to import numpy...")
try:
    import numpy
    print(f"Successfully imported numpy version {numpy.__version__}")
    print(f"Numpy location: {numpy.__file__}")
except ImportError as e:
    print(f"ERROR importing numpy: {e}")
    # Optionally add traceback printout if needed
    # import traceback
    # traceback.print_exc()

print("\nAttempting to import pandas...")
try:
    import pandas
    print(f"Successfully imported pandas version {pandas.__version__}")
    print(f"Pandas location: {pandas.__file__}")
except ImportError as e:
    print(f"ERROR importing pandas: {e}")
    # Optionally add traceback printout if needed
    # import traceback
    # traceback.print_exc()

print("\nTest finished.")