#!/usr/bin/env python3
# !/usr/bin/env python3
# Run from project root directory.
"""
dynamodb_cli.py - Manages synchronization between AWS and local DynamoDB.
                  Provides Full, Incremental, and Missing sync options with parallel processing.
"""
import contextlib
import logging
import os
import sys
import time
# Use ThreadPoolExecutor for I/O, ProcessPool if CPU bound tasks needed
# Use as_completed for concurrent results
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple

from tqdm import tqdm

# Botocore exceptions for specific handling

# Rich imports
try:
    from rich.console import Console
    from rich.logging import RichHandler
    from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, SpinnerColumn
    from rich.table import Table  # Import Table for displaying counts

    rich_available = True
except ImportError:
    rich_available = False


    # Define dummy classes/functions if rich is not available
    class Console:
        def print(self, *args, **kwargs): print(*args)

        def input(self, *args, **kwargs): return input(*args)


    class Progress:
        def __init__(self, *args, **kwargs): pass

        def __enter__(self): return self

        def __exit__(self, exc_type, exc_val, exc_tb): pass

        def add_task(self, *args, **kwargs): return 0

        def update(self, *args, **kwargs): pass

        def stop(self): pass


    class RichHandler(logging.StreamHandler):
        pass  # Basic fallback


    class Table:
        def __init__(self, *args, **kwargs): self._rows = []; self._title = kwargs.get('title', '')

        def add_column(self, *args, **kwargs): pass

        def add_row(self, *args, **kwargs): self._rows.append(args)

        def __str__(self):  # Basic string representation for fallback
            header = f"--- {self._title} ---"
            lines = [header] + [" | ".join(map(str, row)) for row in self._rows]
            return "\n".join(lines)


    # Columns are not strictly needed for fallback
    class BarColumn:
        pass


    class TextColumn:
        pass


    class TimeElapsedColumn:
        pass


    class SpinnerColumn:
        pass

# Configure Logging (using RichHandler if available)
log_handlers: List[logging.Handler]
if rich_available:
    log_handlers = [RichHandler(rich_tracebacks=True, markup=True, console=Console())]  # Enable markup for styling
else:
    log_handlers = [logging.StreamHandler(sys.stdout)]  # Basic fallback

logging.basicConfig(
    level=logging.INFO,  # Default level for CLI output
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s" if not rich_available else "%(message)s",
    datefmt="[%X]",
    handlers=log_handlers
)
cli_logger = logging.getLogger("dynamodb_cli")
console = Console()  # For rich printing (will be dummy if rich not installed)

# --- Relative Imports ---
try:
    # Assuming PROJECT_ROOT is in config
    from src.lib.config import load_config, ScraperConfig, PROJECT_ROOT
    # Import serializer
    from src.lib.dynamodb_base_manager import DynamoDbBaseManager, json_decimal_serializer
    # Import specific managers if needed for type hinting or specific methods outside base
    from src.lib.pacer_manager import PacerManager  # Original Pacer table
    from src.lib.pacer_dockets_manager import PacerDocketsManager  # For PacerDockets table
    from src.lib.fb_archive_manager import FBAdArchiveManager
    # from .docker_dynamodb_manager import DockerDynamoDBManager
except ImportError:
    cli_logger.error("Failed relative imports. Ensure script is run correctly relative to module structure.",
                     exc_info=True)
    # Attempt absolute imports as fallback (adjust paths if necessary)
    try:
        # Adjust these based on your actual structure if running directly
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root_guess = os.path.dirname(os.path.dirname(script_dir))  # Go up two levels from src/scripts
        sys.path.insert(0, project_root_guess)
        from src.lib.config import load_config, ScraperConfig, PROJECT_ROOT
        from src.lib.dynamodb_base_manager import DynamoDbBaseManager, json_decimal_serializer
        from src.lib.pacer_manager import PacerManager
        from src.lib.pacer_dockets_manager import PacerDocketsManager  # For PacerDockets table
        from src.lib.fb_archive_manager import FBAdArchiveManager
        # from docker_dynamodb_manager import DockerDynamoDBManager
    except ImportError:
        cli_logger.critical("Could not resolve imports. Exiting.")
        sys.exit(1)



def get_local_table_counts(config: Dict[str, Any], display_name: str, manager_class: type[DynamoDbBaseManager]) -> Dict[str, Any]:
    """Helper function to get item count for a single local table."""
    try:
        manager = manager_class(config=config, use_local=True)
        count = manager.count_table_items()
        return {"table": display_name, "local": count if count != -1 else "[red]Error[/red]"}
    except Exception as e:
        cli_logger.error(f"Error counting items in local table '{display_name}': {e}", exc_info=True)
        return {"table": display_name, "local": "[red]Error[/red]"}


def display_counts(counts: List[Dict[str, Any]]) -> None:
    """Display the item counts for all tables in a formatted table.
    
    Args:
        counts: List of dictionaries containing table names and their item counts
    """
    if not counts:
        console.print("[yellow]No table counts to display.[/yellow]")
        return
        
    # Create a table with columns
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Table", style="cyan", no_wrap=True)
    table.add_column("Item Count", justify="right")
    
    # Add rows for each table
    for count_info in counts:
        table_name = count_info["table"]
        count = count_info["local"]
        
        # Format the count with thousands separators if it's a number
        if isinstance(count, int):
            count_str = f"{count:,}"
        else:
            count_str = str(count)
            
        table.add_row(table_name, count_str)
    
    # Print the table
    console.print("\n[bold]Current Item Counts:[/bold]")
    console.print(table)

def get_config(end_date: Optional[str] = None) -> ScraperConfig:
    if not end_date:
        end_date = datetime.now().strftime("%m/%d/%Y")
        cli_logger.debug(f"End date default: {end_date}")
    try:
        config_model = load_config(end_date=end_date, return_as_dict=False)
        if not isinstance(config_model, ScraperConfig):
            raise TypeError(f"load_config did not return a ScraperConfig object. Type: {type(config_model)}")

        cli_logger.info(f"Config loaded for date: {config_model.date} (Effective End Date: {end_date})")
        if not config_model.credentials or not config_model.credentials.aws_access_key or not config_model.credentials.aws_secret_key:
            raise ValueError("AWS credentials not found in config.")
        if not config_model.region_name:
            config_model.region_name = 'us-west-2'
            cli_logger.warning("AWS region defaulted to us-west-2.")
        return config_model
    except FileNotFoundError:
        cli_logger.error("[bold red]Config file (.env) not found.[/bold red]")
        sys.exit(1)
    except ValueError as ve:
        cli_logger.error(f"[bold red]{ve}[/bold red]")
        sys.exit(1)
    except TypeError as te:
        cli_logger.error(f"[bold red]Config loading type error: {te}[/bold red]", exc_info=True)
        sys.exit(1)
    except Exception as e:
        cli_logger.error(f"[bold red]Error loading config: {e}[/bold red]", exc_info=True)
        sys.exit(1)

class DynamoDBIncrementalSync:
    """
    Manages synchronization between AWS and local DynamoDB tables.
    Handles Full, Incremental, and Missing sync modes.
    """

    def __init__(self, config: Dict[str, Any], table_name: str, ManagerClass: type[DynamoDbBaseManager]):
        self.config = config
        self.requested_table_name = table_name
        self.ManagerClass = ManagerClass
        self.logger = logging.getLogger(f"DynamoDBSync[{table_name}]")
        self.aws_manager: Optional[DynamoDbBaseManager] = None
        self.local_manager: Optional[DynamoDbBaseManager] = None

        self._initialize_local_manager()
        self.logger.debug("AWS Manager initialization deferred.")

        self.timestamp_field, self.timestamp_index = self._get_sync_fields(ManagerClass, self.requested_table_name)
        self.max_workers = min(os.cpu_count() * 4, 32)
        self.aws_read_batch_size = 100
        self.local_write_batch_size = 25
        self.key_names: List[str] = []

    def _initialize_local_manager(self):
        """Initializes the Local manager."""
        self.logger.debug(
            f"Initializing Local manager for {self.ManagerClass.__name__} targeting table derived from manager (should be '{self.requested_table_name}').")
        try:
            # The ManagerClass __init__ should use its default table name which *must* match self.requested_table_name
            # This is critical. If PacerDocketsManager's default table isn't "PacerDockets", this is problematic.
            # The ManagerClass is selected based on the requested_table_name.
            self.local_manager = self.ManagerClass(config=self.config, use_local=True)

            # Verify the initialized manager is for the correct table
            if self.local_manager.table_name != self.requested_table_name:
                self.logger.error(
                    f"CRITICAL MISMATCH: Local {self.ManagerClass.__name__} initialized for table '{self.local_manager.table_name}', "
                    f"but sync was requested for '{self.requested_table_name}'. This indicates a configuration error in the CLI or ManagerClass defaults."
                )
                # This is a fatal error for the current table's sync logic.
                raise ValueError(
                    f"Local manager table name mismatch: expected {self.requested_table_name}, got {self.local_manager.table_name}")

            if hasattr(self.local_manager, 'logger'):
                self.local_manager.logger.info(
                    f"Local manager initialized, targeting table '{self.local_manager.table_name}'.")
            else:
                self.logger.info(
                    f"Local manager initialized, targeting table '{self.local_manager.table_name}'.")
        except Exception as e:
            self.logger.critical(f"Failed to initialize Local manager for {self.requested_table_name}: {e}",
                                 exc_info=True)
            raise

    def _ensure_aws_manager(self):
        """Initializes the AWS manager if it hasn't been already."""
        if self.aws_manager is None:
            self.logger.debug(
                f"Initializing AWS manager for {self.ManagerClass.__name__} targeting table derived from manager (should be '{self.requested_table_name}').")
            try:
                # Similarly, the ManagerClass should correctly set its table_name to self.requested_table_name
                self.aws_manager = self.ManagerClass(config=self.config, use_local=False)

                # Verify the initialized manager is for the correct table
                if self.aws_manager.table_name != self.requested_table_name:
                    self.logger.error(
                        f"CRITICAL MISMATCH: AWS {self.ManagerClass.__name__} initialized for table '{self.aws_manager.table_name}', "
                        f"but sync was requested for '{self.requested_table_name}'. This indicates a configuration error."
                    )
                    raise ValueError(
                        f"AWS manager table name mismatch: expected {self.requested_table_name}, got {self.aws_manager.table_name}")

                if hasattr(self.aws_manager, 'logger'):
                    self.aws_manager.logger.info(
                        f"AWS manager initialized, targeting table '{self.aws_manager.table_name}'.")
                else:
                    self.logger.info(
                        f"AWS manager initialized, targeting table '{self.aws_manager.table_name}'.")
            except Exception as e:
                self.logger.critical(f"Failed to initialize AWS manager for {self.requested_table_name}: {e}",
                                     exc_info=True)
                raise
        else:
            self.logger.debug(
                f"AWS manager for table '{getattr(self.aws_manager, 'table_name', 'UNKNOWN')}' already initialized.")

    def _get_sync_fields(self, ManagerClass: type, table_name: str) -> Tuple[str, Optional[str]]:
        # Decide based on the actual manager class or table name convention
        # This assumes the table_name passed corresponds to the manager logic
        # Make comparison safer in case ManagerClass is None temporarily
        manager_class_name = ManagerClass.__name__ if ManagerClass else "None"

        if manager_class_name == "PacerManager":  # For original Pacer table
            return "AddedOn", "AddedOn-index"
        elif manager_class_name == "PacerDocketsManager":  # For PacerDockets table
            return "AddedOn", "AddedOn-index"  # Assuming PacerDockets also uses AddedOn for incremental
        elif manager_class_name == "FBAdArchiveManager":
            # Assuming LastUpdated is the GSI hash key
            return "LastUpdated", "LastUpdated-index"
        # Add other manager/table mappings here
        # Fallback based on name if ManagerClass isn't specific enough
        elif table_name == "DocketActivity":  # Example
            return "FilingDate", None  # Assumes FilingDate is part of primary key
        else:
            # Best guess if not explicitly mapped - check manager attributes if available
            if hasattr(ManagerClass, 'DEFAULT_TIMESTAMP_FIELD') and hasattr(ManagerClass, 'DEFAULT_TIMESTAMP_INDEX'):
                self.logger.warning(f"Using default sync fields from {ManagerClass.__name__} for table '{table_name}'")
                # Type ignore might be needed if these aren't formally defined in a base type
                return ManagerClass.DEFAULT_TIMESTAMP_FIELD, ManagerClass.DEFAULT_TIMESTAMP_INDEX  # type: ignore
            # --- MORE ROBUST FALLBACK ---
            # Try inferring from common patterns if no explicit config found
            # Example: Check if 'LastUpdated' or 'AddedOn' exist as attributes in the base manager's known attribute config
            base_attr_config = getattr(DynamoDbBaseManager, 'attribute_config', {})
            table_attrs = base_attr_config.get(table_name, {}).get('include', [])
            if 'LastUpdated' in table_attrs:
                self.logger.warning(f"Guessed 'LastUpdated' as sync field for table '{table_name}'. GSI name unknown.")
                # Attempt to find a matching GSI definition if available
                key_conf = getattr(DynamoDbBaseManager, 'key_config', {}).get(table_name, {})
                gsi_index_name = None
                for gsi in key_conf.get('gsis', []):
                    if gsi.get('hash_key') == 'LastUpdated':
                        gsi_index_name = gsi.get('name')
                        break
                if gsi_index_name:
                    self.logger.info(f"Found matching GSI '{gsi_index_name}' for guessed field 'LastUpdated'.")
                    return "LastUpdated", gsi_index_name
                else:
                    self.logger.warning(f"No GSI found with 'LastUpdated' as hash key for table '{table_name}'.")
                    return "LastUpdated", None  # Assume no index or use primary key? Requires table knowledge.
            elif 'AddedOn' in table_attrs:
                # Similar logic for AddedOn
                self.logger.warning(f"Guessed 'AddedOn' as sync field for table '{table_name}'. GSI name unknown.")
                key_conf = getattr(DynamoDbBaseManager, 'key_config', {}).get(table_name, {})
                gsi_index_name = None
                for gsi in key_conf.get('gsis', []):
                    if gsi.get('hash_key') == 'AddedOn':
                        gsi_index_name = gsi.get('name');
                        break
                if gsi_index_name:
                    self.logger.info(f"Found matching GSI '{gsi_index_name}' for guessed field 'AddedOn'.")
                    return "AddedOn", gsi_index_name
                else:
                    self.logger.warning(f"No GSI found with 'AddedOn' as hash key for table '{table_name}'.")
                    return "AddedOn", None

            # --- FINAL ERROR ---
            raise ValueError(
                f"Incremental sync configuration missing or ambiguous for table '{table_name}' / Manager '{manager_class_name}'. Define fields or map in _get_sync_fields.")

    def _perform_full_sync(self) -> None:
        """Performs a full sync by copying all items from AWS to local."""
        self._ensure_aws_manager()
        if not self.aws_manager or not self.local_manager:
            raise RuntimeError("Managers not initialized")
            
        self.logger.info("Starting FULL sync (all items from AWS to local)")
        
        # Get all items from AWS using scan_table()
        aws_items = []
        try:
            # Use the scan_table method which handles pagination
            scan_response = self.aws_manager.scan_table()
            aws_items = list(scan_response) if scan_response else []
            self.logger.info(f"Retrieved {len(aws_items)} items from AWS")
            
            if not aws_items:
                self.logger.warning("No items found in AWS table")
                return
                
            # Write all items to local using batch_insert_items
            total_items = len(aws_items)
            self.logger.info(f"Starting to write {total_items} items to local table")
            
            # Use batch_insert_items which handles batching and retries
            success_count, failed_count = self.local_manager.batch_insert_items(
                records=aws_items,
                batch_size=self.local_write_batch_size,
                disable_progress=False
            )
            
            self.logger.info(f"Full sync completed. Success: {success_count}, Failed: {failed_count}")
            
        except Exception as e:
            self.logger.error(f"Error during full sync: {str(e)}", exc_info=True)
            raise

    def _perform_incremental_sync(self) -> None:
        """Performs an incremental sync by copying only new/updated items."""
        self._ensure_aws_manager()
        if not self.aws_manager or not self.local_manager:
            raise RuntimeError("Managers not initialized")

        self.logger.info(
            f"Starting INCREMENTAL sync using field: '{self.timestamp_field}' on index '{self.timestamp_index}'")

        try:
            # Determine the latest local timestamp by iterating backwards
            latest_local_timestamp_value = None
            self.logger.info(
                f"Attempting to find latest local timestamp for '{self.timestamp_field}' by iterating backwards from today.")

            # Iterate backwards from today for up to 366 days (today + 365 prior days)
            for i in range(366):
                date_to_check = datetime.now() - timedelta(days=i)
                date_to_check_str = date_to_check.strftime('%Y%m%d')

                # Query local manager for items on this specific date_to_check_str
                # using the timestamp_field and its GSI (timestamp_index).
                # We only need to know if *any* item exists for this date.
                # query_by_date is suitable here if it correctly uses the index for a single day.
                items_on_this_day = list(self.local_manager.query_by_date(
                    start_date_str=date_to_check_str,
                    end_date_str=date_to_check_str,  # Query for a single day
                    date_field=self.timestamp_field,
                    index_name=self.timestamp_index,  # Pass the GSI name
                    limit=1  # We only need one item to confirm data exists for this day
                ))

                if items_on_this_day:
                    # Ensure the item's timestamp field matches the day we queried for
                    # This is a sanity check; it should match if query_by_date works correctly.
                    item_timestamp = items_on_this_day[0].get(self.timestamp_field)
                    if item_timestamp == date_to_check_str:
                        latest_local_timestamp_value = date_to_check_str
                        self.logger.info(
                            f"Found latest local timestamp by iterating backwards: {latest_local_timestamp_value}")
                        break  # Exit loop once the most recent day with data is found
                    else:
                        # This indicates a potential mismatch or issue in data/query logic
                        self.logger.warning(
                            f"Item found on {date_to_check_str} but its '{self.timestamp_field}' field is '{item_timestamp}'. "
                            f"This might indicate an issue. Continuing search for a direct match.")

            if not latest_local_timestamp_value:
                self.logger.info(
                    f"No local items with timestamp field '{self.timestamp_field}' found after checking 366 days.")

            self.logger.info(f"Latest local {self.timestamp_field} determined as: {latest_local_timestamp_value}")

            if latest_local_timestamp_value is None:
                self.logger.warning(
                    f"No local data with a valid timestamp for '{self.timestamp_field}' found, performing full sync instead.")
                return self._perform_full_sync()

            # Query AWS for items from latest_local_timestamp_value up to now.
            # Start querying AWS from the day of the latest_local_timestamp_value to catch any updates on that day.
            aws_items_to_sync = []
            current_aws_sync_date = datetime.strptime(latest_local_timestamp_value, '%Y%m%d')
            end_aws_sync_date = datetime.now()

            self.logger.info(
                f"Querying AWS for items from {current_aws_sync_date.strftime('%Y%m%d')} to {end_aws_sync_date.strftime('%Y%m%d')}.")

            date_iterator = current_aws_sync_date
            while date_iterator.date() <= end_aws_sync_date.date():
                date_str_for_aws_query = date_iterator.strftime('%Y%m%d')
                self.logger.debug(
                    f"Querying AWS for {self.timestamp_field} = {date_str_for_aws_query} using index '{self.timestamp_index}'")

                daily_aws_items = []
                if self.timestamp_index:  # GSI exists for the timestamp field
                    aws_query_key_conditions = {self.timestamp_field: date_str_for_aws_query}
                    daily_aws_items = list(self.aws_manager.query_items(
                        key_conditions=aws_query_key_conditions,
                        index_name=self.timestamp_index
                    ))
                else:  # Fallback to scan with filter if no GSI (less efficient)
                    # This branch is less likely if timestamp_field is designed for incremental syncs.
                    self.logger.warning(
                        f"No GSI specified by 'timestamp_index' for '{self.timestamp_field}'. Falling back to scan for AWS data on {date_str_for_aws_query}. This can be slow.")
                    scan_filter_aws = {
                        self.timestamp_field: {'AttributeValueList': [date_str_for_aws_query],
                                               'ComparisonOperator': 'EQ'}
                    }
                    daily_aws_items = list(self.aws_manager.scan_table(filter_expression=scan_filter_aws))

                if daily_aws_items:
                    self.logger.debug(
                        f"Found {len(daily_aws_items)} items in AWS for {self.timestamp_field}={date_str_for_aws_query}.")
                    aws_items_to_sync.extend(daily_aws_items)

                date_iterator += timedelta(days=1)

            self.logger.info(f"Found {len(aws_items_to_sync)} new/updated items in AWS to sync.")

            if not aws_items_to_sync:
                self.logger.info("No new or updated items found in AWS based on the determined local timestamp.")
                return

            # Write new/updated items to local using batch_insert_items
            success_count, failed_count = self.local_manager.batch_insert_items(
                records=aws_items_to_sync,  # Use the correctly fetched items
                batch_size=self.local_write_batch_size,
                disable_progress=False
            )

            self.logger.info(f"Incremental sync completed. Success: {success_count}, Failed: {failed_count}")

        except Exception as e:
            self.logger.error(f"Error during incremental sync: {str(e)}", exc_info=True)
            raise

    def _perform_missing_sync(self) -> None:
        """Performs a sync of only items that exist in AWS but not in local."""
        self._ensure_aws_manager()
        if not self.aws_manager or not self.local_manager:
            raise RuntimeError("Managers not initialized")
            
        self.logger.info("Starting MISSING ITEMS sync")
        
        try:
            # Get AWS item keys using scan_table with projection of key fields
            aws_keys = set()
            key_attrs = self.aws_manager.get_table_key_info()
            
            # Get the key attribute names for the table
            key_attr_names = [attr['AttributeName'] for attr in key_attrs]
            
            # Scan AWS table with projection of just the key fields
            aws_items = self.aws_manager.scan_table(
                projection_expression=", ".join(key_attr_names)
            )
            
            # Create a set of key tuples for AWS items
            aws_keys = set(
                tuple(item[attr] for attr in key_attr_names)
                for item in aws_items
            )
            
            self.logger.info(f"Found {len(aws_keys)} items in AWS")
            
            # Get local item keys in the same way
            local_items = self.local_manager.scan_table(
                projection_expression=", ".join(key_attr_names)
            )
            
            # Create a set of key tuples for local items
            local_keys = set(
                tuple(item[attr] for attr in key_attr_names)
                for item in local_items
            )
            
            self.logger.info(f"Found {len(local_keys)} items in local")
            
            # Find keys in AWS but not in local
            missing_key_tuples = aws_keys - local_keys
            
            if not missing_key_tuples:
                self.logger.info("No missing items found in local")
                return
                
            self.logger.info(f"Found {len(missing_key_tuples)} missing items in local")
            
            # Convert key tuples back to dictionaries for batch_get
            missing_keys = [
                {attr: key_tuple[i] for i, attr in enumerate(key_attr_names)}
                for key_tuple in missing_key_tuples
            ]
            
            # Batch get the missing items from AWS
            missing_items = []
            for i in range(0, len(missing_keys), self.aws_read_batch_size):
                batch_keys = missing_keys[i:i + self.aws_read_batch_size]
                batch_items = self.aws_manager.batch_get_items(batch_keys)
                if batch_items:
                    missing_items.extend(batch_items)
            
            if not missing_items:
                self.logger.warning("No items found for the missing keys")
                return
                
            # Write missing items to local using batch_insert_items
            success_count, failed_count = self.local_manager.batch_insert_items(
                records=missing_items,
                batch_size=self.local_write_batch_size,
                disable_progress=False
            )
                
            self.logger.info(f"Missing items sync completed. Success: {success_count}, Failed: {failed_count}")
            
        except Exception as e:
            self.logger.error(f"Error during missing items sync: {str(e)}", exc_info=True)
            raise


def main():
    console.print("\n[bold blue]DynamoDB AWS <> Local Synchronization Tool[/bold blue]")
    console.print("========================================")

    try:
        # --- Configuration ---
        end_date_override = sys.argv[1] if len(sys.argv) > 1 else None
        if end_date_override:
            try:
                datetime.strptime(end_date_override, "%m/%d/%Y")
                console.print(f"Using command-line end date: {end_date_override}")
            except ValueError:
                console.print(f"[yellow]Invalid date format '{end_date_override}'. Using default (today).[/yellow]")
                end_date_override = None
        config_model = get_config(end_date_override)
        manager_config = {
            'aws_access_key': config_model.credentials.aws_access_key if config_model.credentials else None,
            'aws_secret_key': config_model.credentials.aws_secret_key if config_model.credentials else None,
            'iso_date': config_model.date,
            'region_name': config_model.region_name,
            'date': config_model.date,
        }
        manager_config = {k: v for k, v in manager_config.items() if v is not None}

        # --- Define Tables and Managers ---
        table_map = {
            "1": ("Pacer", PacerManager),
            "2": ("PacerDockets", PacerDocketsManager),
            "3": ("Pacer + PacerDockets", None),  # Special combined option for syncing both local tables
            "4": ("FBAdArchive", FBAdArchiveManager)
        }

        # For counting, we need individual managers
        tables_for_count_display = [
            ("Pacer", PacerManager),
            ("PacerDockets", PacerDocketsManager),
            ("FBAdArchive", FBAdArchiveManager)
        ]

        # --- Fetch Initial LOCAL Counts Concurrently ---
        console.print("\n[bold]Fetching initial local table counts...[/bold]")
        all_counts = []
        count_workers = min(len(tables_for_count_display), 4)
        with ThreadPoolExecutor(max_workers=count_workers, thread_name_prefix="LocalTableCount") as executor:
            futures = {
                executor.submit(get_local_table_counts, manager_config, display_name, m_class): display_name
                for display_name, m_class in tables_for_count_display
            }
            progress_context = contextlib.nullcontext()
            count_progress = None
            count_task = None
            if rich_available:
                progress_context = Progress(TextColumn("{task.description}"), BarColumn(), console=console,
                                            transient=True)
            with progress_context as progress_maybe:
                if progress_maybe:
                    count_progress = progress_maybe
                    count_task = count_progress.add_task("Counting local tables...", total=len(futures))
                for future in as_completed(futures):
                    table_name_done = futures[future]
                    try:
                        result = future.result()
                        all_counts.append(result)
                        if count_progress and count_task is not None:
                            count_progress.update(count_task, advance=1,
                                                  description=f"Checked local '{table_name_done}'...")
                    except Exception as e:
                        cli_logger.error(f"Error getting future result for '{table_name_done}' local counts: {e}",
                                         exc_info=True)
                        all_counts.append({"table": table_name_done, "local": "[red]Future Error[/red]"})
                        if count_progress and count_task is not None:
                            count_progress.update(count_task, advance=1,
                                                  description=f"[red]Failed local '{table_name_done}'...[/red]")
        all_counts.sort(key=lambda x: x["table"])
        display_counts(all_counts)

        # --- Table Selection ---
        console.print("\n[bold]Available Tables for Sync:[/bold]")
        for key, (name, _) in table_map.items():
            console.print(f"  {key}. {name}")

        selected_table_configs: List[Tuple[str, type[DynamoDbBaseManager]]] = []

        while True:
            table_choice_input = console.input("\nSelect table number to sync: ").strip()
            if table_choice_input == "3":  # Special case for "Pacer + PacerDockets"
                selected_table_configs.append(("Pacer", PacerManager))
                selected_table_configs.append(("PacerDockets", PacerDocketsManager))
                console.print(f"Selected tables: [cyan]Pacer[/cyan] and [cyan]PacerDockets[/cyan] for combined sync.")
                break
            elif table_choice_input in table_map:
                name_selected, MgrClass_selected = table_map[table_choice_input]
                if MgrClass_selected is None:  # Should not happen if not combined
                    console.print(
                        "[red]Invalid selection (ManagerClass is None for non-combined). Please choose again.[/red]")
                    continue
                selected_table_configs.append((name_selected, MgrClass_selected))
                console.print(f"Selected table: [cyan]{name_selected}[/cyan]")
                break
            console.print("[red]Invalid selection. Please enter the number.[/red]")

        # --- Sync Type Selection ---
        console.print("\n[bold]Sync Options:[/bold]")
        sync_map = {
            "1": ("full", "Full Download (Recreates Local Table)"),
            "2": ("incremental", "Incremental Update (Fetches New/Updated)"),
            "3": ("missing", "Missing Sync (Compares Keys, Fetches Missing)")
        }
        for key, (_, desc) in sync_map.items():
            console.print(f"  {key}. {desc}")
        while True:
            sync_choice_input = console.input("\nSelect sync type number: ").strip()
            if sync_choice_input in sync_map:
                sync_type_selected, sync_desc_selected = sync_map[sync_choice_input]
                break
            console.print("[red]Invalid selection. Please enter the number.[/red]")

        # --- Loop through selected tables for sync ---
        overall_sync_start_time = time.time()
        any_sync_failed = False

        for table_to_sync_name, ManagerClass_for_sync in selected_table_configs:
            console.rule(f"[bold white]Processing table: {table_to_sync_name}[/bold white]", style="blue")
            console.print(
                f"\nInitializing sync for table [cyan]'{table_to_sync_name}'[/cyan] using [magenta]{sync_desc_selected}[/magenta]...")

            updater_instance: Optional[DynamoDBIncrementalSync] = None
            try:
                updater_instance = DynamoDBIncrementalSync(
                    config=manager_config,
                    table_name=table_to_sync_name,  # Pass the specific table name
                    ManagerClass=ManagerClass_for_sync  # Pass the specific manager
                )
                console.print("Sync manager initialized.")
            except Exception as init_e_loop:
                console.print(
                    f"[bold red]Initialization failed for table '{table_to_sync_name}': {init_e_loop}[/bold red]",
                    exc_info=True)
                any_sync_failed = True
                continue  # Skip to next table if this one fails to init

            aws_display_name_loop = getattr(getattr(updater_instance, 'aws_manager', None), 'table_name',
                                            table_to_sync_name)
            local_display_name_loop = getattr(getattr(updater_instance, 'local_manager', None), 'table_name',
                                              'UNKNOWN_LOCAL')

            console.print(
                f"\n[bold yellow]Action:[/bold yellow] Perform [magenta]{sync_type_selected}[/magenta] sync for table [cyan]'{aws_display_name_loop}'[/cyan] (AWS) to [cyan]'{local_display_name_loop}'[/cyan] (Local).")
            if sync_type_selected == 'full':
                console.print(
                    f"[bold orange1]WARNING:[/bold orange1] Full sync will [red]DELETE[/red] the existing local table "
                    f"'{local_display_name_loop}' and recreate it.")

            # For combined, only ask for confirmation once before the loop starts.
            # If not combined, or if it's the first table in a combined run, ask.
            proceed_sync = 'y'  # Default to yes for subsequent tables in combined mode
            if len(selected_table_configs) == 1 or (
                    len(selected_table_configs) > 1 and table_to_sync_name == selected_table_configs[0][0]):
                proceed_sync = console.input(
                    f"Proceed with [magenta]{sync_type_selected}[/magenta] sync for selected tables? (y/N): ").strip().lower()
                if proceed_sync != 'y':
                    console.print("Operation cancelled by user.")
                    any_sync_failed = True  # Mark as failed to prevent further processing
                    break  # Exit the loop over tables
            elif len(selected_table_configs) > 1:  # For subsequent tables in combined mode
                console.print(f"Proceeding with sync for [cyan]{table_to_sync_name}[/cyan]...")

            if proceed_sync == 'y':
                console.print(
                    f"\nStarting [magenta]{sync_type_selected}[/magenta] synchronization for '{table_to_sync_name}'...")
                single_table_start_time = time.time()
                sync_success_current_table = False
                try:
                    if updater_instance:
                        # Special handling for PacerDockets - sync from AWS Pacer to both local tables
                        if table_to_sync_name == "PacerDockets" or (table_to_sync_name == "Pacer + PacerDockets" and ManagerClass_for_sync is None):
                            # Sync from AWS Pacer to local Pacer
                            pacer_updater = DynamoDBIncrementalSync(
                                config=manager_config,
                                table_name="Pacer",
                                ManagerClass=PacerManager
                            )
                            
                            # Sync from AWS Pacer to local PacerDockets
                            dockets_updater = DynamoDBIncrementalSync(
                                config=manager_config,
                                table_name="PacerDockets",
                                ManagerClass=PacerDocketsManager
                            )
                            
                            # Perform sync on both tables
                            if sync_type_selected == 'full':
                                pacer_updater._perform_full_sync()
                                dockets_updater._perform_full_sync()
                            elif sync_type_selected == 'incremental':
                                pacer_updater._perform_incremental_sync()
                                dockets_updater._perform_incremental_sync()
                            elif sync_type_selected == 'missing':
                                pacer_updater._perform_missing_sync()
                                dockets_updater._perform_missing_sync()
                        else:
                            # Normal sync for other tables
                            if sync_type_selected == 'full':
                                updater_instance._perform_full_sync()
                            elif sync_type_selected == 'incremental':
                                updater_instance._perform_incremental_sync()
                            elif sync_type_selected == 'missing':
                                updater_instance._perform_missing_sync()
                        sync_success_current_table = True
                    else:  # Should not happen if init passed
                        cli_logger.error(f"Updater object was None for table '{table_to_sync_name}'.")
                        sync_success_current_table = False
                except Exception as sync_err_loop:
                    cli_logger.critical(f"Sync operation for '{table_to_sync_name}' failed: {sync_err_loop}",
                                        exc_info=True)
                    console.print(f"[bold red]Sync Error for '{table_to_sync_name}':[/bold red] Check logs.")
                    sync_success_current_table = False

                if not sync_success_current_table:
                    any_sync_failed = True

                duration_current_table = time.time() - single_table_start_time
                result_color_current = "green" if sync_success_current_table else "red"
                console.print(
                    f"\n[{result_color_current}]{table_to_sync_name} synchronization finished in {duration_current_table:.2f} seconds.[/{result_color_current}]")
            else:  # User cancelled for this specific table (should only happen if asked individually)
                console.print(f"Sync for '{table_to_sync_name}' cancelled by user.")
                any_sync_failed = True  # If one part of combined is cancelled, treat overall as not fully complete

        overall_duration = time.time() - overall_sync_start_time
        final_result_color = "green" if not any_sync_failed else "red"
        console.print(
            f"\n[{final_result_color}]All selected sync operations finished in {overall_duration:.2f} seconds.[/{final_result_color}]")


    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user (KeyboardInterrupt).[/yellow]")
    except Exception as e:
        cli_logger.critical(
            f"\n[bold red]CLI Error: An unexpected error occurred outside the sync process: {e}[/bold red]",
            exc_info=True)
        sys.exit(1)
    finally:
        console.print("\nDynamoDB sync tool finished.")


if __name__ == "__main__":
    # Configure logging levels for dependencies
    logging.getLogger("botocore").setLevel(logging.WARNING)
    logging.getLogger("boto3").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)

    # Set level for our own library modules (adjust as needed for debugging)
    # Set base manager to DEBUG to see detailed batch operations or sanitization
    # logging.getLogger("src.lib.dynamodb_base_manager").setLevel(logging.DEBUG)
    logging.getLogger("src.lib.dynamodb_base_manager").setLevel(logging.INFO)
    logging.getLogger("src.lib.docker_dynamodb_manager").setLevel(logging.INFO)  # Docker startup logs
    logging.getLogger("src.lib.pacer_manager").setLevel(logging.INFO)
    logging.getLogger("src.lib.fb_archive_manager").setLevel(logging.INFO)

    # Allow setting CLI log level via environment variable, defaulting to INFO
    cli_log_level = os.environ.get("CLI_LOG_LEVEL", "INFO").upper()
    logging.getLogger("dynamodb_cli").setLevel(cli_log_level)
    # Also set the sync class logger level
    logging.getLogger("DynamoDBSync").setLevel(cli_log_level)

    main()
