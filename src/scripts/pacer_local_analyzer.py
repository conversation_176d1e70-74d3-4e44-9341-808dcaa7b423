#!/usr/bin/env python3
import sys
import os

print("--- sys.path at script start ---")
for p in sys.path:
    print(p)
print("-------------------------------")

print(f"--- Current Working Directory: {os.getcwd()} ---") # ADD THIS LINE


import csv
import ast
import concurrent.futures
import json
import os
import sys
import time
import re  # Import the re module for regex
from collections import defaultdict, Counter
from pathlib import Path
from typing import List, Dict, Any, Set


def analyze_mdl_details(self) -> None:
    """Analyzes loaded Pacer data for unique MDLs, Flags, and Defendant and prints as JSON."""
    if not self.pacer_items:
        print(json.dumps({"type": "warning", "source": "analyze_mdl_details",
                          "message": "No Pacer data loaded. Please load data first."}))
        return

    print(json.dumps({"type": "info", "source": "analyze_mdl_details", "message": "Analyzing MDL details..."}))
    raw_mdl_data: Dict[str, Dict[str, Set[str]]] = defaultdict(lambda: {"Flags": set(), "Defendant": set()})
    items_processed_for_mdl = 0

    for idx, item in enumerate(self.pacer_items):
        mdl_num_raw = item.get('MdlNum')

        mdl_num = str(mdl_num_raw).strip() if mdl_num_raw is not None else ""

        if not mdl_num or mdl_num.upper() == "NA":
            continue

        items_processed_for_mdl += 1
        item_context = f"item (PK: {item.get('FilingDate', 'N/A')}/{item.get('DocketNum', 'N/A')})"

        flags_val = item.get('Flags')
        defendants_val = item.get('Defendant')

        parsed_flags = self._parse_attribute_list(flags_val, f"Flags for {mdl_num} from {item_context}")
        parsed_defendants = self._parse_attribute_list(defendants_val,
                                                       f"Defendant for {mdl_num} from {item_context}")

        raw_mdl_data[mdl_num]["Flags"].update(parsed_flags)
        raw_mdl_data[mdl_num]["Defendant"].update(parsed_defendants)

    if not raw_mdl_data:
        print(json.dumps({"type": "warning", "source": "analyze_mdl_details",
                          "message": "No valid MDL data found after processing items."}))
        self.analyzed_mdl_data = {}
        return

    self.analyzed_mdl_data = {
        mdl: {
            "Flags": sorted(list(details["Flags"])),
            "Defendant": sorted(list(details["Defendant"]))
        }
        for mdl, details in raw_mdl_data.items()
    }

    output_data = {
        "type": "success",
        "source": "analyze_mdl_details",
        "message": f"Analysis complete. Found details for {len(self.analyzed_mdl_data)} unique MDL numbers from {items_processed_for_mdl} relevant items.",
        "unique_mdl_count": len(self.analyzed_mdl_data),
        "relevant_items_processed": items_processed_for_mdl,
        "analysis_results": self.analyzed_mdl_data
    }
    print(json.dumps(output_data, indent=2))

from rich.table import Table
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.prompt import Prompt

# Add project root to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

try:
    from src.lib.pacer_dockets_manager import PacerDocketsManager
    from src.lib.config import load_config
except ImportError as e_imp:
    print(f"Initial import failed: {e_imp}. Attempting relative import for dev environment...")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    if os.path.basename(parent_dir) != "src":
        sys.path.insert(0, os.path.abspath(os.path.join(current_dir, "..")))
    else:
        sys.path.insert(0, os.path.abspath(os.path.join(parent_dir, "..")))

    from src.lib.pacer_dockets_manager import PacerDocketsManager
    from src.lib.config import load_config

console = Console()


class PacerLocalAnalyzer:
    def __init__(self, max_workers: int = 10):
        self.console = console
        self.pacer_manager: PacerDocketsManager | None = None
        self.logger = None
        try:
            config = load_config('01/01/70')
            self.pacer_manager = PacerDocketsManager(config, use_local=True)
            self.logger = self.pacer_manager.logger
            self.logger.info("PacerLocalAnalyzer initialized with PacerDocketsManager for local DB.")
        except Exception as e:
            self.console.print(f"[bold red]Error initializing PacerManager: {e}[/bold red]")
            import logging
            self.logger = logging.getLogger("PacerLocalAnalyzer_fallback")
            if not self.logger.hasHandlers():
                handler = logging.StreamHandler(sys.stdout)
                formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                handler.setFormatter(formatter)
                self.logger.addHandler(handler)
                self.logger.setLevel(logging.INFO)
            self.logger.error(f"PacerManager initialization failed: {e}", exc_info=True)

        self.pacer_items: List[Dict[str, Any]] = []
        self.max_workers = max_workers
        self.analyzed_mdl_data: Dict[str, Dict[str, List[str]]] = {}

        # Automatically load data when the analyzer is initialized
        self.load_pacer_data()

    @staticmethod
    def _normalize_item_attributes(item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalizes attribute names for consistency for in-memory items:
        - If 'Defendants' (plural) exists, its value is moved to 'Defendant' (singular)
          and 'Defendants' (plural) is removed.
        - If 'Defendant' (singular) does not exist after this check (meaning 'Defendants' also didn't exist),
          it's initialized as an empty list to ensure consistency for downstream parsing.
        """
        if 'Defendants' in item:
            item['Defendant'] = item['Defendants']
            del item['Defendants']
        elif 'Defendant' not in item:
            item['Defendant'] = []
        return item

    def _parallel_scan_worker(self, segment: int, total_segments: int) -> List[Dict[str, Any]]:
        """Worker to scan a single segment of the Pacer table and normalize items."""
        if not self.pacer_manager or not self.pacer_manager.table:
            return []
        try:
            segment_items_raw = list(self.pacer_manager.scan_table(
                Segment=segment,
                TotalSegments=total_segments
            ))
            # Normalize attributes immediately after scanning each item
            normalized_segment_items = [self._normalize_item_attributes(item) for item in segment_items_raw]
            return normalized_segment_items
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error scanning Pacer segment {segment}: {e}", exc_info=True)
            self.console.print(f"[red]Error in worker for segment {segment}: {e}[/red]")
            return []

    def load_pacer_data(self) -> None:
        """Loads data from the local Pacer table in parallel."""
        if not self.pacer_manager or not self.pacer_manager.table:
            self.console.print(
                "[bold red]PacerManager not available or table not initialized. Cannot load data.[/bold red]")
            return

        table_name = "PacerDockets (local)"
        if hasattr(self.pacer_manager, 'table_name') and self.pacer_manager.table_name:
            table_name = f"local Pacer table ('{self.pacer_manager.table_name}')"

        # Clear existing data before loading new
        self.pacer_items = []
        self.console.print(f"[cyan]Starting parallel scan of {table_name}...[/cyan]")
        start_time = time.time()

        total_segments = min(self.max_workers * 2, 50)

        with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                TextColumn("({task.completed} of {task.total} segments)"),
                TimeElapsedColumn(),
                console=self.console,
                transient=False
        ) as progress:
            scan_task = progress.add_task("Scanning Pacer table", total=total_segments)

            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = [executor.submit(self._parallel_scan_worker, i, total_segments)
                           for i in range(total_segments)]

                for future in concurrent.futures.as_completed(futures):
                    try:
                        segment_results = future.result()
                        if segment_results:
                            self.pacer_items.extend(segment_results)
                    except Exception as e:
                        if self.logger:
                            self.logger.error(f"A segment future completed with error: {e}", exc_info=True)
                        self.console.print(f"[red]Error processing a segment result: {e}[/red]")
                    progress.update(scan_task, advance=1)

        elapsed_time = time.time() - start_time
        if self.pacer_items:
            self.console.print(
                f"[green]Successfully loaded {len(self.pacer_items)} items from Pacer table in {elapsed_time:.2f} seconds.[/green]")
        else:
            self.console.print(
                f"[yellow]No items loaded from Pacer table (or scan failed). Elapsed: {elapsed_time:.2f}s.[/yellow]")

    def _parse_attribute_list(self, attribute_value: Any, item_context: str = "") -> List[str]:
        """
        Safely parses an attribute that might be a list or a string representation of a list.
        Returns a list of strings.
        """
        if attribute_value is None:
            return []
        if isinstance(attribute_value, list):
            return [str(x) for x in attribute_value if x is not None and str(x).strip()]

        if isinstance(attribute_value, str):
            val_str = attribute_value.strip()
            if not val_str:
                return []
            try:
                if val_str.startswith('[') and val_str.endswith(']'):
                    parsed_list = ast.literal_eval(val_str)
                    if isinstance(parsed_list, list):
                        return [str(x) for x in parsed_list if x is not None and str(x).strip()]
                    else:
                        if self.logger:
                            self.logger.warning(
                                f"Parsed attribute string '{val_str}' for {item_context} was not a list (type: {type(parsed_list)}). Treating as single element.")
                        return [val_str]
                else:
                    return [s.strip() for s in val_str.split(',') if s.strip()]
            except (ValueError, SyntaxError, TypeError) as e:
                if self.logger:
                    self.logger.warning(
                        f"Failed to parse attribute string '{val_str}' for {item_context}: {e}. Treating as single element.")
                return [val_str]

        str_val = str(attribute_value).strip()
        return [str_val] if str_val else []

    def analyze_mdl_details(self) -> None:
        """Analyzes loaded Pacer data for unique MDLs, Flags, and Defendant, prints as JSON, and saves to file."""
        if not self.pacer_items:
            output = {"type": "warning", "source": "analyze_mdl_details",
                      "message": "No Pacer data loaded. Please load data first."}
            print(json.dumps(output))
            return

        print(json.dumps({"type": "info", "source": "analyze_mdl_details", "message": "Analyzing MDL details..."}))
        raw_mdl_data: Dict[str, Dict[str, Set[str]]] = defaultdict(lambda: {"Flags": set(), "Defendant": set()})
        items_processed_for_mdl = 0

        for idx, item in enumerate(self.pacer_items):
            mdl_num_raw = item.get('MdlNum')
            mdl_num = str(mdl_num_raw).strip() if mdl_num_raw is not None else ""

            if not mdl_num or mdl_num.upper() == "NA":
                continue

            items_processed_for_mdl += 1
            item_context = f"item (PK: {item.get('FilingDate', 'N/A')}/{item.get('DocketNum', 'N/A')})"

            flags_val = item.get('Flags')
            defendants_val = item.get('Defendant')

            parsed_flags = self._parse_attribute_list(flags_val, f"Flags for {mdl_num} from {item_context}")
            parsed_defendants = self._parse_attribute_list(defendants_val,
                                                           f"Defendant for {mdl_num} from {item_context}")

            raw_mdl_data[mdl_num]["Flags"].update(parsed_flags)
            raw_mdl_data[mdl_num]["Defendant"].update(parsed_defendants)

        if not raw_mdl_data:
            output = {"type": "warning", "source": "analyze_mdl_details",
                      "message": "No valid MDL data found after processing items."}
            print(json.dumps(output))
            self.analyzed_mdl_data = {}
            return

        self.analyzed_mdl_data = {
            mdl: {
                "Flags": sorted(list(details["Flags"])),
                "Defendant": sorted(list(details["Defendant"]))
            }
            for mdl, details in raw_mdl_data.items()
        }

        output_data = {
            "type": "success",
            "source": "analyze_mdl_details",
            "message": f"Analysis complete. Found details for {len(self.analyzed_mdl_data)} unique MDL numbers from {items_processed_for_mdl} relevant items.",
            "unique_mdl_count": len(self.analyzed_mdl_data),
            "relevant_items_processed": items_processed_for_mdl,
            "analysis_results": self.analyzed_mdl_data
        }

        # Print to console
        print(json.dumps(output_data, indent=2))

        # Save to file
        output_path = Path("mdl_cleaning.json")
        try:
            with output_path.open('w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            print(json.dumps({
                "type": "info",
                "source": "analyze_mdl_details",
                "message": f"Successfully saved MDL analysis to {output_path.absolute()}"
            }))
        except Exception as e:
            print(json.dumps({
                "type": "error",
                "source": "analyze_mdl_details",
                "message": f"Failed to save MDL analysis to file: {str(e)}"
            }))

    def display_mdl_analysis(self) -> None:
        """Displays the analyzed MDL Flags and Defendants as JSON."""
        if not self.analyzed_mdl_data:
            print(json.dumps({"type": "warning", "source": "display_mdl_analysis",
                              "message": "No analyzed MDL data to display. Run analysis first."}))
            return

        print(json.dumps(self.analyzed_mdl_data, indent=2))

    def analyze_attribute_frequencies(self) -> None:
        """Analyzes and displays frequency of each attribute key present in the Pacer items."""
        if not self.pacer_items:
            console.print("[yellow]No Pacer data loaded. Please load data first (Option 1).[/yellow]")
            return

        console.print("[cyan]Analyzing attribute key frequencies...[/cyan]")

        attribute_key_frequencies: Dict[str, int] = defaultdict(int)
        items_processed = 0
        items_with_valid_attributes = 0

        for item in self.pacer_items:
            items_processed += 1
            has_valid_attribute = False
            for key, value in item.items():
                if value is not None:
                    if isinstance(value, str) and value.strip().upper() == "NA":
                        continue
                    attribute_key_frequencies[key] += 1
                    has_valid_attribute = True
            if has_valid_attribute:
                items_with_valid_attributes += 1

        if not attribute_key_frequencies:
            console.print("[yellow]No attributes found or all items had only NA/null/None values.[/yellow]")
            return

        console.print(
            f"[green]Attribute key frequency analysis complete. Processed {items_processed} items, found attributes in {items_with_valid_attributes} items.[/green]")
        console.rule("[bold blue]Frequency of Attribute Keys[/bold blue]")

        sorted_frequencies = sorted(attribute_key_frequencies.items(), key=lambda x: x[1], reverse=True)

        for attr_key, count in sorted_frequencies:
            console.print(f"  - '{attr_key}': {count}")
        console.print("\n")

    def analyze_docket_subfolder_frequencies(self) -> None:
        base_path = Path("data")
        year_month_day_folders = [p for p in base_path.iterdir() if
                                  p.is_dir() and len(p.name) == 8 and p.name.isdigit()]
        if not year_month_day_folders:
            self.console.print("[bold red]No YYYYMMDD folders found in 'data'.[/bold red]")
            return

        key_frequencies: Counter = Counter()
        all_json_items = []
        total_json_files_to_process = []

        for date_folder in sorted(year_month_day_folders, key=lambda p: p.name):
            dockets_folder = date_folder / "dockets"
            if not dockets_folder.exists() or not dockets_folder.is_dir():
                self.console.print(
                    f"[yellow]Dockets folder not found or is not a directory: {dockets_folder}, skipping.[/yellow]")
                continue

            current_folder_json_files = list(dockets_folder.glob("**/*.json"))
            if not current_folder_json_files:
                self.console.print(
                    f"[yellow]No JSON files found in {dockets_folder} or its subdirectories, skipping.[/yellow]")
                continue

            total_json_files_to_process.extend(current_folder_json_files)

        if not total_json_files_to_process:
            self.console.print(
                f"[bold yellow]No JSON files found in any 'data/YYYYMMDD/dockets' subdirectories.[/bold yellow]")
            return

        self.console.print(
            f"[cyan]Analyzing {len(total_json_files_to_process)} JSON files from all 'data/YYYYMMDD/dockets' subdirectories...[/cyan]")
        with Progress(console=self.console) as progress:
            task = progress.add_task("[green]Processing JSON files...[/green]", total=len(total_json_files_to_process))
            for json_file_path in total_json_files_to_process:
                try:
                    with open(json_file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if isinstance(data, list):
                            for item in data:
                                if isinstance(item, dict):
                                    all_json_items.append(item)
                                    for key in item.keys():
                                        key_frequencies[key] += 1
                        elif isinstance(data, dict):
                            all_json_items.append(data)
                            for key in data.keys():
                                key_frequencies[key] += 1
                except json.JSONDecodeError:
                    self.console.print(f"[bold red]Error decoding JSON from {json_file_path}[/bold red]")
                except Exception as e:
                    self.console.print(f"[bold red]Error processing file {json_file_path}: {e}[/bold red]")
                progress.update(task, advance=1)

        if not key_frequencies:
            self.console.print("[bold yellow]No keys found in any JSON files.[/bold yellow]")
            return

        table = Table(title="Key Frequencies in all 'data/YYYYMMDD/dockets' folders")
        table.add_column("Key", style="cyan")
        table.add_column("Frequency", style="magenta")

        for key, count in key_frequencies.most_common():
            table.add_row(key, str(count))
        self.console.print(table)

        while True:
            self.console.print("\nEnter a key to see items containing it, or 'q' to return to the main menu:")
            user_key_input = self.console.input("[bold green]Key > [/bold green]").strip()

            if user_key_input.lower() == 'q':
                break

            if not user_key_input:
                self.console.print("[bold yellow]Please enter a key.[/bold yellow]")
                continue

            found_items = False
            for item in all_json_items:
                if user_key_input in item:
                    self.console.print(json.dumps(item, indent=2, ensure_ascii=False))
                    found_items = True

            if not found_items:
                self.console.print(f"[bold yellow]No items found with key: '{user_key_input}'[/bold yellow]")

    ATTRIBUTES_TO_REMOVE = [
        "jantz", "greenberg", "attyna", "kim", "transferredoutcase-donotdocket",
        "ifppnd", "transo", "efile", "tof", "lg", "nprose", "ignore", "m/ifp",
        "lc02", "mcshain", "standard", "type-b", "(marx)", "consent_due",
        "nocnst", "appenteng", "refdsp", "ps-c", "pslc-dp", "pso", "(pdx)",
        "5div", "(jprx)", "cab", "194", "pab", "ene1", "green", "_wyrick-lc2",
        "(pvcx)", "xmdl", "jbl", "jjv", "jfa", "open_mj", "pend_consent",
        "stayed", "eis", "_krc", "mapj", "rar", "pmh", "9cca_remand",
        "intradist-transf", "schneider", "attyopen", "pro-se1", "case_info",
        "filingfeedue", "henderson", "ps4", "3m", "3m_prose", "bp_b3", "b3",
        "(ajrx)", "holleb_hotaling", "dcr", "ere", "file", "mapadmin", "ene",
        "greenville", "jms", "_rtc", "jydmd", "srn", "lr16.2_tr4"
    ]

    @staticmethod
    def _contains_any_keyword(text_list: List[str], keywords: List[str]) -> bool:
        """Checks if any string in text_list contains any of the keywords (case-insensitive)."""
        normalized_keywords = [k.lower() for k in keywords]
        for text in text_list:
            normalized_text = text.lower()
            if any(kw in normalized_text for kw in normalized_keywords):
                return True
        return False

    def filter_and_export_mdl_data(self) -> None:
        """
        Filters loaded Pacer data based on specific MDL, Flags, and Defendant criteria,
        then exports the matching items to a CSV file.
        """
        if not self.pacer_items:
            self.console.print("[yellow]No Pacer data loaded. Please load data first (Option 1).[/yellow]")
            return

        self.console.print("[cyan]Applying filters and preparing data for export...[/cyan]")
        matched_items = []

        MDL_3084_DEFENDANTS_KEYWORDS = [
            "Alphabet", "amazon.com", "amazoncom services", "Facebook", "Foshan",
            "GHI Insurance", "Gold Kernal", "Google", "Instagram",
            "JKL Insurance company", "DEF insurance company", "MParticle", "Maxlead",
            "meta", "rokt us corp", "samba tv", "siculus", "snap", "oasis space",
            "tiktok", "youtube"
        ]
        MDL_2570_DEFENDANTS_KEYWORDS = ["Bard"]
        MDL_3094_DEFENDANTS_KEYWORDS = ["caremark", "express scripts"]
        FLAGS_MDL_KEYWORDS = ["MDL 3026", "MDL 3037", "MDL-3074"]
        MDL_2738_DEFENDANTS_KEYWORDS = ["THE ESTATE OF DECEDENT GABRIELE HAMMA"]
        MDL_2753_DEFENDANTS_KEYWORDS = ["maquet"]

        with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                TextColumn("({task.completed} of {task.total} items)"),
                TimeElapsedColumn(),
                console=self.console,
                transient=False
        ) as progress:
            filter_task = progress.add_task("Filtering items", total=len(self.pacer_items))

            for item in self.pacer_items:
                mdl_num_raw = item.get('MdlNum')
                mdl_num = str(mdl_num_raw).strip().lower() if mdl_num_raw is not None else ""

                flags_value = item.get('Flags')
                defendants_value = item.get('Defendant')

                parsed_defendants = self._parse_attribute_list(defendants_value, "Defendant")
                parsed_flags = self._parse_attribute_list(flags_value, "Flags")

                matched = False
                if mdl_num == '3084' and self._contains_any_keyword(parsed_defendants, MDL_3084_DEFENDANTS_KEYWORDS):
                    matched = True
                elif mdl_num == '2570' and self._contains_any_keyword(parsed_defendants, MDL_2570_DEFENDANTS_KEYWORDS):
                    matched = True
                elif mdl_num == '3094' and self._contains_any_keyword(parsed_defendants, MDL_3094_DEFENDANTS_KEYWORDS):
                    matched = True
                elif self._contains_any_keyword(parsed_flags, FLAGS_MDL_KEYWORDS):
                    matched = True
                elif mdl_num == '2738' and self._contains_any_keyword(parsed_defendants, MDL_2738_DEFENDANTS_KEYWORDS):
                    matched = True
                elif mdl_num == '2753' and self._contains_any_keyword(parsed_defendants, MDL_2753_DEFENDANTS_KEYWORDS):
                    matched = True

                if matched:
                    matched_items.append(item)
                progress.update(filter_task, advance=1)

        if not matched_items:
            self.console.print("[yellow]No items matched the specified criteria.[/yellow]")
            return

        self.console.print(f"[green]Found {len(matched_items)} items matching the criteria.[/green]")

        all_keys = set()
        for item in matched_items:
            all_keys.update(item.keys())

        preferred_order = ["MdlNum", "FilingDate", "DocketNum", "Court", "CaseName", "Flags", "Defendant", "PacerUrl"]
        sorted_remaining_keys = sorted([k for k in all_keys if k not in preferred_order])
        final_header = [k for k in preferred_order if k in all_keys] + sorted_remaining_keys

        output_filename = Prompt.ask(
            "[bold green]Enter output CSV filename[/bold green]",
            default="filtered_pacer_data.csv",
            console=self.console
        )

        try:
            with open(output_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=final_header)
                writer.writeheader()
                for item in matched_items:
                    row_data = {key: str(item.get(key, '')) for key in final_header}
                    writer.writerow(row_data)
            self.console.print(
                f"[bold green]Successfully exported {len(matched_items)} items to '{output_filename}'[/bold green]")
        except Exception as e:
            self.console.print(f"[bold red]Error exporting to CSV: {e}[/bold red]")
            if self.logger:
                self.logger.error(f"Error exporting filtered data to CSV: {e}", exc_info=True)

    def normalize_database_defendants_attribute(self) -> None:
        """
        Scans the local DynamoDB table, renames 'Defendants' attribute to 'Defendant'
        if 'Defendants' exists, and removes the original 'Defendants' attribute.
        This modifies the database directly.
        """
        if not self.pacer_manager or not self.pacer_manager.table:
            self.console.print(
                "[bold red]PacerManager not available or table not initialized. Cannot normalize attributes in DB.[/bold red]")
            return

        self.console.print(
            "[cyan]Starting normalization of 'Defendants' attribute to 'Defendant' in local DynamoDB table...[/cyan]")

        table = self.pacer_manager.table
        normalized_items_count = 0
        total_items_scanned = 0

        pk_key = self.pacer_manager.pk_name
        sk_key = self.pacer_manager.sk_name

        if not pk_key or not sk_key:
            self.console.print(
                f"[bold red]Primary key names for '{self.pacer_manager.table_name}' not defined in PacerDocketsManager. Cannot normalize attributes.[/bold red]")
            if self.logger:
                self.logger.error(f"PK/SK names missing for table {self.pacer_manager.table_name}.")
            return

        try:
            with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                    TextColumn("[progress.completed] items processed"),
                    TimeElapsedColumn(),
                    console=self.console,
                    transient=False
            ) as progress:
                scan_task = progress.add_task("Scanning and normalizing PacerDockets", total=None)

                for item in self.pacer_manager.scan_table():
                    total_items_scanned += 1
                    progress.update(scan_task, advance=1, description=f"Processing item {total_items_scanned}")

                    if 'Defendants' in item:
                        pk_value = item.get(pk_key)
                        sk_value = item.get(sk_key)

                        if pk_value is None or sk_value is None:
                            self.console.print(
                                f"[yellow]Skipping item PK: {item.get(pk_key, 'N/A')}, SK: {item.get(sk_key, 'N/A')} for update: Primary key component(s) are missing or None.[/yellow]")
                            if self.logger:
                                self.logger.warning(f"Skipping update for item due to missing PK/SK components: {item}")
                            continue

                        defendants_value = item['Defendants']

                        parsed_defendants_value = self._parse_attribute_list(
                            defendants_value,
                            f"Defendants for PK: {pk_value}, SK: {sk_value}"
                        )

                        update_expression = "SET #def_singular = :def_val REMOVE #def_plural"
                        expression_attribute_names = {
                            "#def_singular": "Defendant",
                            "#def_plural": "Defendants"
                        }
                        expression_attribute_values = {
                            ":def_val": parsed_defendants_value
                        }

                        try:
                            self.pacer_manager.table.update_item(
                                Key={
                                    pk_key: pk_value,
                                    sk_key: sk_value
                                },
                                UpdateExpression=update_expression,
                                ExpressionAttributeValues=expression_attribute_values,
                                ExpressionAttributeNames=expression_attribute_names
                            )
                            normalized_items_count += 1
                            if self.logger:
                                self.logger.debug(
                                    f"Normalized 'Defendants' to 'Defendant' for item PK: {pk_value}, SK: {sk_value}")
                        except Exception as update_e:
                            self.console.print(
                                f"[bold red]Error updating item PK: {pk_value}, SK: {sk_value}: {update_e}[/bold red]")
                            if self.logger:
                                self.logger.error(f"Failed to update item PK: {pk_value}, SK: {sk_value}",
                                                  exc_info=True)

            self.console.print(
                f"[bold green]Normalization complete. Scanned {total_items_scanned} items. Normalized {normalized_items_count} items in the local DynamoDB table.[/bold green]")

        except Exception as scan_e:
            self.console.print(f"[bold red]Error during scan for normalization: {scan_e}[/bold red]")
            if self.logger:
                self.logger.error(f"Error during DynamoDB scan for normalization:", exc_info=True)

    def normalize_titles_in_database(self) -> None:
        """
        Scans the local DynamoDB table, finds titles starting with "IN RE: " (case-sensitive),
        and removes that prefix, updating the item directly in the database.
        """
        if not self.pacer_manager or not self.pacer_manager.table:
            self.console.print(
                "[bold red]PacerManager not available or table not initialized. Cannot normalize titles in DB.[/bold red]")
            return

        self.console.print("[cyan]Starting normalization of 'Title' attribute in local DynamoDB table...[/cyan]")

        table = self.pacer_manager.table
        normalized_titles_count = 0
        total_items_scanned = 0

        pk_key = self.pacer_manager.pk_name
        sk_key = self.pacer_manager.sk_name

        if not pk_key or not sk_key:
            self.console.print(
                f"[bold red]Primary key names for '{self.pacer_manager.table_name}' not defined in PacerDocketsManager. Cannot normalize titles.[/bold red]")
            if self.logger:
                self.logger.error(f"PK/SK names missing for table {self.pacer_manager.table_name}.")
            return

        prefix = "IN RE: "

        try:
            with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                    TextColumn("[progress.completed] items processed"),
                    TimeElapsedColumn(),
                    console=self.console,
                    transient=False
            ) as progress:
                scan_task = progress.add_task("Scanning and normalizing Titles", total=None)

                # Iterate through all items in the table
                for item in self.pacer_manager.scan_table():
                    total_items_scanned += 1
                    progress.update(scan_task, advance=1, description=f"Processing item {total_items_scanned}")

                    current_title = item.get('Title')

                    if isinstance(current_title, str) and current_title.startswith(prefix):
                        new_title = current_title[len(prefix):].strip()

                        pk_value = item.get(pk_key)
                        sk_value = item.get(sk_key)

                        if pk_value is None or sk_value is None:
                            self.console.print(
                                f"[yellow]Skipping item PK: {item.get(pk_key, 'N/A')}, SK: {item.get(sk_key, 'N/A')} for update: Primary key component(s) are missing or None.[/yellow]")
                            if self.logger:
                                self.logger.warning(f"Skipping update for item due to missing PK/SK components: {item}")
                            continue

                        update_expression = "SET #title_attr = :new_title"
                        expression_attribute_names = {"#title_attr": "Title"}
                        expression_attribute_values = {":new_title": new_title}

                        try:
                            self.pacer_manager.table.update_item(
                                Key={
                                    pk_key: pk_value,
                                    sk_key: sk_value
                                },
                                UpdateExpression=update_expression,
                                ExpressionAttributeNames=expression_attribute_names,
                                ExpressionAttributeValues=expression_attribute_values
                            )
                            normalized_titles_count += 1
                            if self.logger:
                                self.logger.debug(
                                    f"Normalized title for item PK: {pk_value}, SK: {sk_value} from '{current_title}' to '{new_title}'")
                        except Exception as update_e:
                            self.console.print(
                                f"[bold red]Error updating title for item PK: {pk_value}, SK: {sk_value}: {update_e}[/bold red]")
                            if self.logger:
                                self.logger.error(f"Failed to update title for item PK: {pk_value}, SK: {sk_value}",
                                                  exc_info=True)

            self.console.print(
                f"[bold green]Normalization complete. Scanned {total_items_scanned} items. Normalized titles for {normalized_titles_count} items in the local DynamoDB table.[/bold green]")

        except Exception as scan_e:
            self.console.print(f"[bold red]Error during scan for title normalization: {scan_e}[/bold red]")
            if self.logger:
                self.logger.error(f"Error during DynamoDB scan for title normalization:", exc_info=True)

    def interactive_loop(self) -> None:
        if not self.pacer_manager:
            self.console.print(
                "[bold red]PacerDocketsManager could not be initialized. Analyzer cannot run.[/bold red]")
            return

        self.console.print("[bold cyan]Pacer Local Analyzer Initialized (interactive loop).[/bold cyan]")
        while True:
            self.console.print("\n[bold cyan]Select an option:[/bold cyan]")
            self.console.print("1. Analyze Pacer Table (Local DynamoDB)")
            self.console.print("2. Analyze MDL Details from Pacer Table (Output as JSON)")
            self.console.print("3. Analyze Attribute Frequencies from Pacer Table")
            self.console.print("4. Analyze Docket Subfolder Frequencies")
            self.console.print("5. Clean JSON Attributes in Docket Subfolders")
            self.console.print("6. Filter and Export Pacer Data (CSV)")
            self.console.print("7. Normalize 'Defendants' attribute to 'Defendant' in DynamoDB")
            self.console.print("8. Normalize 'Title' attribute (remove 'IN RE: ') in DynamoDB")  # New option
            self.console.print("q. Quit")

            choice = Prompt.ask("Select an option", choices=["1", "2", "3", "4", "5", "6", "7", "8", "q", "Q"],
                                default="1",
                                console=self.console)

            if choice == "1":
                self.load_pacer_data()
            elif choice == "2":
                self.analyze_mdl_details()
            elif choice == "3":
                self.analyze_attribute_frequencies()
            elif choice == "4":
                self.analyze_docket_subfolder_frequencies()
            elif choice == "5":
                self.clean_json_attributes()
            elif choice == "6":
                self.filter_and_export_mdl_data()
            elif choice == "7":
                self.normalize_database_defendants_attribute()
            elif choice == "8":  # Call the new method
                self.normalize_titles_in_database()
            elif choice.lower() == "q":
                console.print("[bold]Exiting Pacer Local Analyzer.[/bold]")
                break
            else:
                console.print("[red]Invalid choice. Please try again.[/red]")

    def clean_json_attributes(self):
        """Iterates through all JSON files in data/YYYYMMDD/dockets, removes specified attributes, and saves the modified JSON."""
        attributes_to_remove = Prompt.ask(
            "Enter a comma-separated list of attributes to remove (e.g., jantz,greenberg)"
        ).split(',')
        attributes_to_remove = [attr.strip() for attr in attributes_to_remove if attr.strip()]

        if not attributes_to_remove:
            self.console.print("No attributes specified for removal.")
            return

        data_folder = Path("data")

        date_folders = sorted(
            [d for d in data_folder.iterdir() if d.is_dir() and len(d.name) == 8 and d.name.isdigit()])

        if not date_folders:
            self.console.print(f"No date-stamped subfolders found in '{data_folder}'.")
            return

        base_data_folder = Path("data")
        if not base_data_folder.exists() or not base_data_folder.is_dir():
            self.console.print(f"[bold red]Base data folder '{base_data_folder}' not found.[/bold red]")
            return

        year_month_day_folders = [p for p in base_data_folder.iterdir() if
                                  p.is_dir() and p.name.isdigit() and len(p.name) == 8]
        if not year_month_day_folders:
            self.console.print("[bold red]No YYYYMMDD folders found in 'data'.[/bold red]")
            return

        total_json_files_processed = 0
        total_attributes_removed = 0

        for date_folder in sorted(year_month_day_folders, key=lambda p: p.name):
            dockets_folder = date_folder / "dockets"
            if not dockets_folder.exists() or not dockets_folder.is_dir():
                self.console.print(
                    f"[yellow]Dockets folder not found or is not a directory: {dockets_folder}, skipping.[/yellow]")
                continue

            json_files_to_process = list(dockets_folder.glob("**/*.json"))
            if not json_files_to_process:
                self.console.print(
                    f"[yellow]No JSON files found in {dockets_folder} or its subdirectories, skipping.[/yellow]")
                continue

            self.console.print(f"[cyan]Cleaning JSON files in: {dockets_folder} and its subdirectories[/cyan]")
            with Progress(console=self.console) as progress:
                task = progress.add_task(f"[green]Cleaning files in {dockets_folder.name}...[/green]",
                                         total=len(json_files_to_process))
                for json_file_path in json_files_to_process:
                    attributes_removed_in_file = 0
                    try:
                        with open(json_file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        modified = False
                        if isinstance(data, list):
                            for item in data:
                                if isinstance(item, dict):
                                    for attr in attributes_to_remove:
                                        if attr in item:
                                            del item[attr]
                                            attributes_removed_in_file += 1
                                            modified = True
                        elif isinstance(data, dict):
                            for attr in attributes_to_remove:
                                if attr in data:
                                    del data[attr]
                                    attributes_removed_in_file += 1
                                    modified = True

                        if modified:
                            with open(json_file_path, 'w', encoding='utf-8') as f:
                                json.dump(data, f, indent=2, ensure_ascii=False)
                            total_attributes_removed += attributes_removed_in_file
                            total_json_files_processed += 1

                    except json.JSONDecodeError:
                        self.console.print(f"[bold red]Error decoding JSON from {json_file_path}[/bold red]")
                    except Exception as e:
                        self.console.print(f"[bold red]Error processing file {json_file_path}: {e}[/bold red]")
                    progress.update(task, advance=1)

        if hasattr(self, 'ATTRIBUTES_TO_REMOVE') and self.ATTRIBUTES_TO_REMOVE:
            self.console.print(
                f"[cyan]Also cleaning JSON files based on hardcoded attributes: {', '.join(self.ATTRIBUTES_TO_REMOVE)}[/cyan]")
            for date_folder in sorted(year_month_day_folders, key=lambda p: p.name):
                dockets_folder = date_folder / "dockets"
                if not dockets_folder.exists() or not dockets_folder.is_dir():
                    continue

                json_files_to_process = list(dockets_folder.glob("**/*.json"))
                if not json_files_to_process:
                    continue

                with Progress(console=self.console) as progress:
                    task = progress.add_task(f"[green]Applying static cleanup in {dockets_folder.name}...[/green]",
                                             total=len(json_files_to_process))
                    for json_file_path in json_files_to_process:
                        attributes_removed_in_file = 0
                        try:
                            with open(json_file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)

                            modified = False
                            if isinstance(data, list):
                                for item in data:
                                    if isinstance(item, dict):
                                        for attr in self.ATTRIBUTES_TO_REMOVE:
                                            if attr in item:
                                                del item[attr]
                                                attributes_removed_in_file += 1
                                                modified = True
                            elif isinstance(data, dict):
                                for attr in self.ATTRIBUTES_TO_REMOVE:
                                    if attr in data:
                                        del data[attr]
                                        attributes_removed_in_file += 1
                                        modified = True

                            if modified:
                                with open(json_file_path, 'w', encoding='utf-8') as f:
                                    json.dump(data, f, indent=2, ensure_ascii=False)
                                total_attributes_removed += attributes_removed_in_file
                                total_json_files_processed += 1

                        except json.JSONDecodeError:
                            self.console.print(f"[bold red]Error decoding JSON from {json_file_path}[/bold red]")
                        except Exception as e:
                            self.console.print(f"[bold red]Error processing file {json_file_path}: {e}[/bold red]")
                        progress.update(task, advance=1)

        self.console.print(f"[bold green]Finished cleaning JSON attributes.[/bold green]")
        self.console.print(f"Processed and saved changes in {total_json_files_processed} JSON files.")
        self.console.print(f"Total attributes removed: {total_attributes_removed}.")


if __name__ == "__main__":
    import logging

    # Configure basic logging; PacerManager might use this.
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                        handlers=[logging.StreamHandler(sys.stdout)])  # Ensure logs go to console for CLI

    # Silence overly verbose loggers if necessary
    logging.getLogger('boto3').setLevel(logging.WARNING)
    logging.getLogger('botocore').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

    console.print("[bold green]Initializing Pacer Local Analyzer...[/bold green]")  # Reverted to Rich
    analyzer = PacerLocalAnalyzer(max_workers=os.cpu_count() or 4)  # Default to CPU count or 4 workers
    analyzer.interactive_loop()
