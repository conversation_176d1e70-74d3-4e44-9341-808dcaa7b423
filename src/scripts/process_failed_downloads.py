#!/usr/bin/env python3
"""
Process Failed Downloads Script

This script processes failed downloads collected during PACER scraping. It loads docket files from
the data/YYYYMMDD/dockets directory, finds cases with "_processing_notes": "Download failed.",
and saves them to failed_downloads.json for reprocessing.

Usage:
    python -m src.scripts.process_failed_downloads --date YYYYMMDD
    python -m src.scripts.process_failed_downloads --log-level INFO --date YYYYMMDD

Arguments:
    --date: Date in YYYYMMDD format (e.g., 20240601 for June 1, 2024)
    --log-level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
"""

import logging
import os
import json
from datetime import datetime
import sys
import argparse
from pathlib import Path

# Add the project root to the Python path if running as a script
if __name__ == "__main__":
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.insert(0, project_root)

try:
    from rich.console import Console
    from rich.table import Table
    from rich.progress import track
    from rich import print as rprint
except ImportError:
    print("Rich library not found. Install with: pip install rich")
    sys.exit(1)

# Initialize Rich console
console = Console()

# Constants
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATA_PATH = os.path.join(PROJECT_ROOT, "data")


def setup_logging(log_level):
    """Set up logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def get_valid_date(date_input):
    """Validate date input and return the logs directory path."""
    if not date_input:
        console.print("[red]Date is required. Use --date YYYYMMDD format.[/red]")
        sys.exit(1)

    try:
        date_obj = datetime.strptime(date_input, "%Y%m%d")
        return os.path.join(DATA_PATH, date_obj.strftime("%Y%m%d"))
    except ValueError:
        console.print("[red]Invalid date. Please enter a valid date in YYYYMMDD format.[/red]")
        sys.exit(1)


def find_failed_downloads(dockets_directory):
    """Find all docket files with 'Download failed.' in _processing_notes."""
    failed_downloads = []
    
    if not os.path.exists(dockets_directory):
        console.print(f"[red]Dockets directory not found: {dockets_directory}[/red]")
        return failed_downloads
    
    console.print(f"[cyan]Scanning dockets directory: {dockets_directory}[/cyan]")
    
    json_files = [f for f in os.listdir(dockets_directory) if f.endswith('.json')]
    
    if not json_files:
        console.print("[yellow]No JSON files found in dockets directory.[/yellow]")
        return failed_downloads
    
    console.print(f"[cyan]Found {len(json_files)} JSON files to check...[/cyan]")
    
    for filename in track(json_files, description="Checking files"):
        file_path = os.path.join(dockets_directory, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check if this file has "Download failed." in _processing_notes
            processing_notes = data.get('_processing_notes', '')
            if 'Download failed.' in processing_notes:
                court_id = data.get('court_id')
                docket_num = data.get('docket_num')
                
                if court_id and docket_num:
                    failed_downloads.append({
                        'court_id': court_id,
                        'docket_num': docket_num,
                        'filename': filename,
                        '_processing_notes': processing_notes,
                        'filing_date': data.get('filing_date'),
                        'versus': data.get('versus')
                    })
                    logging.debug(f"Found failed download: {court_id}/{docket_num}")
                else:
                    logging.warning(f"File {filename} has 'Download failed.' but missing court_id or docket_num")
                    
        except json.JSONDecodeError:
            logging.error(f"Error reading JSON from {filename}. Skipping...")
        except Exception as e:
            logging.error(f"Unexpected error reading {filename}: {str(e)}")
    
    return failed_downloads


def main(args, config):
    """Main function to process failed downloads for a specific date."""
    date_path = get_valid_date(args.date)
    date_str = os.path.basename(date_path)  # YYYYMMDD
    console.print(f"[cyan]Processing failed downloads for date: {date_str}[/cyan]")
    
    dockets_directory = os.path.join(date_path, "dockets")
    logs_directory = os.path.join(date_path, "logs")
    
    if not os.path.isdir(dockets_directory):
        console.print(f"[red]Dockets directory not found: {dockets_directory}[/red]")
        sys.exit(1)
    
    # Ensure logs directory exists
    os.makedirs(logs_directory, exist_ok=True)
    
    console.print("[cyan]Searching for failed downloads...[/cyan]")
    failed_downloads = find_failed_downloads(dockets_directory)
    
    console.print(f"[green]Found {len(failed_downloads)} failed downloads[/green]")
    
    if not failed_downloads:
        console.print("[yellow]No failed downloads found. Exiting.[/yellow]")
        return
    
    # Display summary table
    table = Table(title=f"Failed Downloads Summary for {date_str}")
    table.add_column("Court ID", style="cyan")
    table.add_column("Docket Number", style="magenta")
    table.add_column("Versus", style="green")
    table.add_column("Filing Date", style="yellow")
    
    for item in failed_downloads[:10]:  # Show first 10
        table.add_row(
            item.get('court_id', 'N/A'),
            item.get('docket_num', 'N/A'),
            (item.get('versus', 'N/A')[:50] + '...' if len(str(item.get('versus', ''))) > 50 else item.get('versus', 'N/A')),
            item.get('filing_date', 'N/A')
        )
    
    if len(failed_downloads) > 10:
        table.add_row("...", "...", f"... and {len(failed_downloads) - 10} more", "...")
    
    console.print(table)
    
    # Save to failed_downloads.json
    output_filename = "failed_downloads.json"
    output_path = os.path.join(logs_directory, output_filename)
    
    # Create simplified list for processing (just court_id and docket_num)
    simplified_list = [
        {"court_id": item["court_id"], "docket_num": item["docket_num"]}
        for item in failed_downloads
    ]
    
    save_response = console.input(
        f"\n[yellow]Save the {len(simplified_list)} failed downloads to '{output_filename}'? (Y/N) [Y]:[/yellow] ").strip().upper() or "Y"
    
    if save_response != "N":
        with open(output_path, 'w') as f:
            json.dump(simplified_list, f, indent=4)
        console.print(f"[green]Failed downloads saved to {output_path}[/green]")
        console.print(f"[green]Saved {len(simplified_list)} cases for reprocessing.[/green]")
    else:
        console.print("[yellow]File save canceled.[/yellow]")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process failed downloads from PACER scraping")
    parser.add_argument("--date", required=True, help="Date in YYYYMMDD format")
    parser.add_argument("--log-level", default="INFO", help="Logging level")
    
    args = parser.parse_args()
    
    # Set up logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        from src.lib.config import load_config
        
        effective_date_for_config = args.date if args.date else datetime.now().strftime("%Y%m%d")
        try:
            config_date_mmddyy = datetime.strptime(effective_date_for_config, "%Y%m%d").strftime("%m/%d/%y")
        except ValueError:
            logger.critical(f"Invalid effective date for config: {effective_date_for_config}")
            sys.exit(1)
        
        config = load_config(end_date=config_date_mmddyy)
    except ImportError:
        logger.critical("Failed to import load_config. Ensure src.lib.config is correct.")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"Failed to load configuration: {e}")
        sys.exit(1)
    
    main(args, config)
