#!/usr/bin/env python3
"""
Test script for the reprocess_failed functionality
"""

import sys
import os
import json
from pathlib import Path

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import the function we want to test
from src.main import load_failed_downloads

def test_load_failed_downloads():
    """Test the load_failed_downloads function"""
    print("Testing load_failed_downloads function...")
    
    # Test data
    base_data_dir = "data"
    iso_date = "20250101"
    
    # Call the function
    failed_downloads = load_failed_downloads(base_data_dir, iso_date)
    
    print(f"Found {len(failed_downloads)} failed downloads:")
    for item in failed_downloads:
        print(f"  - {item['court_id']}/{item['docket_num']}")
    
    # Verify results
    expected_count = 2  # We created 2 files with "Download failed."
    if len(failed_downloads) == expected_count:
        print("✅ Test PASSED: Found expected number of failed downloads")
        
        # Check specific cases
        expected_cases = [
            {"court_id": "cacd", "docket_num": "2:25-cv-00001"},
            {"court_id": "nysd", "docket_num": "1:25-cv-00002"}
        ]
        
        for expected in expected_cases:
            found = any(
                item['court_id'] == expected['court_id'] and 
                item['docket_num'] == expected['docket_num']
                for item in failed_downloads
            )
            if found:
                print(f"✅ Found expected case: {expected['court_id']}/{expected['docket_num']}")
            else:
                print(f"❌ Missing expected case: {expected['court_id']}/{expected['docket_num']}")
                
    else:
        print(f"❌ Test FAILED: Expected {expected_count} failed downloads, got {len(failed_downloads)}")
    
    return failed_downloads

def test_yaml_config():
    """Test that the YAML config has the new flag"""
    print("\nTesting YAML configuration...")
    
    config_path = Path("config/scraper.yml")
    if config_path.exists():
        with open(config_path, 'r') as f:
            content = f.read()
        
        if "reprocess_failed:" in content:
            print("✅ Test PASSED: reprocess_failed flag found in scraper.yml")
        else:
            print("❌ Test FAILED: reprocess_failed flag not found in scraper.yml")
    else:
        print("❌ Test FAILED: config/scraper.yml not found")

if __name__ == "__main__":
    print("Running reprocess_failed functionality tests...\n")
    
    # Test the function
    failed_downloads = test_load_failed_downloads()
    
    # Test the config
    test_yaml_config()
    
    print("\nTest completed!")
    
    # Show what would be saved to failed_downloads.json
    if failed_downloads:
        print(f"\nThis would be saved to failed_downloads.json:")
        print(json.dumps(failed_downloads, indent=2))
